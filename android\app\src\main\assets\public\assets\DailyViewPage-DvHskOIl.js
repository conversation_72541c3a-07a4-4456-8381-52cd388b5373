import{u as F,m as q,j as e,B as w,k as A,C as k,i as B,l as C}from"./index-Cmt5neWh.js";import{M as P}from"./MainLayout-wyzz138D.js";import{V as J,A as S,a as D,b as I,c as H}from"./VisitRecommendations-4l1HIKdN.js";import{B as c}from"./badge-XkNoLG2o.js";import{D as Z}from"./download-BHlV_KY3.js";import"./input-BK13BBqa.js";import"./index-CB2fuKla.js";import"./index-C8JwcrUT.js";import"./chevron-down-I8tOk39n.js";import"./circle-check-big-DK6RP7UF.js";import"./clock-DhYcPjhn.js";const G=()=>{const T=F(),{getTodaysHouses:z,getVisitsByHouse:R,getDoorsByVisit:O,getAddressById:j,getProductsByDoor:V,getAddressesRequiringReturnVisits:M}=q(),b=z(),U=M(),o={};b.forEach(t=>{const l=j(t.addressId);if(l){const s=l.street;o[s]||(o[s]={addressId:t.addressId,houses:[]}),o[s].houses.push(t)}});const N=t=>R(t).flatMap(s=>O(s.id)),K=t=>{switch(t){case"N/A":return e.jsx(c,{variant:"outline",className:"bg-gray-100",children:"N/A"});case"Angetroffen → Termin":return e.jsx(c,{className:"bg-yellow-500",children:"Termin"});case"Angetroffen → Kein Interesse":return e.jsx(c,{variant:"destructive",children:"Kein Interesse"});case"Angetroffen → Sale":return e.jsx(c,{className:"bg-green-500",children:"Sale"});default:return e.jsx(c,{variant:"outline",children:"Unbekannt"})}},L=()=>{try{const l=new Date().toISOString().split("T")[0],s=b.flatMap(a=>N(a.id)),u=s.filter(a=>a.status==="Angetroffen → Sale").length,i=s.filter(a=>a.status==="Angetroffen → Termin").length,h=s.filter(a=>a.status==="Angetroffen → Kein Interesse").length,g=s.filter(a=>a.status==="N/A").length,r={datum:l,exportZeitpunkt:new Date().toISOString(),zusammenfassung:{gesamtHaeuser:b.length,gesamtTueren:s.length,sales:u,termine:i,keinInteresse:h,nichtAngetroffen:g},strassen:Object.entries(o).map(([a,{houses:y}])=>({strassenname:a,anzahlHaeuser:y.length,haeuser:y.map(x=>{const f=j(x.addressId),m=N(x.id);return{hausnummer:x.houseNumber,vollAdresse:f?`${f.street} ${x.houseNumber}, ${f.zipCode} ${f.city}`:"Unbekannte Adresse",typ:x.type,anzahlTueren:m.length,tueren:m.map(n=>{const v=V(n.id);return{name:n.name,stockwerk:n.floor||null,status:n.status,notizen:n.notes||null,anzahlProdukte:v.length,produkte:v.map(p=>({kategorie:p.category,typ:p.type,anzahl:p.quantity,notizen:p.notes||null}))}}),statistiken:{sales:m.filter(n=>n.status==="Angetroffen → Sale").length,termine:m.filter(n=>n.status==="Angetroffen → Termin").length,keinInteresse:m.filter(n=>n.status==="Angetroffen → Kein Interesse").length,nichtAngetroffen:m.filter(n=>n.status==="N/A").length}}})}))},$=JSON.stringify(r,null,2),E=new Blob([$],{type:"application/json"}),d=document.createElement("a");d.href=URL.createObjectURL(E),d.download=`tagesübersicht-${l}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(d.href)}catch(t){console.error("Fehler beim Download:",t),alert("Fehler beim Erstellen der Download-Datei. Bitte versuchen Sie es erneut.")}};return e.jsxs("div",{className:"w-full mobile-container mobile-spacing",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4 md:mb-6",children:[e.jsx("h2",{className:"text-xl md:text-2xl font-bold",children:"Tagesübersicht"}),e.jsx(w,{variant:"outline",size:"icon",onClick:L,className:"mobile-button h-12 w-12 touch-feedback","data-primary":"true",children:e.jsx(Z,{size:20})})]}),U.length>0&&e.jsx("div",{children:e.jsx(J,{})}),e.jsx("div",{children:Object.keys(o).length===0?e.jsxs(A,{className:"mobile-card w-full",children:[e.jsx(k,{className:"p-4 md:p-6",children:e.jsx(B,{className:"text-lg md:text-xl",children:"Heutige Besuche"})}),e.jsx(C,{className:"p-4 md:p-6",children:e.jsxs("div",{className:"text-center py-6 md:py-8",children:[e.jsx("p",{className:"text-muted-foreground mb-4 text-sm md:text-base",children:"Heute wurden noch keine Besuche erfasst"}),e.jsx(w,{onClick:()=>T("/"),className:"mobile-action-button bg-red-600 hover:bg-red-700 text-white touch-feedback","data-size":"large",children:"Neuen Besuch erfassen"})]})})]}):e.jsxs(A,{className:"mobile-card w-full",children:[e.jsx(k,{className:"p-4 md:p-6",children:e.jsx(B,{className:"text-lg md:text-xl",children:"Heutige Besuche"})}),e.jsx(C,{children:e.jsx(S,{type:"single",collapsible:!0,className:"w-full",children:Object.entries(o).map(([t,{addressId:l,houses:s}])=>{const u=j(l);return e.jsxs(D,{value:t,children:[e.jsx(I,{className:"hover:no-underline",children:e.jsxs("div",{className:"flex justify-between w-full pr-4",children:[e.jsx("span",{children:t}),e.jsxs(c,{variant:"outline",className:"ml-2",children:[s.length," ",s.length===1?"Haus":"Häuser"]})]})}),e.jsx(H,{children:e.jsx("div",{className:"space-y-2 pl-2",children:s.map(i=>{const h=N(i.id),g=h.filter(r=>r.status==="Angetroffen → Sale").length;return e.jsx(S,{type:"single",collapsible:!0,className:"w-full",children:e.jsxs(D,{value:i.id,children:[e.jsx(I,{className:"py-2 hover:no-underline",children:e.jsxs("div",{className:"flex justify-between w-full pr-4",children:[e.jsxs("span",{children:[u==null?void 0:u.street," ",i.houseNumber,i.type==="MFH"&&e.jsx("span",{className:"ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded",children:"MFH"})]}),g>0&&e.jsxs(c,{className:"bg-green-500 ml-2",children:[g," ",g===1?"Sale":"Sales"]})]})}),e.jsx(H,{children:e.jsx("div",{className:"pl-4 pt-2 space-y-2",children:h.length===0?e.jsx("p",{className:"text-sm text-muted-foreground",children:"Keine Türen erfasst"}):h.map(r=>e.jsxs("div",{className:"flex justify-between items-center text-sm border-b pb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:r.name}),r.floor&&e.jsxs("span",{className:"text-muted-foreground ml-2",children:["(",r.floor,")"]})]}),K(r.status)]},r.id))})})]})},i.id)})})})]},t)})})})]})})]})},le=()=>e.jsx(P,{title:"Tagesübersicht",children:e.jsx("div",{className:"w-full",children:e.jsx(G,{})})});export{le as default};
