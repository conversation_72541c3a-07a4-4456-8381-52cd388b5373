import{r as s,j as o,a as n,Q as d}from"./index-Cmt5neWh.js";import{P as m}from"./input-BK13BBqa.js";var f="Label",l=s.forwardRef((e,t)=>o.jsx(m.label,{...e,ref:t,onMouseDown:a=>{var r;a.target.closest("button, input, select, textarea")||((r=e.onMouseDown)==null||r.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));l.displayName=f;var i=l;const c=d("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=s.forwardRef(({className:e,...t},a)=>o.jsx(i,{ref:a,className:n(c(),e),...t}));u.displayName=i.displayName;export{u as L};
