import{r as n,j as c,F as I}from"./index-Cmt5neWh.js";import{c as O,u as S,e as D,P as m,a as M,d as F,g as L}from"./input-BK13BBqa.js";var g="Collapsible",[k,H]=O(g),[B,v]=k(g),T=n.forwardRef((e,s)=>{const{__scopeCollapsible:r,open:a,defaultOpen:t,disabled:l,onOpenChange:i,...f}=e,[p=!1,d]=S({prop:a,defaultProp:t,onChange:i});return c.jsx(B,{scope:r,disabled:l,contentId:D(),open:p,onOpenToggle:n.useCallback(()=>d(C=>!C),[d]),children:c.jsx(m.div,{"data-state":R(p),"data-disabled":l?"":void 0,...f,ref:s})})});T.displayName=g;var j="CollapsibleTrigger",w=n.forwardRef((e,s)=>{const{__scopeCollapsible:r,...a}=e,t=v(j,r);return c.jsx(m.button,{type:"button","aria-controls":t.contentId,"aria-expanded":t.open||!1,"data-state":R(t.open),"data-disabled":t.disabled?"":void 0,disabled:t.disabled,...a,ref:s,onClick:M(e.onClick,t.onOpenToggle)})});w.displayName=j;var x="CollapsibleContent",A=n.forwardRef((e,s)=>{const{forceMount:r,...a}=e,t=v(x,e.__scopeCollapsible);return c.jsx(F,{present:r||t.open,children:({present:l})=>c.jsx(G,{...a,ref:s,present:l})})});A.displayName=x;var G=n.forwardRef((e,s)=>{const{__scopeCollapsible:r,present:a,children:t,...l}=e,i=v(x,r),[f,p]=n.useState(a),d=n.useRef(null),C=I(s,d),h=n.useRef(0),y=h.current,P=n.useRef(0),N=P.current,b=i.open||f,E=n.useRef(b),u=n.useRef();return n.useEffect(()=>{const o=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(o)},[]),L(()=>{const o=d.current;if(o){u.current=u.current||{transitionDuration:o.style.transitionDuration,animationName:o.style.animationName},o.style.transitionDuration="0s",o.style.animationName="none";const _=o.getBoundingClientRect();h.current=_.height,P.current=_.width,E.current||(o.style.transitionDuration=u.current.transitionDuration,o.style.animationName=u.current.animationName),p(a)}},[i.open,a]),c.jsx(m.div,{"data-state":R(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!b,...l,ref:C,style:{"--radix-collapsible-content-height":y?`${y}px`:void 0,"--radix-collapsible-content-width":N?`${N}px`:void 0,...e.style},children:b&&t})});function R(e){return e?"open":"closed"}var z=T,J=w,K=A;export{w as C,z as R,J as T,A as a,K as b,H as c};
