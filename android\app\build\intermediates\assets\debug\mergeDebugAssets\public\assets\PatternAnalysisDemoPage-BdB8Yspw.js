import{c as H,m as Q,r as x,j as e,k as p,l as j,C as z,i as F,B as W,H as Me,T as Ve,J as de}from"./index-Cmt5neWh.js";import{t as me,z as Ce,w as O,a1 as Ne,l as Pe,m as X,a as J,T as Y,f as U,d as te,j as Ie,k as ye,C as xe,B as Ee,U as Re,M as He,e as Be}from"./MainLayout-wyzz138D.js";import{B as R}from"./badge-XkNoLG2o.js";import{P as ae}from"./progress-DeyBaPEi.js";import{u as ze,v as be,t as L}from"./useSwipeGestures-49dHBwDT.js";import{Z as ie}from"./zap-BNKcOEAu.js";import{C as _}from"./clock-DhYcPjhn.js";import{N as Fe}from"./navigation-B6ya6VuX.js";import{T as le}from"./target-Cbp8QShB.js";import{s as We,e as re}from"./subDays-BLJlWEqr.js";import{I as Ze,U as qe}from"./input-BK13BBqa.js";import{L as se}from"./label-NwAA2N0T.js";import{C as he}from"./circle-check-big-DK6RP7UF.js";import{S as Le}from"./search-BlRJyMtb.js";import{C as ve}from"./chart-column-TdCSSvzn.js";import{V as $e}from"./VisitRecommendations-4l1HIKdN.js";import{R as ue}from"./rotate-ccw-YT9XFzuk.js";import{T as Oe,a as Ue,b as Z,c as q}from"./tabs-BJh52NhZ.js";import"./index-CB2fuKla.js";import"./index-C8JwcrUT.js";import"./chevron-down-I8tOk39n.js";import"./download-BHlV_KY3.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=H("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=H("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=H("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=H("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=H("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=H("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=H("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=H("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=H("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);function Je(o,i){const m=me(o),g=me(i),l=pe(m,g),C=Math.abs(Ce(m,g));m.setDate(m.getDate()-l*C);const N=+(pe(m,g)===-l),b=l*(C-N);return b===0?0:b}function pe(o,i){const m=o.getFullYear()-i.getFullYear()||o.getMonth()-i.getMonth()||o.getDate()-i.getDate()||o.getHours()-i.getHours()||o.getMinutes()-i.getMinutes()||o.getSeconds()-i.getSeconds()||o.getMilliseconds()-i.getMilliseconds();return m<0?-1:m>0?1:m}function Ye(o,i,m){const g=O(o,m),l=O(i,m);return+g==+l}function _e(o,i){return Ye(o,Ne(o),i)}function je(o){return Pe(o,We(Ne(o),1))}const Qe=(o,i,m,g=new Date)=>{const l=m.filter(y=>y.addressId===o),C=i.filter(y=>l.some(v=>v.id===y.houseId));if(C.length<3)return[];const N=[],b=g.getDay();g.getHours();const u=C.filter(y=>new Date(y.timestamp).getDay()===b);if(u.length>=2){const y=u.filter(T=>T.status!=="N/A"),v=y.length/u.length;if(v>.5){const T=new Map;y.forEach(A=>{const f=new Date(A.timestamp).getHours(),M=Math.floor(f/2)*2,d=`${M}-${M+2}`;T.set(d,(T.get(d)||0)+1)}),Array.from(T.entries()).sort(([,A],[,f])=>f-A).slice(0,3).forEach(([A,f])=>{const[M]=A.split("-").map(Number),d=f/y.length;if(d>.3){const h=new Date(g);h.setHours(M,0,0,0);const P=new Date(h);P.setHours(M+2),N.push({startTime:h.toISOString(),endTime:P.toISOString(),probability:d,factors:[`Historical success rate: ${(v*100).toFixed(0)}%`,`Based on ${u.length} similar visits`,`${f} successful visits in this time window`]})}})}}return N.sort((y,v)=>v.probability-y.probability)},es=({className:o})=>{const{getAddressesRequiringReturnVisits:i,getHighPriorityRecommendations:m,visits:g,houses:l,addresses:C}=Q(),[N,b]=x.useState(0),[u,y]=x.useState({todayVisits:0,successRate:0,streak:0,weeklyGoal:25,achievements:[]}),[v,T]=x.useState(!1),A=x.useRef(null),f=i();m();const{ref:M,isSwipeActive:d}=ze({onSwipeLeft:()=>{N<f.length-1&&(b(t=>t+1),L("light"))},onSwipeRight:()=>{N>0&&(b(t=>t-1),L("light"))},threshold:50});x.useEffect(()=>{const t=new Date,a=g.filter(c=>X(new Date(c.timestamp))),r=a.filter(c=>c.status!=="N/A");let n=0;const D=new Date(t);for(let c=0;c<30&&g.filter(k=>new Date(k.timestamp).toDateString()===D.toDateString()).some(k=>k.status!=="N/A");c++)n++,D.setDate(D.getDate()-1);const w=[];n>=7&&w.push("🔥 Wochenserie"),r.length>=5&&w.push("⭐ Tagesstar"),a.length>=10&&w.push("💪 Fleißbiene"),y({todayVisits:a.length,successRate:a.length>0?r.length/a.length*100:0,streak:n,weeklyGoal:25,achievements:w})},[g]),x.useEffect(()=>{A.current&&A.current.querySelectorAll("button").forEach(a=>{be(a)||console.warn("Touch target too small:",a)})},[]);const h=f[N],P=h?Qe(h.address.id,g,l):[],E=async()=>{if(h){T(!0),L("medium");try{const t=h.address,a=`${t.street} ${t.zipCode} ${t.city}`;if("geolocation"in navigator){const r=`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(a)}`;window.open(r,"_blank")}}catch(t){console.error("Navigation error:",t),L("error")}finally{T(!1)}}},B=t=>t>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600",s=t=>t>=14?e.jsx(we,{className:"h-4 w-4 text-yellow-500"}):t>=7?e.jsx(ne,{className:"h-4 w-4 text-orange-500"}):t>=3?e.jsx(ke,{className:"h-4 w-4 text-blue-500"}):e.jsx(le,{className:"h-4 w-4 text-gray-500"});return f.length===0?e.jsx(p,{className:o,children:e.jsx(j,{className:"pt-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx($,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Perfekte Arbeit!"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"Alle Adressen wurden erfolgreich besucht. Keine Wiederholungsbesuche erforderlich."}),e.jsx("div",{className:"flex justify-center gap-2",children:u.achievements.map((t,a)=>e.jsx(R,{className:"bg-green-100 text-green-800",children:t},a))})]})})}):e.jsxs("div",{className:o,ref:A,children:[e.jsxs(p,{className:"mb-4",children:[e.jsx(z,{className:"pb-3",children:e.jsxs(F,{className:"flex items-center gap-2 text-lg",children:[e.jsx($,{className:"h-5 w-5 text-blue-500"}),"Ihr persönlicher Assistent"]})}),e.jsxs(j,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold",children:u.todayVisits}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Besuche heute"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:`text-2xl font-bold ${B(u.successRate)}`,children:[u.successRate.toFixed(0),"%"]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Erfolgsrate"})]})]}),e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[s(u.streak),e.jsxs("span",{className:"text-sm font-medium",children:[u.streak," Tage Serie"]})]}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Wochenziel: ",u.todayVisits,"/",u.weeklyGoal]})]}),e.jsx(ae,{value:u.todayVisits/u.weeklyGoal*100,className:"h-2 mb-3"}),u.achievements.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1",children:u.achievements.map((t,a)=>e.jsx(R,{variant:"outline",className:"text-xs",children:t},a))})]})]}),e.jsxs(p,{children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(F,{className:"flex items-center gap-2 text-lg",children:[e.jsx(ie,{className:"h-5 w-5 text-yellow-500"}),"Intelligente Empfehlungen"]}),e.jsxs(R,{variant:"outline",className:"bg-blue-50",children:[N+1," / ",f.length]})]})}),e.jsx(j,{children:h&&e.jsxs("div",{ref:M,className:`space-y-4 ${d?"transition-transform":""}`,children:[e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(J,{className:"h-5 w-5 text-blue-500 mt-0.5"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-semibold",children:h.address.street}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[h.address.zipCode," ",h.address.city]}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs(R,{variant:"destructive",className:"text-xs",children:[h.failedVisitCount," fehlgeschlagen"]}),e.jsxs(R,{className:"bg-blue-500 text-xs",children:[h.recommendations.length," Empfehlungen"]})]})]})]}),h.recommendations.length>0&&e.jsxs("div",{className:"p-3 border border-green-200 bg-green-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(Y,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"font-medium text-green-800",children:"Beste Empfehlung"}),e.jsxs(R,{className:"bg-green-600 text-xs",children:[Math.round(h.recommendations[0].confidence*100),"% Vertrauen"]})]}),e.jsx("p",{className:"text-sm text-green-700",children:h.recommendations[0].recommendation})]}),P.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("h5",{className:"font-medium flex items-center gap-2",children:[e.jsx(_,{className:"h-4 w-4 text-purple-500"}),"Optimale Zeitfenster heute"]}),P.slice(0,2).map((t,a)=>e.jsx("div",{className:"p-2 bg-purple-50 rounded border border-purple-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium",children:[U(new Date(t.startTime),"HH:mm",{locale:te})," -",U(new Date(t.endTime),"HH:mm",{locale:te})]}),e.jsxs(R,{className:"bg-purple-600 text-xs",children:[Math.round(t.probability*100),"% Chance"]})]})},a))]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(W,{variant:"outline",size:"sm",onClick:()=>b(t=>Math.max(0,t-1)),disabled:N===0,className:"flex-1 h-12",children:[e.jsx(Ie,{className:"h-4 w-4 mr-1"}),"Zurück"]}),e.jsxs(W,{onClick:E,disabled:v,className:"flex-1 h-12 bg-blue-600 hover:bg-blue-700",children:[e.jsx(Fe,{className:"h-4 w-4 mr-2"}),v?"Öffne...":"Navigation"]}),e.jsxs(W,{variant:"outline",size:"sm",onClick:()=>b(t=>Math.min(f.length-1,t+1)),disabled:N===f.length-1,className:"flex-1 h-12",children:["Weiter",e.jsx(ye,{className:"h-4 w-4 ml-1"})]})]}),e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-xs text-muted-foreground",children:"← Wischen für weitere Empfehlungen →"})})]})})]})]})},ss=({className:o})=>{const{visits:i,addresses:m,houses:g}=Q(),[l,C]=x.useState({todayVisits:0,todaySuccessful:0,weeklyVisits:0,weeklySuccessful:0,totalVisits:0,totalSuccessful:0,currentStreak:0,longestStreak:0,averageVisitsPerDay:0,successRate:0,level:1,experience:0,experienceToNext:100}),[N,b]=x.useState([]),[u,y]=x.useState([]),[v,T]=x.useState(!1);x.useEffect(()=>{const s=new Date;O(s,{weekStartsOn:1}),re(s,{weekStartsOn:1});const t=i.filter(S=>X(new Date(S.timestamp))),a=i.filter(S=>_e(new Date(S.timestamp),{weekStartsOn:1})),r=i.filter(S=>S.status!=="N/A"),n=t.filter(S=>S.status!=="N/A"),D=a.filter(S=>S.status!=="N/A");let w=0,c=0,I=0;const V=new Date(s),k=new Map;i.forEach(S=>{const G=U(new Date(S.timestamp),"yyyy-MM-dd"),oe=S.status!=="N/A";k.set(G,k.get(G)||oe)});for(let S=0;S<365;S++){const G=U(V,"yyyy-MM-dd");k.get(G)||!1?(I++,S===0&&(w=I),c=Math.max(c,I)):(S===0&&(w=0),I=0),V.setDate(V.getDate()-1)}const ee=r.length*10+w*5,ce=Math.floor(ee/100)+1,Se=ce*100-ee,Te=i.length>0?new Date(Math.min(...i.map(S=>new Date(S.timestamp).getTime()))):s,De=Math.max(1,Je(s,Te)+1),Ae=i.length/De;C({todayVisits:t.length,todaySuccessful:n.length,weeklyVisits:a.length,weeklySuccessful:D.length,totalVisits:i.length,totalSuccessful:r.length,currentStreak:w,longestStreak:c,averageVisitsPerDay:Ae,successRate:i.length>0?r.length/i.length*100:0,level:ce,experience:ee%100,experienceToNext:Se})},[i]);const A=x.useRef([]),f=x.useMemo(()=>[{id:"first-visit",title:"Erster Besuch",description:"Ersten Besuch erfolgreich abgeschlossen",icon:e.jsx(ke,{className:"h-6 w-6 text-yellow-500"}),getProgress:s=>Math.min(s.totalSuccessful,1),maxProgress:1,isUnlocked:s=>s.totalSuccessful>=1,rarity:"common",points:10},{id:"daily-goal",title:"Tagesziel",description:"5 erfolgreiche Besuche an einem Tag",icon:e.jsx(le,{className:"h-6 w-6 text-blue-500"}),getProgress:s=>Math.min(s.todaySuccessful,5),maxProgress:5,isUnlocked:s=>s.todaySuccessful>=5,rarity:"common",points:25},{id:"week-warrior",title:"Wochenkämpfer",description:"25 erfolgreiche Besuche in einer Woche",icon:e.jsx(we,{className:"h-6 w-6 text-purple-500"}),getProgress:s=>Math.min(s.weeklySuccessful,25),maxProgress:25,isUnlocked:s=>s.weeklySuccessful>=25,rarity:"rare",points:100},{id:"streak-master",title:"Serienkönig",description:"7 Tage in Folge erfolgreich",icon:e.jsx(ne,{className:"h-6 w-6 text-orange-500"}),getProgress:s=>Math.min(s.currentStreak,7),maxProgress:7,isUnlocked:s=>s.currentStreak>=7,rarity:"rare",points:150},{id:"century-club",title:"Jahrhundert-Club",description:"100 erfolgreiche Besuche insgesamt",icon:e.jsx(fe,{className:"h-6 w-6 text-gold-500"}),getProgress:s=>Math.min(s.totalSuccessful,100),maxProgress:100,isUnlocked:s=>s.totalSuccessful>=100,rarity:"epic",points:500},{id:"perfectionist",title:"Perfektionist",description:"95% Erfolgsrate bei mindestens 50 Besuchen",icon:e.jsx(ge,{className:"h-6 w-6 text-yellow-600"}),getProgress:s=>s.totalVisits>=50?Math.min(s.successRate,95):0,maxProgress:95,isUnlocked:s=>s.totalVisits>=50&&s.successRate>=95,rarity:"legendary",points:1e3},{id:"speed-demon",title:"Geschwindigkeitsdämon",description:"10 Besuche an einem Tag",icon:e.jsx(ie,{className:"h-6 w-6 text-yellow-400"}),getProgress:s=>Math.min(s.todayVisits,10),maxProgress:10,isUnlocked:s=>s.todayVisits>=10,rarity:"rare",points:200},{id:"marathon-runner",title:"Marathonläufer",description:"30 Tage Streak",icon:e.jsx(Ge,{className:"h-6 w-6 text-bronze-500"}),getProgress:s=>Math.min(s.currentStreak,30),maxProgress:30,isUnlocked:s=>s.currentStreak>=30,rarity:"legendary",points:2e3}],[]),M=x.useMemo(()=>f.map(s=>({id:s.id,title:s.title,description:s.description,icon:s.icon,progress:s.getProgress(l),maxProgress:s.maxProgress,unlocked:s.isUnlocked(l),rarity:s.rarity,points:s.points})),[f,l]),d=x.useCallback(()=>{const s=A.current,t=[];M.forEach(a=>{const r=s.find(n=>n.id===a.id);if(a.unlocked&&(!r||!r.unlocked)){const n={...a,unlockedAt:new Date().toISOString()};t.push(n)}}),t.length>0&&(y(a=>[...t,...a.slice(0,2)]),t.forEach(()=>L("success"))),A.current=M,b(M)},[M]);x.useEffect(()=>{d()},[d]);const h=x.useCallback(s=>{switch(s){case"common":return"bg-gray-100 text-gray-800 border-gray-300";case"rare":return"bg-blue-100 text-blue-800 border-blue-300";case"epic":return"bg-purple-100 text-purple-800 border-purple-300";case"legendary":return"bg-yellow-100 text-yellow-800 border-yellow-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},[]),P=x.useCallback(s=>s>=90?"text-green-600":s>=75?"text-blue-600":s>=60?"text-yellow-600":"text-red-600",[]),E=x.useMemo(()=>N.filter(s=>s.unlocked),[N]),B=x.useMemo(()=>E.reduce((s,t)=>s+t.points,0),[E]);return e.jsxs("div",{className:o,children:[e.jsxs(p,{className:"mb-4",children:[e.jsx(z,{className:"pb-3",children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-5 w-5 text-yellow-500"}),"Level ",l.level]})}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm",children:"Erfahrung"}),e.jsxs("span",{className:"text-sm font-medium",children:[l.experience,"/100 XP"]})]}),e.jsx(ae,{value:l.experience,className:"h-3"}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-muted-foreground",children:[e.jsxs("span",{children:[B," Punkte insgesamt"]}),e.jsxs("span",{children:[l.experienceToNext," XP bis Level ",l.level+1]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsx(p,{children:e.jsx(j,{className:"pt-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold",children:l.todayVisits}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Besuche heute"}),e.jsxs("div",{className:"text-xs text-green-600",children:[l.todaySuccessful," erfolgreich"]})]})})}),e.jsx(p,{children:e.jsx(j,{className:"pt-4",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:`text-2xl font-bold ${P(l.successRate)}`,children:[l.successRate.toFixed(0),"%"]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Erfolgsrate"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[l.totalSuccessful,"/",l.totalVisits]})]})})}),e.jsx(p,{children:e.jsx(j,{className:"pt-4",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-1",children:[e.jsx(ne,{className:"h-5 w-5 text-orange-500"}),e.jsx("span",{className:"text-2xl font-bold",children:l.currentStreak})]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Tage Serie"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Rekord: ",l.longestStreak]})]})})}),e.jsx(p,{children:e.jsx(j,{className:"pt-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold",children:l.averageVisitsPerDay.toFixed(1)}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Ø pro Tag"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[l.weeklyVisits," diese Woche"]})]})})})]}),u.length>0&&e.jsxs(p,{className:"mb-4",children:[e.jsx(z,{className:"pb-3",children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(Ke,{className:"h-5 w-5 text-green-500"}),"Neue Erfolge!"]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-2",children:u.map(s=>e.jsxs("div",{className:"flex items-center gap-3 p-2 bg-green-50 border border-green-200 rounded",children:[s.icon,e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-sm",children:s.title}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.description})]}),e.jsxs(R,{className:h(s.rarity),children:["+",s.points," XP"]})]},s.id))})})]}),e.jsxs(p,{children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-5 w-5 text-yellow-500"}),"Erfolge (",E.length,"/",N.length,")"]}),e.jsxs(W,{variant:"outline",size:"sm",onClick:()=>T(!v),children:[v?"Weniger":"Alle",e.jsx(ye,{className:`h-4 w-4 ml-1 transition-transform ${v?"rotate-90":""}`})]})]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-2",children:(v?N:N.slice(0,4)).map(s=>e.jsxs("div",{className:`flex items-center gap-3 p-3 rounded border ${s.unlocked?"bg-white border-gray-200":"bg-gray-50 border-gray-100 opacity-60"}`,children:[e.jsx("div",{className:s.unlocked?"":"grayscale",children:s.icon}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-sm",children:s.title}),e.jsx("p",{className:"text-xs text-muted-foreground mb-1",children:s.description}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ae,{value:s.progress/s.maxProgress*100,className:"h-1 flex-1"}),e.jsxs("span",{className:"text-xs text-muted-foreground",children:[s.progress,"/",s.maxProgress]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx(R,{className:h(s.rarity),children:s.rarity}),s.unlocked&&e.jsxs("div",{className:"text-xs text-green-600 mt-1",children:["+",s.points," XP"]})]})]},s.id))})})]})]})},ts=({className:o,onVisitComplete:i})=>{const{visits:m,houses:g,addresses:l,doors:C,products:N}=Q(),[b,u]=x.useState(""),[y,v]=x.useState("all"),[T,A]=x.useState("today"),f=x.useRef(null),d=(()=>{const t=new Date,a=m.filter(k=>X(new Date(k.timestamp))),r=m.filter(k=>je(new Date(k.timestamp))),n=O(t,{weekStartsOn:1}),D=re(t,{weekStartsOn:1}),w=m.filter(k=>{const K=new Date(k.timestamp);return K>=n&&K<=D}),c=a.filter(k=>k.status==="Angetroffen → Sale").length,I=a.filter(k=>k.status==="Angetroffen → Termin").length,V=a.filter(k=>k.status==="N/A").length;return{todayTotal:a.length,yesterdayTotal:r.length,weekTotal:w.length,successfulToday:c,appointmentsToday:I,notMetToday:V,successRate:a.length>0?c/a.length*100:0}})(),h=()=>{let t=m;if(T==="today")t=t.filter(a=>X(new Date(a.timestamp)));else if(T==="yesterday")t=t.filter(a=>je(new Date(a.timestamp)));else if(T==="week"){const a=O(new Date,{weekStartsOn:1}),r=re(new Date,{weekStartsOn:1});t=t.filter(n=>{const D=new Date(n.timestamp);return D>=a&&D<=r})}return y!=="all"&&(t=t.filter(a=>a.status===y)),b&&(t=t.filter(a=>{const r=g.find(w=>w.id===a.houseId),n=r?l.find(w=>w.id===r.addressId):null;return`${(n==null?void 0:n.street)||""} ${(r==null?void 0:r.houseNumber)||""} ${(n==null?void 0:n.city)||""}`.toLowerCase().includes(b.toLowerCase())})),t.sort((a,r)=>new Date(r.timestamp).getTime()-new Date(a.timestamp).getTime())},P=t=>{const a=m.find(c=>c.id===t);if(!a)return null;const r=g.find(c=>c.id===a.houseId),n=r?l.find(c=>c.id===r.addressId):null,D=C.filter(c=>c.visitId===t),w=D.flatMap(c=>N.filter(I=>I.doorId===c.id));return{visit:a,house:r,address:n,doors:D,products:w}};x.useEffect(()=>{f.current&&f.current.querySelectorAll("button").forEach(a=>{be(a)||console.warn("Touch target too small:",a)})},[]);const E=t=>{switch(t){case"Angetroffen → Sale":return"bg-green-100 text-green-800 border-green-200";case"Angetroffen → Termin":return"bg-blue-100 text-blue-800 border-blue-200";case"Angetroffen → Kein Interesse":return"bg-orange-100 text-orange-800 border-orange-200";case"N/A":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},B=t=>{switch(t){case"Angetroffen → Sale":return e.jsx(he,{className:"h-4 w-4"});case"Angetroffen → Termin":return e.jsx(xe,{className:"h-4 w-4"});case"Angetroffen → Kein Interesse":return e.jsx(Re,{className:"h-4 w-4"});case"N/A":return e.jsx(_,{className:"h-4 w-4"});default:return e.jsx(J,{className:"h-4 w-4"})}},s=h();return e.jsxs("div",{className:o,ref:f,children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsx(p,{className:"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(le,{className:"h-5 w-5 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Heute"})]}),e.jsx("div",{className:"text-2xl font-bold text-blue-900",children:d.todayTotal}),e.jsx("div",{className:"text-xs text-blue-700",children:d.yesterdayTotal>0&&e.jsxs("span",{className:d.todayTotal>=d.yesterdayTotal?"text-green-600":"text-red-600",children:[d.todayTotal>=d.yesterdayTotal?"↗":"↘",Math.abs(d.todayTotal-d.yesterdayTotal)," vs. gestern"]})})]})}),e.jsx(p,{className:"bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(he,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-sm font-medium text-green-800",children:"Sales"})]}),e.jsx("div",{className:"text-2xl font-bold text-green-900",children:d.successfulToday}),e.jsxs("div",{className:"text-xs text-green-700",children:[d.successRate.toFixed(1),"% Erfolgsrate"]})]})}),e.jsx(p,{className:"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200",children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(xe,{className:"h-5 w-5 text-orange-600"}),e.jsx("span",{className:"text-sm font-medium text-orange-800",children:"Termine"})]}),e.jsx("div",{className:"text-2xl font-bold text-orange-900",children:d.appointmentsToday}),e.jsx("div",{className:"text-xs text-orange-700",children:"Vereinbart"})]})}),e.jsx(p,{className:"bg-gradient-to-br from-red-50 to-red-100 border-red-200",children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(_,{className:"h-5 w-5 text-red-600"}),e.jsx("span",{className:"text-sm font-medium text-red-800",children:"Nicht angetroffen"})]}),e.jsx("div",{className:"text-2xl font-bold text-red-900",children:d.notMetToday}),e.jsx("div",{className:"text-xs text-red-700",children:"Versuche"})]})})]}),e.jsx(p,{className:"mb-6",children:e.jsx(j,{className:"pt-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(se,{htmlFor:"search",className:"text-sm font-medium mb-2 block",children:"Suche nach Adresse"}),e.jsxs("div",{className:"relative",children:[e.jsx(Le,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(Ze,{id:"search",type:"text",placeholder:"Straße, Hausnummer oder Stadt...",value:b,onChange:t=>u(t.target.value),className:"pl-10 h-12 text-base"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(se,{htmlFor:"timeframe",className:"text-sm font-medium mb-2 block",children:"Zeitraum"}),e.jsxs("select",{id:"timeframe",value:T,onChange:t=>A(t.target.value),className:"w-full h-12 px-3 border border-gray-200 rounded-lg text-base bg-white",children:[e.jsx("option",{value:"today",children:"Heute"}),e.jsx("option",{value:"yesterday",children:"Gestern"}),e.jsx("option",{value:"week",children:"Diese Woche"}),e.jsx("option",{value:"all",children:"Alle"})]})]}),e.jsxs("div",{children:[e.jsx(se,{htmlFor:"status",className:"text-sm font-medium mb-2 block",children:"Status"}),e.jsxs("select",{id:"status",value:y,onChange:t=>v(t.target.value),className:"w-full h-12 px-3 border border-gray-200 rounded-lg text-base bg-white",children:[e.jsx("option",{value:"all",children:"Alle Status"}),e.jsx("option",{value:"Angetroffen → Sale",children:"Sales"}),e.jsx("option",{value:"Angetroffen → Termin",children:"Termine"}),e.jsx("option",{value:"Angetroffen → Kein Interesse",children:"Kein Interesse"}),e.jsx("option",{value:"N/A",children:"Nicht angetroffen"})]})]})]})]})})}),e.jsxs(p,{children:[e.jsx(z,{className:"pb-3",children:e.jsxs(F,{className:"flex items-center gap-2 text-lg",children:[e.jsx(ve,{className:"h-5 w-5 text-purple-500"}),"Besuche (",s.length,")"]})}),e.jsx(j,{children:s.length===0?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(J,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),e.jsx("p",{className:"text-lg font-medium mb-2",children:"Keine Besuche gefunden"}),e.jsx("p",{className:"text-sm",children:b||y!=="all"||T!=="today"?"Versuchen Sie andere Filter oder Suchbegriffe":"Starten Sie Ihren ersten Besuch!"})]}):e.jsx("div",{className:"space-y-3",children:s.map(t=>{const a=P(t.id);if(!a)return null;const{house:r,address:n,doors:D,products:w}=a;return e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[(r==null?void 0:r.type)==="EFH"?e.jsx(Me,{className:"h-4 w-4 text-blue-500"}):e.jsx(Ee,{className:"h-4 w-4 text-purple-500"}),e.jsxs("span",{className:"font-medium text-lg",children:[n==null?void 0:n.street," ",r==null?void 0:r.houseNumber]})]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[n==null?void 0:n.zipCode," ",n==null?void 0:n.city]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:U(new Date(t.timestamp),"dd.MM.yyyy HH:mm",{locale:te})})]}),e.jsx(R,{className:`${E(t.status)} border`,children:e.jsxs("div",{className:"flex items-center gap-1",children:[B(t.status),e.jsx("span",{className:"text-xs font-medium",children:t.status==="N/A"?"Nicht angetroffen":t.status==="Angetroffen → Sale"?"Sale":t.status==="Angetroffen → Termin"?"Termin":"Kein Interesse"})]})})]}),D.length>0&&e.jsx("div",{className:"space-y-2",children:D.map(c=>{const I=w.filter(V=>V.doorId===c.id);return e.jsxs("div",{className:"bg-gray-50 rounded p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"font-medium text-sm",children:c.name}),e.jsx(R,{className:`${E(c.status)} border text-xs`,children:c.status==="N/A"?"Nicht angetroffen":c.status==="Angetroffen → Sale"?"Sale":c.status==="Angetroffen → Termin"?"Termin":"Kein Interesse"})]}),I.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-xs font-medium text-gray-700",children:"Produkte:"}),I.map(V=>e.jsxs("div",{className:"text-xs text-gray-600",children:[V.quantity,"x ",V.type," (",V.category,")"]},V.id))]}),c.comment&&e.jsxs("div",{className:"mt-2 text-xs text-gray-600",children:[e.jsx("span",{className:"font-medium",children:"Kommentar:"})," ",c.comment]})]},c.id)})}),t.comment&&e.jsxs("div",{className:"mt-3 p-2 bg-blue-50 rounded text-sm",children:[e.jsx("span",{className:"font-medium text-blue-800",children:"Notiz:"}),e.jsx("span",{className:"text-blue-700 ml-1",children:t.comment})]})]},t.id)})})})]})]})},as=()=>{const{addAddress:o,addHouse:i,addVisit:m,addDoor:g,getAddressesRequiringReturnVisits:l,getHighPriorityRecommendations:C,updateVisitPatterns:N}=Q(),[b,u]=x.useState(!1),[y,v]=x.useState(!1),T=async()=>{u(!0);try{const d=o({street:"Musterstraße",city:"Stuttgart",zipCode:"70173"}),h=o({street:"Beispielweg",city:"Stuttgart",zipCode:"70174"}),P=i({addressId:d.id,houseNumber:"42",type:"EFH",latitude:48.7758,longitude:9.1829}),E=i({addressId:h.id,houseNumber:"15",type:"MFH",latitude:48.7758,longitude:9.1829}),B=["2024-01-15T09:30:00Z","2024-01-16T11:00:00Z","2024-01-17T14:30:00Z","2024-01-18T10:15:00Z","2024-01-19T13:45:00Z"],s=["2024-01-20T18:30:00Z","2024-01-21T19:15:00Z"];for(const r of B){const n=m({houseId:P.id,timestamp:r});g({visitId:n.id,name:"Haupteingang",status:"N/A"})}for(const r of s){const n=m({houseId:P.id,timestamp:r});g({visitId:n.id,name:"Haupteingang",status:"Angetroffen → Sale"})}const t=["2024-01-15T19:30:00Z","2024-01-16T20:00:00Z","2024-01-17T18:45:00Z"],a=["2024-01-20T14:30:00Z","2024-01-21T15:15:00Z"];for(const r of t){const n=m({houseId:E.id,timestamp:r});g({visitId:n.id,name:"Wohnung 1",status:"N/A"})}for(const r of a){const n=m({houseId:E.id,timestamp:r});g({visitId:n.id,name:"Wohnung 1",status:"Angetroffen → Termin"})}N(d.id),N(h.id),v(!0),de.success("Demo-Daten erfolgreich erstellt! Schauen Sie sich die Empfehlungen in der Tagesübersicht an.")}catch(d){console.error("Error generating demo data:",d),de.error("Fehler beim Erstellen der Demo-Daten")}finally{u(!1)}},A=()=>{window.location.reload()},f=l(),M=C();return e.jsxs(p,{className:"w-full",children:[e.jsx(z,{children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"Intelligente Besuchsoptimierung - Demo"]})}),e.jsxs(j,{className:"space-y-4",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Diese Demo zeigt die intelligente Zeitstempel-basierte Besuchsoptimierung. Das System analysiert fehlgeschlagene Besuche und generiert datenbasierte Empfehlungen."}),y?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-green-800 mb-2",children:"Demo erfolgreich erstellt!"}),e.jsx("p",{className:"text-sm text-green-700",children:"Die Demo-Daten wurden erstellt und Muster analysiert. Schauen Sie sich die Empfehlungen in der Tagesübersicht an."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx(p,{children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ve,{className:"h-4 w-4 text-red-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Adressen mit Empfehlungen"})]}),e.jsx("div",{className:"text-2xl font-bold mt-1",children:f.length})]})}),e.jsx(p,{children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4 text-blue-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Hohe Priorität"})]}),e.jsx("div",{className:"text-2xl font-bold mt-1",children:M.length})]})}),e.jsx(p,{children:e.jsxs(j,{className:"pt-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Muster erkannt"})]}),e.jsx("div",{className:"text-2xl font-bold mt-1",children:f.reduce((d,h)=>d+h.recommendations.length,0)})]})})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs(W,{variant:"outline",onClick:A,className:"flex-1",children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Demo zurücksetzen"]})})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-blue-800 mb-2",children:"Was wird demonstriert:"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:'• Automatische Zeitstempel-Erfassung bei "nicht angetroffen"'}),e.jsx("li",{children:"• Mustererkennung (Arbeitszeiten, Wochenend-Präferenzen)"}),e.jsx("li",{children:"• Intelligente Besuchsempfehlungen mit Konfidenz-Scores"}),e.jsx("li",{children:"• Priorisierung basierend auf Datenqualität"})]})]}),e.jsx(W,{onClick:T,disabled:b,className:"w-full",size:"lg",children:b?e.jsxs(e.Fragment,{children:[e.jsx(ue,{className:"h-4 w-4 mr-2 animate-spin"}),"Erstelle Demo-Daten..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{className:"h-4 w-4 mr-2"}),"Demo starten"]})})]}),e.jsxs("div",{className:"text-xs text-muted-foreground border-t pt-4",children:[e.jsx("strong",{children:"Hinweis:"})," Diese Demo erstellt Beispieldaten zur Demonstration der Funktionalität. In der Produktionsumgebung werden echte Besuchsdaten analysiert."]})]})]})},Ts=()=>{const[o,i]=x.useState("assistant");return e.jsx(He,{title:"Smart Visit Assistant",children:e.jsxs("div",{className:"w-full space-y-6 p-4",children:[e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx($,{className:"h-8 w-8 text-blue-500"}),e.jsx("h1",{className:"text-3xl font-bold",children:"Smart Visit Assistant"})]}),e.jsx("p",{className:"text-lg text-muted-foreground max-w-3xl mx-auto",children:"Ihr intelligenter Begleiter für optimierte Besuche. KI-gestützte Empfehlungen, detaillierte Übersichten und personalisierte Insights für maximalen Erfolg."})]}),e.jsxs(Oe,{value:o,onValueChange:i,className:"w-full",children:[e.jsxs(Ue,{className:"grid w-full grid-cols-5",children:[e.jsxs(Z,{value:"assistant",className:"flex items-center gap-2",children:[e.jsx($,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Assistent"})]}),e.jsxs(Z,{value:"dashboard",className:"flex items-center gap-2",children:[e.jsx(qe,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Dashboard"})]}),e.jsxs(Z,{value:"tracker",className:"flex items-center gap-2",children:[e.jsx(ve,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Übersicht"})]}),e.jsxs(Z,{value:"insights",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Insights"})]}),e.jsxs(Z,{value:"demo",className:"flex items-center gap-2",children:[e.jsx(Be,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Demo"})]})]}),e.jsx(q,{value:"assistant",className:"mt-6",children:e.jsx(es,{})}),e.jsx(q,{value:"dashboard",className:"mt-6",children:e.jsx(ss,{})}),e.jsx(q,{value:"tracker",className:"mt-6",children:e.jsx(ts,{})}),e.jsx(q,{value:"insights",className:"mt-6",children:e.jsx($e,{})}),e.jsx(q,{value:"demo",className:"mt-6",children:e.jsx(as,{})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6",children:[e.jsx(p,{className:"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:e.jsxs(j,{className:"pt-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx($,{className:"h-6 w-6 text-blue-600"}),e.jsx("span",{className:"font-semibold text-blue-800",children:"KI-Empfehlungen"})]}),e.jsx("p",{className:"text-sm text-blue-700",children:"Intelligente Zeitfenster-Vorhersagen basierend auf historischen Mustern und Erfolgsraten"})]})}),e.jsx(p,{className:"bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:e.jsxs(j,{className:"pt-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(J,{className:"h-6 w-6 text-green-600"}),e.jsx("span",{className:"font-semibold text-green-800",children:"GPS-Tracking"})]}),e.jsx("p",{className:"text-sm text-green-700",children:"Automatische Besuchserfassung und Navigation mit präziser Standorterkennung"})]})}),e.jsx(p,{className:"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",children:e.jsxs(j,{className:"pt-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(ie,{className:"h-6 w-6 text-purple-600"}),e.jsx("span",{className:"font-semibold text-purple-800",children:"Gamification"})]}),e.jsx("p",{className:"text-sm text-purple-700",children:"Erfolge, Streaks und Levelaufstieg für motivierende Leistungssteigerung"})]})})]})]})})};export{Ts as default};
