import{r as o}from"./index-Cmt5neWh.js";const X=a=>{const{onSwipeLeft:n,onSwipeRight:s,onSwipeUp:u,onSwipeDown:i,threshold:f=50,preventScroll:d=!1}=a,r=o.useRef(null),c=o.useRef(null),[p,l]=o.useState(!1),m=t=>{const e=t.touches[0];e&&(r.current={x:e.clientX,y:e.clientY,time:Date.now()},c.current=null,l(!0))},g=t=>{if(!r.current)return;const e=t.touches[0];if(e&&(c.current={x:e.clientX,y:e.clientY,time:Date.now()},d)){const h=Math.abs(e.clientX-r.current.x),v=Math.abs(e.clientY-r.current.y);h>v&&h>10&&t.preventDefault()}},E=()=>{if(!r.current||!c.current){l(!1);return}const t=c.current.x-r.current.x,e=c.current.y-r.current.y,h=c.current.time-r.current.time,v=Math.sqrt(t*t+e*e),x=v/h;if(v<f||x<.1){l(!1);return}const y=Math.abs(t),M=Math.abs(e);y>M?t>0?s==null||s():n==null||n():e>0?i==null||i():u==null||u(),l(!1)},b=o.useRef(null);return o.useEffect(()=>{const t=b.current;if(t)return t.addEventListener("touchstart",m,{passive:!1}),t.addEventListener("touchmove",g,{passive:!1}),t.addEventListener("touchend",E,{passive:!0}),()=>{t.removeEventListener("touchstart",m),t.removeEventListener("touchmove",g),t.removeEventListener("touchend",E)}},[n,s,u,i,f,d]),{ref:b,isSwipeActive:p}},Y=(a="light")=>{if("vibrate"in navigator){const n={light:[10],medium:[20],heavy:[30],success:[10,5,10],warning:[20,10,20,10,20],error:[50,20,50]};navigator.vibrate(n[a])}},L=a=>{const n=a.getBoundingClientRect(),s=44;return n.width>=s&&n.height>=s};export{Y as t,X as u,L as v};
