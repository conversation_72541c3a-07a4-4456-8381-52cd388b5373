import{q as $,s as p,p as Ke,r as Y,b as et,m as tt,j as l,k as ue,C as fe,i as de,l as pe,t as Oe}from"./index-Cmt5neWh.js";import{b as rt,M as je,f as nt,B as at,U as it,T as ot,d as st}from"./MainLayout-wyzz138D.js";import{f as P,_ as Fe,b as Ve,c as lt,d as ct,i as D,e as ye,p as T,g as w,h as ze,j as be,k as We,D as ut,l as ft,m as W,n as K,S as dt,A as pt,o as mt,q as ke,r as ht,s as F,t as vt,G as gt,u as yt,v as bt,w as ee,x as we,y as te,z as xt,E as Ue,F as At,H as Pt,R as _e,T as Se,L as Te,B as Ot,C as jt,X as kt,Y as wt,a as _t}from"./BarChart-DJI4ZcuR.js";import"./input-BK13BBqa.js";var St=["points","className","baseLinePoints","connectNulls"];function V(){return V=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},V.apply(this,arguments)}function Tt(r,e){if(r==null)return{};var a=Rt(r,e),t,n;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);for(n=0;n<o.length;n++)t=o[n],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(r,t)&&(a[t]=r[t])}return a}function Rt(r,e){if(r==null)return{};var a={};for(var t in r)if(Object.prototype.hasOwnProperty.call(r,t)){if(e.indexOf(t)>=0)continue;a[t]=r[t]}return a}function Re(r){return It(r)||Nt(r)||$t(r)||Et()}function Et(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $t(r,e){if(r){if(typeof r=="string")return me(r,e);var a=Object.prototype.toString.call(r).slice(8,-1);if(a==="Object"&&r.constructor&&(a=r.constructor.name),a==="Map"||a==="Set")return Array.from(r);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return me(r,e)}}function Nt(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function It(r){if(Array.isArray(r))return me(r)}function me(r,e){(e==null||e>r.length)&&(e=r.length);for(var a=0,t=new Array(e);a<e;a++)t[a]=r[a];return t}var Ee=function(e){return e&&e.x===+e.x&&e.y===+e.y},Lt=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=[[]];return e.forEach(function(t){Ee(t)?a[a.length-1].push(t):a[a.length-1].length>0&&a.push([])}),Ee(e[0])&&a[a.length-1].push(e[0]),a[a.length-1].length<=0&&(a=a.slice(0,-1)),a},Z=function(e,a){var t=Lt(e);a&&(t=[t.reduce(function(o,i){return[].concat(Re(o),Re(i))},[])]);var n=t.map(function(o){return o.reduce(function(i,c,f){return"".concat(i).concat(f===0?"M":"L").concat(c.x,",").concat(c.y)},"")}).join("");return t.length===1?"".concat(n,"Z"):n},Dt=function(e,a,t){var n=Z(e,t);return"".concat(n.slice(-1)==="Z"?n.slice(0,-1):n,"L").concat(Z(a.reverse(),t).slice(1))},Ct=function(e){var a=e.points,t=e.className,n=e.baseLinePoints,o=e.connectNulls,i=Tt(e,St);if(!a||!a.length)return null;var c=$("recharts-polygon",t);if(n&&n.length){var f=i.stroke&&i.stroke!=="none",s=Dt(a,n,o);return p.createElement("g",{className:c},p.createElement("path",V({},P(i,!0),{fill:s.slice(-1)==="Z"?i.fill:"none",stroke:"none",d:s})),f?p.createElement("path",V({},P(i,!0),{fill:"none",d:Z(a,o)})):null,f?p.createElement("path",V({},P(i,!0),{fill:"none",d:Z(n,o)})):null)}var m=Z(a,o);return p.createElement("path",V({},P(i,!0),{fill:m.slice(-1)==="Z"?i.fill:"none",className:c,d:m}))},Mt=Fe,Bt=lt,Kt=Ve;function Ft(r,e){return r&&r.length?Mt(r,Kt(e),Bt):void 0}var Vt=Ft;const zt=Ke(Vt);var Wt=Fe,Ut=Ve,Gt=ct;function Ht(r,e){return r&&r.length?Wt(r,Ut(e),Gt):void 0}var qt=Ht;const Zt=Ke(qt);var Xt=["cx","cy","angle","ticks","axisLine"],Yt=["ticks","tick","angle","tickFormatter","stroke"];function U(r){"@babel/helpers - typeof";return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},U(r)}function X(){return X=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},X.apply(this,arguments)}function $e(r,e){var a=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),a.push.apply(a,t)}return a}function I(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?$e(Object(a),!0).forEach(function(t){oe(r,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):$e(Object(a)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(a,t))})}return r}function Ne(r,e){if(r==null)return{};var a=Jt(r,e),t,n;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);for(n=0;n<o.length;n++)t=o[n],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(r,t)&&(a[t]=r[t])}return a}function Jt(r,e){if(r==null)return{};var a={};for(var t in r)if(Object.prototype.hasOwnProperty.call(r,t)){if(e.indexOf(t)>=0)continue;a[t]=r[t]}return a}function Qt(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Ie(r,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,He(t.key),t)}}function er(r,e,a){return e&&Ie(r.prototype,e),a&&Ie(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r}function tr(r,e,a){return e=ne(e),rr(r,Ge()?Reflect.construct(e,a||[],ne(r).constructor):e.apply(r,a))}function rr(r,e){if(e&&(U(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return nr(r)}function nr(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function Ge(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ge=function(){return!!r})()}function ne(r){return ne=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},ne(r)}function ar(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&he(r,e)}function he(r,e){return he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},he(r,e)}function oe(r,e,a){return e=He(e),e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}function He(r){var e=ir(r,"string");return U(e)=="symbol"?e:e+""}function ir(r,e){if(U(r)!="object"||!r)return r;var a=r[Symbol.toPrimitive];if(a!==void 0){var t=a.call(r,e||"default");if(U(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var se=function(r){function e(){return Qt(this,e),tr(this,e,arguments)}return ar(e,r),er(e,[{key:"getTickValueCoord",value:function(t){var n=t.coordinate,o=this.props,i=o.angle,c=o.cx,f=o.cy;return T(c,f,n,i)}},{key:"getTickTextAnchor",value:function(){var t=this.props.orientation,n;switch(t){case"left":n="end";break;case"right":n="start";break;default:n="middle";break}return n}},{key:"getViewBox",value:function(){var t=this.props,n=t.cx,o=t.cy,i=t.angle,c=t.ticks,f=zt(c,function(m){return m.coordinate||0}),s=Zt(c,function(m){return m.coordinate||0});return{cx:n,cy:o,startAngle:i,endAngle:i,innerRadius:s.coordinate||0,outerRadius:f.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,n=t.cx,o=t.cy,i=t.angle,c=t.ticks,f=t.axisLine,s=Ne(t,Xt),m=c.reduce(function(h,u){return[Math.min(h[0],u.coordinate),Math.max(h[1],u.coordinate)]},[1/0,-1/0]),v=T(n,o,m[0],i),d=T(n,o,m[1],i),y=I(I(I({},P(s,!1)),{},{fill:"none"},P(f,!1)),{},{x1:v.x,y1:v.y,x2:d.x,y2:d.y});return p.createElement("line",X({className:"recharts-polar-radius-axis-line"},y))}},{key:"renderTicks",value:function(){var t=this,n=this.props,o=n.ticks,i=n.tick,c=n.angle,f=n.tickFormatter,s=n.stroke,m=Ne(n,Yt),v=this.getTickTextAnchor(),d=P(m,!1),y=P(i,!1),h=o.map(function(u,b){var x=t.getTickValueCoord(u),A=I(I(I(I({textAnchor:v,transform:"rotate(".concat(90-c,", ").concat(x.x,", ").concat(x.y,")")},d),{},{stroke:"none",fill:s},y),{},{index:b},x),{},{payload:u});return p.createElement(w,X({className:$("recharts-polar-radius-axis-tick",ze(i)),key:"tick-".concat(u.coordinate)},be(t.props,u,b)),e.renderTickItem(i,A,f?f(u.value,b):u.value))});return p.createElement(w,{className:"recharts-polar-radius-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,n=t.ticks,o=t.axisLine,i=t.tick;return!n||!n.length?null:p.createElement(w,{className:$("recharts-polar-radius-axis",this.props.className)},o&&this.renderAxisLine(),i&&this.renderTicks(),We.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(t,n,o){var i;return p.isValidElement(t)?i=p.cloneElement(t,n):D(t)?i=t(n):i=p.createElement(ye,X({},n,{className:"recharts-polar-radius-axis-tick-value"}),o),i}}])}(Y.PureComponent);oe(se,"displayName","PolarRadiusAxis");oe(se,"axisType","radiusAxis");oe(se,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function G(r){"@babel/helpers - typeof";return G=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(r)}function C(){return C=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},C.apply(this,arguments)}function Le(r,e){var a=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),a.push.apply(a,t)}return a}function L(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?Le(Object(a),!0).forEach(function(t){le(r,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):Le(Object(a)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(a,t))})}return r}function or(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function De(r,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,Ze(t.key),t)}}function sr(r,e,a){return e&&De(r.prototype,e),a&&De(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r}function lr(r,e,a){return e=ae(e),cr(r,qe()?Reflect.construct(e,a||[],ae(r).constructor):e.apply(r,a))}function cr(r,e){if(e&&(G(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ur(r)}function ur(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function qe(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qe=function(){return!!r})()}function ae(r){return ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},ae(r)}function fr(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&ve(r,e)}function ve(r,e){return ve=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},ve(r,e)}function le(r,e,a){return e=Ze(e),e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}function Ze(r){var e=dr(r,"string");return G(e)=="symbol"?e:e+""}function dr(r,e){if(G(r)!="object"||!r)return r;var a=r[Symbol.toPrimitive];if(a!==void 0){var t=a.call(r,e||"default");if(G(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var pr=Math.PI/180,Ce=1e-5,ce=function(r){function e(){return or(this,e),lr(this,e,arguments)}return fr(e,r),sr(e,[{key:"getTickLineCoord",value:function(t){var n=this.props,o=n.cx,i=n.cy,c=n.radius,f=n.orientation,s=n.tickSize,m=s||8,v=T(o,i,c,t.coordinate),d=T(o,i,c+(f==="inner"?-1:1)*m,t.coordinate);return{x1:v.x,y1:v.y,x2:d.x,y2:d.y}}},{key:"getTickTextAnchor",value:function(t){var n=this.props.orientation,o=Math.cos(-t.coordinate*pr),i;return o>Ce?i=n==="outer"?"start":"end":o<-Ce?i=n==="outer"?"end":"start":i="middle",i}},{key:"renderAxisLine",value:function(){var t=this.props,n=t.cx,o=t.cy,i=t.radius,c=t.axisLine,f=t.axisLineType,s=L(L({},P(this.props,!1)),{},{fill:"none"},P(c,!1));if(f==="circle")return p.createElement(ut,C({className:"recharts-polar-angle-axis-line"},s,{cx:n,cy:o,r:i}));var m=this.props.ticks,v=m.map(function(d){return T(n,o,i,d.coordinate)});return p.createElement(Ct,C({className:"recharts-polar-angle-axis-line"},s,{points:v}))}},{key:"renderTicks",value:function(){var t=this,n=this.props,o=n.ticks,i=n.tick,c=n.tickLine,f=n.tickFormatter,s=n.stroke,m=P(this.props,!1),v=P(i,!1),d=L(L({},m),{},{fill:"none"},P(c,!1)),y=o.map(function(h,u){var b=t.getTickLineCoord(h),x=t.getTickTextAnchor(h),A=L(L(L({textAnchor:x},m),{},{stroke:"none",fill:s},v),{},{index:u,payload:h,x:b.x2,y:b.y2});return p.createElement(w,C({className:$("recharts-polar-angle-axis-tick",ze(i)),key:"tick-".concat(h.coordinate)},be(t.props,h,u)),c&&p.createElement("line",C({className:"recharts-polar-angle-axis-tick-line"},d,b)),i&&e.renderTickItem(i,A,f?f(h.value,u):h.value))});return p.createElement(w,{className:"recharts-polar-angle-axis-ticks"},y)}},{key:"render",value:function(){var t=this.props,n=t.ticks,o=t.radius,i=t.axisLine;return o<=0||!n||!n.length?null:p.createElement(w,{className:$("recharts-polar-angle-axis",this.props.className)},i&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(t,n,o){var i;return p.isValidElement(t)?i=p.cloneElement(t,n):D(t)?i=t(n):i=p.createElement(ye,C({},n,{className:"recharts-polar-angle-axis-tick-value"}),o),i}}])}(Y.PureComponent);le(ce,"displayName","PolarAngleAxis");le(ce,"axisType","angleAxis");le(ce,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var re;function H(r){"@babel/helpers - typeof";return H=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},H(r)}function z(){return z=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},z.apply(this,arguments)}function Me(r,e){var a=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),a.push.apply(a,t)}return a}function g(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?Me(Object(a),!0).forEach(function(t){j(r,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):Me(Object(a)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(a,t))})}return r}function mr(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Be(r,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,Ye(t.key),t)}}function hr(r,e,a){return e&&Be(r.prototype,e),a&&Be(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r}function vr(r,e,a){return e=ie(e),gr(r,Xe()?Reflect.construct(e,a||[],ie(r).constructor):e.apply(r,a))}function gr(r,e){if(e&&(H(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return yr(r)}function yr(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function Xe(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xe=function(){return!!r})()}function ie(r){return ie=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},ie(r)}function br(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&ge(r,e)}function ge(r,e){return ge=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},ge(r,e)}function j(r,e,a){return e=Ye(e),e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}function Ye(r){var e=xr(r,"string");return H(e)=="symbol"?e:e+""}function xr(r,e){if(H(r)!="object"||!r)return r;var a=r[Symbol.toPrimitive];if(a!==void 0){var t=a.call(r,e||"default");if(H(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var R=function(r){function e(a){var t;return mr(this,e),t=vr(this,e,[a]),j(t,"pieRef",null),j(t,"sectorRefs",[]),j(t,"id",xt("recharts-pie-")),j(t,"handleAnimationEnd",function(){var n=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),D(n)&&n()}),j(t,"handleAnimationStart",function(){var n=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),D(n)&&n()}),t.state={isAnimationFinished:!a.isAnimationActive,prevIsAnimationActive:a.isAnimationActive,prevAnimationId:a.animationId,sectorToFocus:0},t}return br(e,r),hr(e,[{key:"isActiveIndex",value:function(t){var n=this.props.activeIndex;return Array.isArray(n)?n.indexOf(t)!==-1:t===n}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?t.length!==0:t||t===0}},{key:"renderLabels",value:function(t){var n=this.props.isAnimationActive;if(n&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.label,c=o.labelLine,f=o.dataKey,s=o.valueKey,m=P(this.props,!1),v=P(i,!1),d=P(c,!1),y=i&&i.offsetRadius||20,h=t.map(function(u,b){var x=(u.startAngle+u.endAngle)/2,A=T(u.cx,u.cy,u.outerRadius+y,x),k=g(g(g(g({},m),u),{},{stroke:"none"},v),{},{index:b,textAnchor:e.getTextAnchor(A.x,u.cx)},A),M=g(g(g(g({},m),u),{},{fill:"none",stroke:u.fill},d),{},{index:b,points:[T(u.cx,u.cy,u.outerRadius,x),A]}),_=f;return W(f)&&W(s)?_="value":W(f)&&(_=s),p.createElement(w,{key:"label-".concat(u.startAngle,"-").concat(u.endAngle,"-").concat(u.midAngle,"-").concat(b)},c&&e.renderLabelLineItem(c,M,"line"),e.renderLabelItem(i,k,K(u,_)))});return p.createElement(w,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(t){var n=this,o=this.props,i=o.activeShape,c=o.blendStroke,f=o.inactiveShape;return t.map(function(s,m){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&t.length!==1)return null;var v=n.isActiveIndex(m),d=f&&n.hasActiveIndex()?f:null,y=v?i:d,h=g(g({},s),{},{stroke:c?s.fill:s.stroke,tabIndex:-1});return p.createElement(w,z({ref:function(b){b&&!n.sectorRefs.includes(b)&&n.sectorRefs.push(b)},tabIndex:-1,className:"recharts-pie-sector"},be(n.props,s,m),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(m)}),p.createElement(dt,z({option:y,isActive:v,shapeType:"sector"},h)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,n=this.props,o=n.sectors,i=n.isAnimationActive,c=n.animationBegin,f=n.animationDuration,s=n.animationEasing,m=n.animationId,v=this.state,d=v.prevSectors,y=v.prevIsAnimationActive;return p.createElement(pt,{begin:c,duration:f,isActive:i,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(m,"-").concat(y),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(h){var u=h.t,b=[],x=o&&o[0],A=x.startAngle;return o.forEach(function(k,M){var _=d&&d[M],N=M>0?mt(k,"paddingAngle",0):0;if(_){var q=ke(_.endAngle-_.startAngle,k.endAngle-k.startAngle),O=g(g({},k),{},{startAngle:A+N,endAngle:A+q(u)+N});b.push(O),A=O.endAngle}else{var B=k.endAngle,S=k.startAngle,J=ke(0,B-S),Q=J(u),E=g(g({},k),{},{startAngle:A+N,endAngle:A+Q+N});b.push(E),A=E.endAngle}}),p.createElement(w,null,t.renderSectorsStatically(b))})}},{key:"attachKeyboardHandlers",value:function(t){var n=this;t.onkeydown=function(o){if(!o.altKey)switch(o.key){case"ArrowLeft":{var i=++n.state.sectorToFocus%n.sectorRefs.length;n.sectorRefs[i].focus(),n.setState({sectorToFocus:i});break}case"ArrowRight":{var c=--n.state.sectorToFocus<0?n.sectorRefs.length-1:n.state.sectorToFocus%n.sectorRefs.length;n.sectorRefs[c].focus(),n.setState({sectorToFocus:c});break}case"Escape":{n.sectorRefs[n.state.sectorToFocus].blur(),n.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var t=this.props,n=t.sectors,o=t.isAnimationActive,i=this.state.prevSectors;return o&&n&&n.length&&(!i||!ht(i,n))?this.renderSectorsWithAnimation():this.renderSectorsStatically(n)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,n=this.props,o=n.hide,i=n.sectors,c=n.className,f=n.label,s=n.cx,m=n.cy,v=n.innerRadius,d=n.outerRadius,y=n.isAnimationActive,h=this.state.isAnimationFinished;if(o||!i||!i.length||!F(s)||!F(m)||!F(v)||!F(d))return null;var u=$("recharts-pie",c);return p.createElement(w,{tabIndex:this.props.rootTabIndex,className:u,ref:function(x){t.pieRef=x}},this.renderSectors(),f&&this.renderLabels(i),We.renderCallByParent(this.props,null,!1),(!y||h)&&vt.renderCallByParent(this.props,i,!1))}}],[{key:"getDerivedStateFromProps",value:function(t,n){return n.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==n.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:n.curSectors,isAnimationFinished:!0}:t.sectors!==n.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,n){return t>n?"start":t<n?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,n,o){if(p.isValidElement(t))return p.cloneElement(t,n);if(D(t))return t(n);var i=$("recharts-pie-label-line",typeof t!="boolean"?t.className:"");return p.createElement(ft,z({},n,{key:o,type:"linear",className:i}))}},{key:"renderLabelItem",value:function(t,n,o){if(p.isValidElement(t))return p.cloneElement(t,n);var i=o;if(D(t)&&(i=t(n),p.isValidElement(i)))return i;var c=$("recharts-pie-label-text",typeof t!="boolean"&&!D(t)?t.className:"");return p.createElement(ye,z({},n,{alignmentBaseline:"middle",className:c}),i)}}])}(Y.PureComponent);re=R;j(R,"displayName","Pie");j(R,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!gt.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});j(R,"parseDeltaAngle",function(r,e){var a=te(e-r),t=Math.min(Math.abs(e-r),360);return a*t});j(R,"getRealPieData",function(r){var e=r.data,a=r.children,t=P(r,!1),n=yt(a,Ue);return e&&e.length?e.map(function(o,i){return g(g(g({payload:o},t),o),n&&n[i]&&n[i].props)}):n&&n.length?n.map(function(o){return g(g({},t),o.props)}):[]});j(R,"parseCoordinateOfPie",function(r,e){var a=e.top,t=e.left,n=e.width,o=e.height,i=bt(n,o),c=t+ee(r.cx,n,n/2),f=a+ee(r.cy,o,o/2),s=ee(r.innerRadius,i,0),m=ee(r.outerRadius,i,i*.8),v=r.maxRadius||Math.sqrt(n*n+o*o)/2;return{cx:c,cy:f,innerRadius:s,outerRadius:m,maxRadius:v}});j(R,"getComposedData",function(r){var e=r.item,a=r.offset,t=e.type.defaultProps!==void 0?g(g({},e.type.defaultProps),e.props):e.props,n=re.getRealPieData(t);if(!n||!n.length)return null;var o=t.cornerRadius,i=t.startAngle,c=t.endAngle,f=t.paddingAngle,s=t.dataKey,m=t.nameKey,v=t.valueKey,d=t.tooltipType,y=Math.abs(t.minAngle),h=re.parseCoordinateOfPie(t,a),u=re.parseDeltaAngle(i,c),b=Math.abs(u),x=s;W(s)&&W(v)?(we(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x="value"):W(s)&&(we(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x=v);var A=n.filter(function(O){return K(O,x,0)!==0}).length,k=(b>=360?A:A-1)*f,M=b-A*y-k,_=n.reduce(function(O,B){var S=K(B,x,0);return O+(F(S)?S:0)},0),N;if(_>0){var q;N=n.map(function(O,B){var S=K(O,x,0),J=K(O,m,B),Q=(F(S)?S:0)/_,E;B?E=q.endAngle+te(u)*f*(S!==0?1:0):E=i;var xe=E+te(u)*((S!==0?y:0)+Q*M),Ae=(E+xe)/2,Pe=(h.innerRadius+h.outerRadius)/2,Je=[{name:J,value:S,payload:O,dataKey:x,type:d}],Qe=T(h.cx,h.cy,Pe,Ae);return q=g(g(g({percent:Q,cornerRadius:o,name:J,tooltipPayload:Je,midAngle:Ae,middleRadius:Pe,tooltipPosition:Qe},O),h),{},{value:K(O,x),startAngle:E,endAngle:xe,payload:O,paddingAngle:te(u)*f}),q})}return g(g({},h),{},{sectors:N,data:n})});var Ar=At({chartName:"PieChart",GraphicalChild:R,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:ce},{axisType:"radiusAxis",AxisComp:se}],formatAxisMap:Pt,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});const wr=()=>{const{user:r,users:e}=et();tt();const[a,t]=Y.useState(null),n=rt(),o=[{id:"team-ulm",name:"Team Ulm"},{id:"team-stuttgart",name:"Team Stuttgart"},{id:"team-ludwigsburg",name:"Team Ludwigsburg"}];if(Y.useEffect(()=>{r!=null&&r.teamId&&!a?t(r.teamId):!a&&o.length>0&&t(o[0].id)},[r,a,o]),!r||r.role!=="teamleiter")return l.jsx(je,{title:"Teams Statistiken",children:l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:l.jsx("div",{className:"text-center",children:l.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})})})});const i=o.find(d=>d.id===a)||o[0],f=(d=>{const h={"team-ulm":1,"team-stuttgart":1.5,"team-ludwigsburg":.8}[d]||1;return[{name:"Berater",value:Math.round(8*h)},{name:"Mentoren",value:Math.round(4*h)},{name:"Teamleiter",value:1}]})(a||""),s=["#3b82f6","#10b981","#f59e0b"],v=(d=>{const h={"team-ulm":1.2,"team-stuttgart":.9,"team-ludwigsburg":1.5}[d]||1;return[{name:"KIP",value:Math.round(15*h)},{name:"TV",value:Math.round(10*h)},{name:"Mobile",value:Math.round(20*h)}]})(a||"");return l.jsx(je,{title:"Teams Statistiken",children:l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:l.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[l.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[l.jsx("h1",{className:`font-bold text-gray-800 ${n?"text-2xl":"text-4xl"}`,children:"Teams Statistiken"}),l.jsxs("p",{className:`text-gray-600 ${n?"text-sm":"text-lg"}`,children:["Statistiken für ",i.name]}),l.jsx("p",{className:`text-gray-500 ${n?"text-xs":"text-sm"}`,children:nt(new Date,"'Stand:' d. MMMM yyyy",{locale:st})})]}),l.jsxs(ue,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[l.jsx(fe,{className:`${n?"p-4 pb-2":"p-6 pb-4"}`,children:l.jsxs(de,{className:`flex items-center gap-3 ${n?"text-lg":"text-xl"} font-bold text-gray-800`,children:[l.jsx(at,{className:`${n?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Team auswählen"]})}),l.jsx(pe,{className:`${n?"p-4 pt-0":"p-6 pt-0"}`,children:l.jsx("div",{className:`flex flex-wrap gap-2 ${n?"gap-2":"gap-3"}`,children:o.map(d=>l.jsx("button",{className:`${n?"px-3 py-2 text-sm":"px-4 py-2.5 text-base"} rounded-2xl font-medium transition-all duration-300 hover-scale ${a===d.id?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,onClick:()=>t(d.id),children:d.name},d.id))})})]}),l.jsxs("div",{className:`grid gap-6 ${n?"grid-cols-1":"grid-cols-1 lg:grid-cols-2"}`,children:[l.jsxs(ue,{className:"glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[l.jsxs(fe,{className:`${n?"p-4 pb-2":"p-6 pb-4"}`,children:[l.jsxs(de,{className:`flex items-center gap-3 ${n?"text-lg":"text-xl"} font-bold text-gray-800`,children:[l.jsx(it,{className:`${n?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Team nach Rolle"]}),l.jsx(Oe,{className:`${n?"text-xs":"text-sm"} text-gray-600`,children:"Verteilung der Rollen im Team"})]}),l.jsx(pe,{className:`${n?"p-4 pt-0":"p-6 pt-0"}`,children:l.jsx("div",{style:{height:n?"280px":"350px"},className:"w-full",children:l.jsx(_e,{width:"100%",height:"100%",children:l.jsxs(Ar,{children:[l.jsx(R,{data:f,cx:"50%",cy:"50%",labelLine:!1,label:({name:d,percent:y})=>`${d}: ${(y*100).toFixed(0)}%`,outerRadius:n?70:90,fill:"#8884d8",dataKey:"value",children:f.map((d,y)=>l.jsx(Ue,{fill:s[y%s.length]},`cell-${y}`))}),l.jsx(Se,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),l.jsx(Te,{})]})})})})]}),l.jsxs(ue,{className:"glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[l.jsxs(fe,{className:`${n?"p-4 pb-2":"p-6 pb-4"}`,children:[l.jsxs(de,{className:`flex items-center gap-3 ${n?"text-lg":"text-xl"} font-bold text-gray-800`,children:[l.jsx(ot,{className:`${n?"h-5 w-5":"h-6 w-6"} text-green-600`}),"Verkäufe nach Kategorie"]}),l.jsx(Oe,{className:`${n?"text-xs":"text-sm"} text-gray-600`,children:"Produktverkäufe pro Kategorie"})]}),l.jsx(pe,{className:`${n?"p-4 pt-0":"p-6 pt-0"}`,children:l.jsx("div",{style:{height:n?"280px":"350px"},className:"w-full",children:l.jsx(_e,{width:"100%",height:"100%",children:l.jsxs(Ot,{data:v,margin:{top:20,right:30,left:20,bottom:5},children:[l.jsx(jt,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),l.jsx(kt,{dataKey:"name",tick:{fontSize:n?12:14},stroke:"#64748b"}),l.jsx(wt,{tick:{fontSize:n?12:14},stroke:"#64748b"}),l.jsx(Se,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),l.jsx(Te,{}),l.jsx(_t,{dataKey:"value",name:"Anzahl",fill:"#8b5cf6",radius:[4,4,0,0]})]})})})})]})]})]})})})};export{wr as default};
