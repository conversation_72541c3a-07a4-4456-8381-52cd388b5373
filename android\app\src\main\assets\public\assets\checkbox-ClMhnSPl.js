import{r as s,F as O,j as n,a as R}from"./index-Cmt5neWh.js";import{c as A,u as B,P as w,a as j,d as H,b as K}from"./input-BK13BBqa.js";import{u as L}from"./index-CEhV84jC.js";import{C as q}from"./check-abM7k-xd.js";var E="Checkbox",[z,W]=A(E),[F,T]=z(E),N=s.forwardRef((e,i)=>{const{__scopeCheckbox:t,name:u,checked:p,defaultChecked:o,required:h,disabled:d,value:m="on",onCheckedChange:C,form:l,...x}=e,[a,k]=s.useState(null),v=O(i,r=>k(r)),y=s.useRef(!1),P=a?l||!!a.closest("form"):!0,[f=!1,g]=B({prop:p,defaultProp:o,onChange:C}),M=s.useRef(f);return s.useEffect(()=>{const r=a==null?void 0:a.form;if(r){const b=()=>g(M.current);return r.addEventListener("reset",b),()=>r.removeEventListener("reset",b)}},[a,g]),n.jsxs(F,{scope:t,state:f,disabled:d,children:[n.jsx(w.button,{type:"button",role:"checkbox","aria-checked":c(f)?"mixed":f,"aria-required":h,"data-state":_(f),"data-disabled":d?"":void 0,disabled:d,value:m,...x,ref:v,onKeyDown:j(e.onKeyDown,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:j(e.onClick,r=>{g(b=>c(b)?!0:!b),P&&(y.current=r.isPropagationStopped(),y.current||r.stopPropagation())})}),P&&n.jsx(X,{control:a,bubbles:!y.current,name:u,value:m,checked:f,required:h,disabled:d,form:l,style:{transform:"translateX(-100%)"},defaultChecked:c(o)?!1:o})]})});N.displayName=E;var S="CheckboxIndicator",I=s.forwardRef((e,i)=>{const{__scopeCheckbox:t,forceMount:u,...p}=e,o=T(S,t);return n.jsx(H,{present:u||c(o.state)||o.state===!0,children:n.jsx(w.span,{"data-state":_(o.state),"data-disabled":o.disabled?"":void 0,...p,ref:i,style:{pointerEvents:"none",...e.style}})})});I.displayName=S;var X=e=>{const{control:i,checked:t,bubbles:u=!0,defaultChecked:p,...o}=e,h=s.useRef(null),d=L(t),m=K(i);s.useEffect(()=>{const l=h.current,x=window.HTMLInputElement.prototype,k=Object.getOwnPropertyDescriptor(x,"checked").set;if(d!==t&&k){const v=new Event("click",{bubbles:u});l.indeterminate=c(t),k.call(l,c(t)?!1:t),l.dispatchEvent(v)}},[d,t,u]);const C=s.useRef(c(t)?!1:t);return n.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:p??C.current,...o,tabIndex:-1,ref:h,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function c(e){return e==="indeterminate"}function _(e){return c(e)?"indeterminate":e?"checked":"unchecked"}var D=N,$=I;const G=s.forwardRef(({className:e,...i},t)=>n.jsx(D,{ref:t,className:R("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...i,children:n.jsx($,{className:R("flex items-center justify-center text-current"),children:n.jsx(q,{className:"h-4 w-4"})})}));G.displayName=D.displayName;export{G as C};
