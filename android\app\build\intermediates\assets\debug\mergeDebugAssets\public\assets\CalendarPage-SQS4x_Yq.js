import{r as y,m as A,j as e,k as i,C as c,i as d,B as m,l as o,a as N}from"./index-Cmt5neWh.js";import{s as B,h as O,i as z,M as I,C as v,f as r,j as F,k as H,l as K,m as L,a as f,d as b}from"./MainLayout-wyzz138D.js";import{B as P}from"./badge-XkNoLG2o.js";import{C as R}from"./clock-DhYcPjhn.js";import"./input-BK13BBqa.js";const V=()=>{const[a,x]=y.useState(new Date),[n,M]=y.useState(new Date),{getAppointmentsByDate:D,getUpcomingAppointments:C}=A(),w=B(a),k=O(a),S=z({start:w,end:k}),h=C().slice(0,5),g=s=>{const t=new Date(a);s==="prev"?t.setMonth(a.getMonth()-1):t.setMonth(a.getMonth()+1),x(t)},T=s=>{M(s)},l=s=>{const t=r(s,"yyyy-MM-dd");return D(t)},E=s=>l(s).length>0;return e.jsx(I,{title:"Termine",children:e.jsx("div",{className:"min-h-full bg-gradient-to-br from-red-50 via-white to-red-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Terminkalender"}),e.jsx("p",{className:"text-gray-600",children:"Übersicht Ihrer geplanten Termine"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(i,{className:"w-full",children:[e.jsx(c,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5"}),r(a,"MMMM yyyy",{locale:b})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(m,{variant:"outline",size:"sm",onClick:()=>g("prev"),children:e.jsx(F,{className:"h-4 w-4"})}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>x(new Date),children:"Heute"}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>g("next"),children:e.jsx(H,{className:"h-4 w-4"})})]})]})}),e.jsx(o,{children:e.jsxs("div",{className:"grid grid-cols-7 gap-2",children:[["Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag","Sonntag"].map(s=>e.jsx("div",{className:"h-10 flex items-center justify-center text-sm font-medium text-gray-500",children:s.slice(0,2)},s)),S.map(s=>{const t=l(s),j=E(s),p=n&&K(s,n),u=L(s);return e.jsx("button",{onClick:()=>T(s),className:N("h-20 p-2 rounded-lg border-2 border-transparent transition-all","hover:border-gray-200 hover:bg-gray-50",u&&"bg-red-50 border-red-200",p&&"border-blue-400 bg-blue-50",j&&"bg-green-50"),children:e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:N("text-sm font-medium",u&&"text-red-700",p&&"text-blue-700"),children:r(s,"d")}),j&&e.jsx("div",{className:"mt-1",children:e.jsx(P,{variant:"secondary",className:"text-xs px-1 py-0 bg-blue-100 text-blue-700",children:t.length})})]})},s.toISOString())})]})})]})}),e.jsxs("div",{className:"space-y-6",children:[n&&e.jsxs(i,{children:[e.jsx(c,{children:e.jsx(d,{className:"text-lg",children:r(n,"EEEE, dd.MM.yyyy",{locale:b})})}),e.jsx(o,{children:l(n).length>0?e.jsx("div",{className:"space-y-3",children:l(n).map((s,t)=>e.jsxs("div",{className:"p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:[e.jsxs("div",{className:"flex items-center gap-2 text-blue-800 font-medium",children:[e.jsx(R,{className:"h-4 w-4"}),s.time]}),e.jsxs("div",{className:"flex items-start gap-2 mt-1 text-blue-600 text-sm",children:[e.jsx(f,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:s.address})]})]},t))}):e.jsx("p",{className:"text-gray-500 text-sm",children:"Keine Termine an diesem Tag"})})]}),e.jsxs(i,{children:[e.jsx(c,{children:e.jsx(d,{className:"text-lg",children:"Nächste Termine"})}),e.jsx(o,{children:h.length>0?e.jsx("div",{className:"space-y-3",children:h.map((s,t)=>e.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-800 font-medium text-sm",children:[e.jsx(v,{className:"h-4 w-4"}),r(new Date(s.date),"dd.MM.yyyy")," um ",s.time]}),e.jsxs("div",{className:"flex items-start gap-2 mt-1 text-gray-600 text-xs",children:[e.jsx(f,{className:"h-3 w-3 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:s.address})]})]},t))}):e.jsx("p",{className:"text-gray-500 text-sm",children:"Keine anstehenden Termine"})})]})]})]})]})})})};export{V as default};
