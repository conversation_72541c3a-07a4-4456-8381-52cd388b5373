var f=Object.defineProperty;var p=(n,t,o)=>t in n?f(n,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[t]=o;var u=(n,t,o)=>p(n,typeof t!="symbol"?t+"":t,o);class g{constructor(t){u(this,"accessToken");this.accessToken=t}async geocodeAddress(t){try{const o=encodeURIComponent(t),r=await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${o}.json?access_token=${this.accessToken}&country=DE&limit=1`);if(!r.ok)throw new Error("Geocoding request failed");const s=await r.json();if(s.features&&s.features.length>0){const e=s.features[0];if(e&&e.center&&e.center.length>=2)return{longitude:e.center[0],latitude:e.center[1],place_name:e.place_name}}return null}catch(o){return console.error("Geocoding error:",o),null}}async reverseGeocode(t,o){try{const r=await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${t},${o}.json?access_token=${this.accessToken}&limit=1&country=DE&types=address`);if(!r.ok)throw new Error("Reverse geocoding request failed");const s=await r.json();if(s.features&&s.features.length>0){const e=s.features[0];if(e&&e.place_name)return e.place_name}return null}catch(r){return console.error("Reverse geocoding error:",r),null}}async reverseGeocodeDetailed(t,o){try{const r=await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${t},${o}.json?access_token=${this.accessToken}&limit=1&country=DE&types=address`);if(!r.ok)throw new Error("Detailed reverse geocoding request failed");const s=await r.json();if(s.features&&s.features.length>0){const e=s.features[0];let a="",i="",d="",l="";if(e.text&&(a=e.text),e.address&&(i=e.address),e.context)for(const c of e.context)c.id.startsWith("postcode")?d=c.text:c.id.startsWith("place")&&(l=c.text);return{street:a||void 0,houseNumber:i||void 0,zipCode:d||void 0,city:l||void 0,fullAddress:e.place_name}}return null}catch(r){return console.error("Detailed reverse geocoding error:",r),null}}}export{g as G};
