import{s as p,j as e,F as pe,r as R,a as E,m as xe,k as B,l as $,C as he,i as ue,B as U,T as z}from"./index-Cmt5neWh.js";import{B as v}from"./badge-XkNoLG2o.js";import{f as ge,c as fe,u as Z,a as ve,P as q,e as je}from"./input-BK13BBqa.js";import{c as G,R as be,T as Ne,b as ye}from"./index-CB2fuKla.js";import{u as Ce}from"./index-C8JwcrUT.js";import{C as Ae}from"./chevron-down-I8tOk39n.js";import{C as we}from"./circle-check-big-DK6RP7UF.js";import{T as L,a as Ie,f as K,d as Re,C as _e}from"./MainLayout-wyzz138D.js";import{D as Se}from"./download-BHlV_KY3.js";import{C as F}from"./clock-DhYcPjhn.js";var N="Accordion",Te=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[k,Pe,De]=ge(N),[_,Xe]=fe(N,[De,G]),M=G(),W=p.forwardRef((s,o)=>{const{type:t,...a}=s,c=a,r=a;return e.jsx(k.Provider,{scope:s.__scopeAccordion,children:t==="multiple"?e.jsx(Oe,{...r,ref:o}):e.jsx(Me,{...c,ref:o})})});W.displayName=N;var[J,Ee]=_(N),[Y,ke]=_(N,{collapsible:!1}),Me=p.forwardRef((s,o)=>{const{value:t,defaultValue:a,onValueChange:c=()=>{},collapsible:r=!1,...i}=s,[d,x]=Z({prop:t,defaultProp:a,onChange:c});return e.jsx(J,{scope:s.__scopeAccordion,value:d?[d]:[],onItemOpen:x,onItemClose:p.useCallback(()=>r&&x(""),[r,x]),children:e.jsx(Y,{scope:s.__scopeAccordion,collapsible:r,children:e.jsx(Q,{...i,ref:o})})})}),Oe=p.forwardRef((s,o)=>{const{value:t,defaultValue:a,onValueChange:c=()=>{},...r}=s,[i=[],d]=Z({prop:t,defaultProp:a,onChange:c}),x=p.useCallback(h=>d((g=[])=>[...g,h]),[d]),j=p.useCallback(h=>d((g=[])=>g.filter(l=>l!==h)),[d]);return e.jsx(J,{scope:s.__scopeAccordion,value:i,onItemOpen:x,onItemClose:j,children:e.jsx(Y,{scope:s.__scopeAccordion,collapsible:!0,children:e.jsx(Q,{...r,ref:o})})})}),[He,S]=_(N),Q=p.forwardRef((s,o)=>{const{__scopeAccordion:t,disabled:a,dir:c,orientation:r="vertical",...i}=s,d=p.useRef(null),x=pe(d,o),j=Pe(t),g=Ce(c)==="ltr",l=ve(s.onKeyDown,f=>{var H;if(!Te.includes(f.key))return;const w=f.target,n=j().filter(P=>{var V;return!((V=P.ref.current)!=null&&V.disabled)}),m=n.findIndex(P=>P.ref.current===w),C=n.length;if(m===-1)return;f.preventDefault();let u=m;const b=0,A=C-1,y=()=>{u=m+1,u>A&&(u=b)},T=()=>{u=m-1,u<b&&(u=A)};switch(f.key){case"Home":u=b;break;case"End":u=A;break;case"ArrowRight":r==="horizontal"&&(g?y():T());break;case"ArrowDown":r==="vertical"&&y();break;case"ArrowLeft":r==="horizontal"&&(g?T():y());break;case"ArrowUp":r==="vertical"&&T();break}const me=u%C;(H=n[me].ref.current)==null||H.focus()});return e.jsx(He,{scope:t,disabled:a,direction:c,orientation:r,children:e.jsx(k.Slot,{scope:t,children:e.jsx(q.div,{...i,"data-orientation":r,ref:x,onKeyDown:a?void 0:l})})})}),I="AccordionItem",[Ve,O]=_(I),X=p.forwardRef((s,o)=>{const{__scopeAccordion:t,value:a,...c}=s,r=S(I,t),i=Ee(I,t),d=M(t),x=je(),j=a&&i.value.includes(a)||!1,h=r.disabled||s.disabled;return e.jsx(Ve,{scope:t,open:j,disabled:h,triggerId:x,children:e.jsx(be,{"data-orientation":r.orientation,"data-state":ne(j),...d,...c,ref:o,disabled:h,open:j,onOpenChange:g=>{g?i.onItemOpen(a):i.onItemClose(a)}})})});X.displayName=I;var ee="AccordionHeader",te=p.forwardRef((s,o)=>{const{__scopeAccordion:t,...a}=s,c=S(N,t),r=O(ee,t);return e.jsx(q.h3,{"data-orientation":c.orientation,"data-state":ne(r.open),"data-disabled":r.disabled?"":void 0,...a,ref:o})});te.displayName=ee;var D="AccordionTrigger",se=p.forwardRef((s,o)=>{const{__scopeAccordion:t,...a}=s,c=S(N,t),r=O(D,t),i=ke(D,t),d=M(t);return e.jsx(k.ItemSlot,{scope:t,children:e.jsx(Ne,{"aria-disabled":r.open&&!i.collapsible||void 0,"data-orientation":c.orientation,id:r.triggerId,...d,...a,ref:o})})});se.displayName=D;var re="AccordionContent",ae=p.forwardRef((s,o)=>{const{__scopeAccordion:t,...a}=s,c=S(N,t),r=O(re,t),i=M(t);return e.jsx(ye,{role:"region","aria-labelledby":r.triggerId,"data-orientation":c.orientation,...i,...a,ref:o,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...s.style}})});ae.displayName=re;function ne(s){return s?"open":"closed"}var Be=W,$e=X,Ue=te,oe=se,ie=ae;const ze=Be,ce=R.forwardRef(({className:s,...o},t)=>e.jsx($e,{ref:t,className:E("border-b",s),...o}));ce.displayName="AccordionItem";const le=R.forwardRef(({className:s,children:o,...t},a)=>e.jsx(Ue,{className:"flex",children:e.jsxs(oe,{ref:a,className:E("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",s),...t,children:[o,e.jsx(Ae,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));le.displayName=oe.displayName;const de=R.forwardRef(({className:s,children:o,...t},a)=>e.jsx(ie,{ref:a,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:e.jsx("div",{className:E("pb-4 pt-0",s),children:o})}));de.displayName=ie.displayName;const et=({className:s})=>{const{getAddressesRequiringReturnVisits:o,getHighPriorityRecommendations:t}=xe(),[a,c]=R.useState(!1),r=o(),i=t(),d=a?r:r.slice(0,5),x=l=>{switch(l){case"high":return e.jsx(v,{variant:"destructive",children:"Hoch"});case"medium":return e.jsx(v,{className:"bg-yellow-500",children:"Mittel"});case"low":return e.jsx(v,{className:"bg-green-500",children:"Niedrig"});default:return e.jsx(v,{variant:"outline",children:"Unbekannt"})}},j=l=>{switch(l){case"optimal_time":return e.jsx(F,{className:"h-4 w-4 text-blue-500"});case"avoid_time":return e.jsx(z,{className:"h-4 w-4 text-red-500"});case"day_preference":return e.jsx(_e,{className:"h-4 w-4 text-green-500"});case"general_advice":return e.jsx(L,{className:"h-4 w-4 text-purple-500"});default:return e.jsx(F,{className:"h-4 w-4 text-gray-500"})}},h=l=>`${Math.round(l*100)}%`,g=()=>{const l={exportDate:new Date().toISOString(),totalAddresses:r.length,highPriorityCount:i.length,addresses:r.map(({address:m,recommendations:C,failedVisitCount:u})=>({address:`${m.street}, ${m.zipCode} ${m.city}`,failedVisits:u,recommendations:C.map(b=>{var A;return{type:b.recommendationType,priority:b.priority,confidence:h(b.confidence),recommendation:b.recommendation,suggestedTimeSlots:(A=b.suggestedTimeSlots)==null?void 0:A.map(y=>({day:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"][y.day]||"Beliebig",time:`${y.startHour}:00 - ${y.endHour}:00`,confidence:h(y.confidence)}))}})}))},f=JSON.stringify(l,null,2),w=new Blob([f],{type:"application/json"}),n=document.createElement("a");n.href=URL.createObjectURL(w),n.download=`besuchsempfehlungen-${K(new Date,"yyyy-MM-dd")}.json`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(n.href)};return r.length===0?e.jsx(B,{className:s,children:e.jsx($,{className:"pt-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(we,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),e.jsx("p",{className:"text-muted-foreground mb-2",children:"Keine Wiederholungsbesuche erforderlich"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Alle Besuche waren erfolgreich!"})]})})}):e.jsxs(B,{className:s,children:[e.jsx(he,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs(ue,{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5"}),"Intelligente Besuchsempfehlungen"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{variant:"outline",className:"bg-blue-50",children:[r.length," Adressen"]}),e.jsxs(U,{variant:"outline",size:"sm",onClick:g,children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Export"]})]})]})}),e.jsxs($,{children:[i.length>0&&e.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(z,{className:"h-5 w-5 text-red-500"}),e.jsx("span",{className:"font-semibold text-red-700",children:"Hohe Priorität"}),e.jsx(v,{variant:"destructive",children:i.length})]}),e.jsxs("p",{className:"text-sm text-red-600",children:[i.length," Empfehlung",i.length!==1?"en":""," mit hoher Priorität verfügbar"]})]}),e.jsx(ze,{type:"single",collapsible:!0,className:"w-full",children:d.map(({address:l,recommendations:f,failedVisitCount:w})=>e.jsxs(ce,{value:l.id,children:[e.jsx(le,{className:"hover:no-underline",children:e.jsxs("div",{className:"flex justify-between w-full pr-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ie,{className:"h-4 w-4 text-gray-500"}),e.jsxs("span",{children:[l.street,", ",l.city]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",className:"bg-red-50",children:[w," fehlgeschlagen"]}),e.jsxs(v,{variant:"outline",className:"bg-blue-50",children:[f.length," Empfehlung",f.length!==1?"en":""]})]})]})}),e.jsx(de,{children:e.jsx("div",{className:"space-y-3 pl-6",children:f.map(n=>e.jsxs("div",{className:"border rounded-lg p-3 bg-gray-50",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[j(n.recommendationType),e.jsxs("span",{className:"font-medium text-sm",children:[n.recommendationType==="optimal_time"&&"Optimale Zeit",n.recommendationType==="avoid_time"&&"Zeit vermeiden",n.recommendationType==="day_preference"&&"Tagesbevorzugung",n.recommendationType==="general_advice"&&"Allgemeine Empfehlung"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[x(n.priority),e.jsxs(v,{variant:"outline",className:"text-xs",children:[h(n.confidence)," Vertrauen"]})]})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-2",children:n.recommendation}),n.suggestedTimeSlots&&n.suggestedTimeSlots.length>0&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-xs font-medium text-gray-600 mb-1",children:"Empfohlene Zeitfenster:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:n.suggestedTimeSlots.map((m,C)=>e.jsxs(v,{variant:"outline",className:"text-xs bg-green-50",children:[m.day!==-1?["So","Mo","Di","Mi","Do","Fr","Sa"][m.day]:"Täglich"," ",m.startHour,":00-",m.endHour,":00"]},C))})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2 text-xs text-gray-500",children:[e.jsxs("span",{children:["Basiert auf ",n.basedOnVisits," fehlgeschlagenen Besuchen"]}),e.jsxs("span",{children:["Erstellt: ",K(new Date(n.createdAt),"dd.MM.yyyy",{locale:Re})]})]})]},n.id))})})]},l.id))}),r.length>5&&e.jsx("div",{className:"mt-4 text-center",children:e.jsx(U,{variant:"outline",onClick:()=>c(!a),className:"w-full",children:a?"Weniger anzeigen":`${r.length-5} weitere Adressen anzeigen`})})]})]})};export{ze as A,et as V,ce as a,le as b,de as c};
