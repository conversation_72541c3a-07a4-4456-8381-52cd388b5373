import{m as L,b as P,r as R,j as e,k as b,l as g,C as N,i as y,t as f}from"./index-Cmt5neWh.js";import{b as O,U as v,f as W,d as q,a as F,T as G,C as J}from"./MainLayout-wyzz138D.js";import{T as $,a as w,b as c,c as i,d as C,e as l}from"./table-Bp6KGmPn.js";import{T as Q,a as X,b as T,c as D}from"./tabs-BJh52NhZ.js";import{B as m}from"./badge-XkNoLG2o.js";import{E as Y}from"./eye-DwLHs0eg.js";import{C as I}from"./circle-check-big-DK6RP7UF.js";const ne=({mentorId:B})=>{const{visits:p,doors:V,products:k,houses:Z,addresses:A,getHouseById:S}=L(),{users:u}=P(),s=O(),[M,E]=R.useState("overview"),j=u.filter(t=>t.mentorId===B),o=j.map(t=>{const a=p.filter(r=>r.userId===t.id),x=k.filter(r=>r.userId===t.id),n=new Date().toISOString().split("T")[0],d=a.filter(r=>r.timestamp.startsWith(n));return{id:t.id,name:t.name,totalVisits:a.length,todayVisits:d.length,salesCount:x.length}}),H=o.reduce((t,a)=>t+a.totalVisits,0),U=o.reduce((t,a)=>t+a.salesCount,0),z=o.reduce((t,a)=>t+a.todayVisits,0),h=V.filter(t=>t.status==="Angetroffen → Sale").map(t=>{const a=p.find(r=>r.id===t.visitId);if(!a)return null;const x=u.find(r=>r.id===a.userId),n=S(a.houseId),d=n?A.find(r=>r.id===n.addressId):null;return{doorId:t.id,visitId:t.visitId,visitDate:a.timestamp?new Date(a.timestamp).toLocaleDateString("de-DE"):"Unbekannt",address:d?`${d.street} ${(n==null?void 0:n.houseNumber)||""}, ${d.zipCode} ${d.city}`:"Unbekannte Adresse",beraterName:(x==null?void 0:x.name)||"Unbekannt"}}).filter(Boolean),K=[{title:"Team Besuche",value:H,icon:v,color:"from-blue-500 to-blue-600",textColor:"text-blue-600"},{title:"Team Verkäufe",value:U,icon:G,color:"from-green-500 to-green-600",textColor:"text-green-600"},{title:"Heute",value:z,icon:J,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${s?"text-2xl":"text-4xl"}`,children:"Mentor Dashboard"}),e.jsxs("p",{className:`text-gray-600 ${s?"text-sm":"text-lg"}`,children:["Übersicht über ",j.length," zugewiesene Berater"]})]}),e.jsxs(Q,{value:M,onValueChange:E,className:"w-full",children:[e.jsxs(X,{className:`grid w-full grid-cols-2 ${s?"h-12":"h-14"} bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg rounded-2xl`,children:[e.jsxs(T,{value:"overview",className:`${s?"text-sm py-2":"text-base py-3"} data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white rounded-xl transition-all duration-300`,children:[e.jsx(Y,{className:"h-4 w-4 mr-2"}),"Team Übersicht"]}),e.jsxs(T,{value:"completed",className:`${s?"text-sm py-2":"text-base py-3"} data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300`,children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Abgeschlossen"]})]}),e.jsxs(D,{value:"overview",className:"space-y-6 animate-fade-in",children:[e.jsx("div",{className:`grid gap-4 md:gap-6 ${s?"grid-cols-1":"grid-cols-1 md:grid-cols-3"}`,children:K.map((t,a)=>e.jsx(b,{className:`glass-card hover-lift ${s?"p-4":"p-6"} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`,style:{animationDelay:`${a*.1}s`},children:e.jsx(g,{className:"p-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:`font-medium text-gray-600 ${s?"text-xs":"text-sm"}`,children:t.title}),e.jsx("p",{className:`font-bold ${t.textColor} ${s?"text-2xl":"text-4xl"}`,children:t.value})]}),e.jsx("div",{className:`rounded-2xl bg-gradient-to-br ${t.color} p-3 shadow-lg`,children:e.jsx(t.icon,{className:`${s?"h-6 w-6":"h-8 w-8"} text-white`})})]})})},t.title))}),e.jsxs(b,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(N,{className:`${s?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(y,{className:`flex items-center gap-3 ${s?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(v,{className:`${s?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Berater-Übersicht"]}),e.jsx(f,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:W(new Date,"'Stand:' d. MMMM yyyy",{locale:q})})]}),e.jsx(g,{className:`${s?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{className:"overflow-x-auto scrollbar-none",children:e.jsxs($,{children:[e.jsx(w,{children:e.jsxs(c,{className:"border-gray-200",children:[e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Name"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Besuche gesamt"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Heute"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Verkäufe"})]})}),e.jsxs(C,{children:[o.map((t,a)=>e.jsxs(c,{className:"hover:bg-blue-50/50 transition-colors border-gray-100",style:{animationDelay:`${a*.05}s`},children:[e.jsx(l,{className:`font-medium text-gray-800 ${s?"text-sm":"text-base"}`,children:t.name}),e.jsx(l,{className:`${s?"text-sm":"text-base"}`,children:e.jsx(m,{variant:"secondary",className:"bg-blue-100 text-blue-800 hover:bg-blue-200",children:t.totalVisits})}),e.jsx(l,{className:`${s?"text-sm":"text-base"}`,children:e.jsx(m,{variant:"secondary",className:"bg-purple-100 text-purple-800 hover:bg-purple-200",children:t.todayVisits})}),e.jsx(l,{className:`${s?"text-sm":"text-base"}`,children:e.jsx(m,{variant:"secondary",className:"bg-green-100 text-green-800 hover:bg-green-200",children:t.salesCount})})]},t.id)),o.length===0&&e.jsx(c,{children:e.jsx(l,{colSpan:4,className:`text-center py-8 text-gray-500 ${s?"text-sm":"text-base"}`,children:"Keine Berater zugewiesen"})})]})]})})})]})]}),e.jsx(D,{value:"completed",className:"space-y-6 animate-fade-in",children:e.jsxs(b,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(N,{className:`${s?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(y,{className:`flex items-center gap-3 ${s?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(F,{className:`${s?"h-5 w-5":"h-6 w-6"} text-green-600`}),"Abgeschlossene Adressen"]}),e.jsxs(f,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:["Erfolgreich bearbeitete Adressen (",h.length," Einträge)"]})]}),e.jsx(g,{className:`${s?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{className:"overflow-x-auto scrollbar-none",children:e.jsxs($,{children:[e.jsx(w,{children:e.jsxs(c,{className:"border-gray-200",children:[e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Datum"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Adresse"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Berater"}),e.jsx(i,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Status"})]})}),e.jsxs(C,{children:[h.map((t,a)=>e.jsxs(c,{className:"hover:bg-green-50/50 transition-colors border-gray-100",style:{animationDelay:`${a*.05}s`},children:[e.jsx(l,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:t.visitDate}),e.jsx(l,{className:`font-medium text-gray-800 ${s?"text-xs":"text-sm"} max-w-0 truncate`,children:t.address}),e.jsx(l,{className:`${s?"text-xs":"text-sm"} text-gray-700`,children:t.beraterName}),e.jsx(l,{children:e.jsxs(m,{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-md",children:[e.jsx(I,{className:"h-3 w-3 mr-1"}),"Abgeschlossen"]})})]},t.doorId)),h.length===0&&e.jsx(c,{children:e.jsx(l,{colSpan:4,className:`text-center py-8 text-gray-500 ${s?"text-sm":"text-base"}`,children:"Keine abgeschlossenen Adressen vorhanden"})})]})]})})})]})})]})]})})};export{ne as M};
