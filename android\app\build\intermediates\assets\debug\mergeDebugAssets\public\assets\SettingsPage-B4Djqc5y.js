import{c as I,r as m,j as e,B as x,J as l,b as U,z as F,k as f,C as w,i as b,l as N,T as $}from"./index-Cmt5neWh.js";import{e as B,g as O,M as L}from"./MainLayout-wyzz138D.js";import{T as K,a as H,b as Z,c as V}from"./tabs-BJh52NhZ.js";import{I as y,U as W}from"./input-BK13BBqa.js";import{L as r}from"./label-NwAA2N0T.js";import{S as E,a as T,b as A,c as M,d as t}from"./select-IVIdgARa.js";import{S as D}from"./switch-DOMi03D3.js";import{D as q,a as G,b as J,c as R,d as Y}from"./dialog-BFTaoFLK.js";import{s as _}from"./client-DbI4l5kI.js";import{L as Q}from"./lock-B_L7eRBi.js";import{E as S}from"./eye-off-j0pHjYk_.js";import{E as P}from"./eye-DwLHs0eg.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./chevron-down-I8tOk39n.js";import"./check-abM7k-xd.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=I("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=I("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),se=()=>{const[s,c]=m.useState(!1),[d,u]=m.useState(""),[o,j]=m.useState(""),[p,g]=m.useState(""),[h,C]=m.useState({current:!1,new:!1,confirm:!1}),[a,k]=m.useState(!1),n=async()=>{if(o!==p){l.error("Neue Passwörter stimmen nicht überein");return}if(o.length<6){l.error("Das neue Passwort muss mindestens 6 Zeichen lang sein");return}k(!0);try{const{error:i}=await _.auth.updateUser({password:o});i?l.error("Fehler beim Ändern des Passworts: "+i.message):(l.success("Passwort erfolgreich geändert"),c(!1),u(""),j(""),g(""))}catch{l.error("Ein unerwarteter Fehler ist aufgetreten")}finally{k(!1)}},v=i=>{C(z=>({...z,[i]:!z[i]}))};return e.jsxs(q,{open:s,onOpenChange:c,children:[e.jsx(G,{asChild:!0,children:e.jsxs(x,{variant:"outline",className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-4 w-4"}),"Passwort ändern"]})}),e.jsxs(J,{children:[e.jsx(R,{children:e.jsx(Y,{children:"Passwort ändern"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"current-password",children:"Aktuelles Passwort"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{id:"current-password",type:h.current?"text":"password",value:d,onChange:i=>u(i.target.value),placeholder:"Aktuelles Passwort"}),e.jsx(x,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>v("current"),children:h.current?e.jsx(S,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"new-password",children:"Neues Passwort"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{id:"new-password",type:h.new?"text":"password",value:o,onChange:i=>j(i.target.value),placeholder:"Neues Passwort (mindestens 6 Zeichen)"}),e.jsx(x,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>v("new"),children:h.new?e.jsx(S,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"confirm-password",children:"Neues Passwort bestätigen"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{id:"confirm-password",type:h.confirm?"text":"password",value:p,onChange:i=>g(i.target.value),placeholder:"Neues Passwort wiederholen"}),e.jsx(x,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>v("confirm"),children:h.confirm?e.jsx(S,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"flex gap-2 pt-4",children:[e.jsx(x,{onClick:n,disabled:a||!d||!o||!p,className:"flex-1",children:a?"Wird geändert...":"Passwort ändern"}),e.jsx(x,{variant:"outline",onClick:()=>c(!1),className:"flex-1",children:"Abbrechen"})]})]})]})]})},ae=()=>{const{user:s,updateUser:c}=U(),{settings:d,updateSettings:u}=F(),[o,j]=m.useState((s==null?void 0:s.name)||""),[p,g]=m.useState((s==null?void 0:s.email)||"");m.useEffect(()=>{s&&(j(s.name||""),g(s.email||""))},[s]);const h=()=>{s&&(c({...s,name:o,email:p}),l.success("Profil-Informationen gespeichert"))},C=n=>{u({language:n}),l.success(`Sprache auf ${n==="de"?"Deutsch":"English"} geändert`)},a=n=>{u({timezone:n}),l.success("Zeitzone aktualisiert")},k=(n,v)=>{u({[n]:v}),l.success(`${n==="emailNotifications"?"E-Mail":"Push"}-Benachrichtigungen ${v?"aktiviert":"deaktiviert"}`)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(W,{className:"h-5 w-5"}),"Profil-Informationen"]})}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"name",children:"Name"}),e.jsx(y,{id:"name",value:o,onChange:n=>j(n.target.value),placeholder:"Ihr vollständiger Name"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"email",children:"E-Mail"}),e.jsx(y,{id:"email",type:"email",value:p,onChange:n=>g(n.target.value),placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx(x,{onClick:h,className:"flex-1",children:"Profil speichern"}),e.jsx(se,{})]})]})]}),e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-5 w-5"}),"Sprache & Region"]})}),e.jsx(N,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Sprache"}),e.jsxs(E,{value:d.language,onValueChange:C,children:[e.jsx(T,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(t,{value:"de",children:"Deutsch"}),e.jsx(t,{value:"en",children:"English"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Zeitzone"}),e.jsxs(E,{value:d.timezone,onValueChange:a,children:[e.jsx(T,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(t,{value:"Europe/Berlin",children:"Berlin (UTC+1)"}),e.jsx(t,{value:"Europe/Vienna",children:"Wien (UTC+1)"}),e.jsx(t,{value:"Europe/Zurich",children:"Zürich (UTC+1)"}),e.jsx(t,{value:"America/New_York",children:"New York (UTC-5)"}),e.jsx(t,{value:"Asia/Tokyo",children:"Tokyo (UTC+9)"})]})]})]})]})})]}),e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"Benachrichtigungen"]})}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(r,{children:"E-Mail Benachrichtigungen"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Erhalten Sie Updates per E-Mail"})]}),e.jsx(D,{checked:d.emailNotifications,onCheckedChange:n=>k("emailNotifications",n)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(r,{children:"Push-Benachrichtigungen"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Browser-Benachrichtigungen aktivieren"})]}),e.jsx(D,{checked:d.pushNotifications,onCheckedChange:n=>k("pushNotifications",n)})]})]})]})]})},ne=()=>{const{settings:s,updateSettings:c}=F(),[d,u]=m.useState(!1),o=a=>{c({defaultView:a}),l.success(`Standard-Ansicht auf ${a==="list"?"Laufliste":a==="map"?"Kartenansicht":"Tagesübersicht"} geändert`)},j=a=>{c({mapboxToken:a}),a&&l.success("Mapbox Token gespeichert")},p=a=>{c({defaultZoom:a}),l.success(`Standard-Zoom auf ${a} geändert`)},g=a=>{c({autoSave:a}),l.success(`Automatisches Speichern ${a?"aktiviert":"deaktiviert"}`)},h=a=>{c({offlineMode:a}),l.success(`Offline-Modus ${a?"aktiviert":"deaktiviert"}`)},C=()=>{l.success("App-Einstellungen gespeichert")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Ansicht-Einstellungen"]})}),e.jsx(N,{className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Standard-Ansicht"}),e.jsxs(E,{value:s.defaultView,onValueChange:o,children:[e.jsx(T,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(t,{value:"list",children:"Laufliste"}),e.jsx(t,{value:"map",children:"Kartenansicht"}),e.jsx(t,{value:"daily",children:"Tagesübersicht"})]})]})]})})]}),e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(O,{className:"h-5 w-5"}),"Karten-Einstellungen"]})}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"mapbox-token",children:"Mapbox Token"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{id:"mapbox-token",type:d?"text":"password",value:s.mapboxToken,onChange:a=>j(a.target.value),placeholder:"pk.ey..."}),e.jsx(x,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>u(!d),children:d?e.jsx(S,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Persönlicher Mapbox Token für Kartendarstellung"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Standard-Zoom"}),e.jsxs(E,{value:s.defaultZoom,onValueChange:p,children:[e.jsx(T,{children:e.jsx(A,{})}),e.jsxs(M,{children:[e.jsx(t,{value:"10",children:"Weit (10)"}),e.jsx(t,{value:"12",children:"Normal (12)"}),e.jsx(t,{value:"14",children:"Nah (14)"}),e.jsx(t,{value:"16",children:"Sehr nah (16)"})]})]})]})]})]}),e.jsxs(f,{children:[e.jsx(w,{children:e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Allgemeine App-Einstellungen"]})}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(r,{children:"Automatisches Speichern"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Änderungen automatisch speichern"})]}),e.jsx(D,{checked:s.autoSave,onCheckedChange:g})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(r,{children:"Offline-Modus"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"App auch ohne Internetverbindung nutzen"})]}),e.jsx(D,{checked:s.offlineMode,onCheckedChange:h})]}),e.jsx(x,{onClick:C,className:"w-full md:w-auto",children:"Einstellungen speichern"})]})]})]})},ve=()=>{const{user:s}=U();return s?e.jsx(L,{title:"Einstellungen",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Einstellungen"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Konfigurieren Sie Ihre persönlichen und App-Einstellungen"})]}),e.jsxs(K,{defaultValue:"personal",className:"w-full",children:[e.jsxs(H,{className:"mb-6",children:[e.jsx(Z,{value:"personal",children:"Persönlich"}),e.jsx(Z,{value:"app",children:"App-Einstellungen"})]}),e.jsx(V,{value:"personal",children:e.jsx(ae,{})}),e.jsx(V,{value:"app",children:e.jsx(ne,{})})]}),(s==null?void 0:s.role)==="admin"&&e.jsx(f,{className:"mt-6 border-blue-200 bg-blue-50/50",children:e.jsx(N,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx($,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-blue-900 mb-1",children:"System-Einstellungen"}),e.jsx("p",{className:"text-sm text-blue-700 mb-3",children:"Als Administrator können Sie systemweite Einstellungen im Admin-Dashboard verwalten."}),e.jsx("a",{href:"/admin-dashboard",className:"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors",children:"Zum Admin-Dashboard →"})]})]})})})]})}):e.jsx(L,{title:"Einstellungen",children:e.jsx("div",{className:"p-4 text-center",children:"Sie müssen angemeldet sein, um auf die Einstellungen zuzugreifen."})})};export{ve as default};
