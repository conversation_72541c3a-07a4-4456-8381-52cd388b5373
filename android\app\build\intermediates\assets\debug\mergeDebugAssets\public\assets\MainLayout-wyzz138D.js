import{c as V,r as l,F as U,j as s,Y as vo,V as sr,a as N,Q as Zt,S as xo,P as De,B as Ze,Z,m as bo,u as ir,k as wo,C as yo,i as Eo,l as So,H as Co,b as To,$ as Po}from"./index-Cmt5neWh.js";import{h as re,P as H,a as F,i as cr,g as st,f as No,c as en,u as tn,d as Be,X as lr,b as Mo,e as et,j as jo,I as Oo,U as Ro}from"./input-BK13BBqa.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ko=V("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=V("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Do=V("ChartBar",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M7 16h8",key:"srdodz"}],["path",{d:"M7 11h12",key:"127s9w"}],["path",{d:"M7 6h3",key:"w9rmul"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=V("ChartColumnIncreasing",[["path",{d:"M13 17V9",key:"1fwyjl"}],["path",{d:"M18 17V5",key:"sfb6ij"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=V("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _o=V("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nn=V("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=V("LayoutPanelLeft",[["rect",{width:"7",height:"18",x:"3",y:"3",rx:"1",key:"2obqm"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fo=V("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Io=V("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=V("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=V("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=V("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=V("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yo=V("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vo=V("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mn=V("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),zo=1,Xo=1e6;let Mt=0;function qo(){return Mt=(Mt+1)%Number.MAX_SAFE_INTEGER,Mt.toString()}const jt=new Map,jn=e=>{if(jt.has(e))return;const t=setTimeout(()=>{jt.delete(e),Ie({type:"REMOVE_TOAST",toastId:e})},Xo);jt.set(e,t)},Go=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,zo)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?jn(n):e.toasts.forEach(r=>{jn(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},tt=[];let nt={toasts:[]};function Ie(e){nt=Go(nt,e),tt.forEach(t=>{t(nt)})}function Uo({...e}){const t=qo(),n=a=>Ie({type:"UPDATE_TOAST",toast:{...a,id:t}}),r=()=>Ie({type:"DISMISS_TOAST",toastId:t});return Ie({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:a=>{a||r()}}}),{id:t,dismiss:r,update:n}}function Ko(){const[e,t]=l.useState(nt);return l.useEffect(()=>(tt.push(t),()=>{const n=tt.indexOf(t);n>-1&&tt.splice(n,1)}),[e]),{...e,toast:Uo,dismiss:n=>Ie({type:"DISMISS_TOAST",toastId:n})}}function Qo(e,t=globalThis==null?void 0:globalThis.document){const n=re(e);l.useEffect(()=>{const r=a=>{a.key==="Escape"&&n(a)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Jo="DismissableLayer",Ht="dismissableLayer.update",Zo="dismissableLayer.pointerDownOutside",es="dismissableLayer.focusOutside",On,fr=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),mt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:a,onFocusOutside:o,onInteractOutside:i,onDismiss:c,...u}=e,d=l.useContext(fr),[f,p]=l.useState(null),v=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=l.useState({}),x=U(t,T=>p(T)),h=Array.from(d.layers),[g]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(g),b=f?h.indexOf(f):-1,w=d.layersWithOutsidePointerEventsDisabled.size>0,E=b>=y,S=ns(T=>{const j=T.target,k=[...d.branches].some(D=>D.contains(j));!E||k||(a==null||a(T),i==null||i(T),T.defaultPrevented||c==null||c())},v),P=rs(T=>{const j=T.target;[...d.branches].some(D=>D.contains(j))||(o==null||o(T),i==null||i(T),T.defaultPrevented||c==null||c())},v);return Qo(T=>{b===d.layers.size-1&&(r==null||r(T),!T.defaultPrevented&&c&&(T.preventDefault(),c()))},v),l.useEffect(()=>{if(f)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(On=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),Rn(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=On)}},[f,v,n,d]),l.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),Rn())},[f,d]),l.useEffect(()=>{const T=()=>m({});return document.addEventListener(Ht,T),()=>document.removeEventListener(Ht,T)},[]),s.jsx(H.div,{...u,ref:x,style:{pointerEvents:w?E?"auto":"none":void 0,...e.style},onFocusCapture:F(e.onFocusCapture,P.onFocusCapture),onBlurCapture:F(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:F(e.onPointerDownCapture,S.onPointerDownCapture)})});mt.displayName=Jo;var ts="DismissableLayerBranch",hr=l.forwardRef((e,t)=>{const n=l.useContext(fr),r=l.useRef(null),a=U(t,r);return l.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),s.jsx(H.div,{...e,ref:a})});hr.displayName=ts;function ns(e,t=globalThis==null?void 0:globalThis.document){const n=re(e),r=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{const o=c=>{if(c.target&&!r.current){let u=function(){mr(Zo,n,d,{discrete:!0})};const d={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",a.current),a.current=u,t.addEventListener("click",a.current,{once:!0})):u()}else t.removeEventListener("click",a.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",o),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function rs(e,t=globalThis==null?void 0:globalThis.document){const n=re(e),r=l.useRef(!1);return l.useEffect(()=>{const a=o=>{o.target&&!r.current&&mr(es,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",a),()=>t.removeEventListener("focusin",a)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Rn(){const e=new CustomEvent(Ht);document.dispatchEvent(e)}function mr(e,t,n,{discrete:r}){const a=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),r?cr(a,o):a.dispatchEvent(o)}var as=mt,os=hr,ss="Portal",nn=l.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[a,o]=l.useState(!1);st(()=>o(!0),[]);const i=n||a&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?vo.createPortal(s.jsx(H.div,{...r,ref:t}),i):null});nn.displayName=ss;var is="VisuallyHidden",pt=l.forwardRef((e,t)=>s.jsx(H.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));pt.displayName=is;var cs=pt,rn="ToastProvider",[an,ls,ds]=No("Toast"),[pr,Mu]=en("Toast",[ds]),[us,gt]=pr(rn),gr=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:a="right",swipeThreshold:o=50,children:i}=e,[c,u]=l.useState(null),[d,f]=l.useState(0),p=l.useRef(!1),v=l.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${rn}\`. Expected non-empty \`string\`.`),s.jsx(an.Provider,{scope:t,children:s.jsx(us,{scope:t,label:n,duration:r,swipeDirection:a,swipeThreshold:o,toastCount:d,viewport:c,onViewportChange:u,onToastAdd:l.useCallback(()=>f(m=>m+1),[]),onToastRemove:l.useCallback(()=>f(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:i})})};gr.displayName=rn;var vr="ToastViewport",fs=["F8"],Bt="toast.viewportPause",Yt="toast.viewportResume",xr=l.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=fs,label:a="Notifications ({hotkey})",...o}=e,i=gt(vr,n),c=ls(n),u=l.useRef(null),d=l.useRef(null),f=l.useRef(null),p=l.useRef(null),v=U(t,p,i.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=i.toastCount>0;l.useEffect(()=>{const g=y=>{var w;r.length!==0&&r.every(E=>y[E]||y.code===E)&&((w=p.current)==null||w.focus())};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[r]),l.useEffect(()=>{const g=u.current,y=p.current;if(x&&g&&y){const b=()=>{if(!i.isClosePausedRef.current){const P=new CustomEvent(Bt);y.dispatchEvent(P),i.isClosePausedRef.current=!0}},w=()=>{if(i.isClosePausedRef.current){const P=new CustomEvent(Yt);y.dispatchEvent(P),i.isClosePausedRef.current=!1}},E=P=>{!g.contains(P.relatedTarget)&&w()},S=()=>{g.contains(document.activeElement)||w()};return g.addEventListener("focusin",b),g.addEventListener("focusout",E),g.addEventListener("pointermove",b),g.addEventListener("pointerleave",S),window.addEventListener("blur",b),window.addEventListener("focus",w),()=>{g.removeEventListener("focusin",b),g.removeEventListener("focusout",E),g.removeEventListener("pointermove",b),g.removeEventListener("pointerleave",S),window.removeEventListener("blur",b),window.removeEventListener("focus",w)}}},[x,i.isClosePausedRef]);const h=l.useCallback(({tabbingDirection:g})=>{const b=c().map(w=>{const E=w.ref.current,S=[E,...Ts(E)];return g==="forwards"?S:S.reverse()});return(g==="forwards"?b.reverse():b).flat()},[c]);return l.useEffect(()=>{const g=p.current;if(g){const y=b=>{var S,P,T;const w=b.altKey||b.ctrlKey||b.metaKey;if(b.key==="Tab"&&!w){const j=document.activeElement,k=b.shiftKey;if(b.target===g&&k){(S=d.current)==null||S.focus();return}const I=h({tabbingDirection:k?"backwards":"forwards"}),_=I.findIndex(C=>C===j);Ot(I.slice(_+1))?b.preventDefault():k?(P=d.current)==null||P.focus():(T=f.current)==null||T.focus()}};return g.addEventListener("keydown",y),()=>g.removeEventListener("keydown",y)}},[c,h]),s.jsxs(os,{ref:u,role:"region","aria-label":a.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&s.jsx(Vt,{ref:d,onFocusFromOutsideViewport:()=>{const g=h({tabbingDirection:"forwards"});Ot(g)}}),s.jsx(an.Slot,{scope:n,children:s.jsx(H.ol,{tabIndex:-1,...o,ref:v})}),x&&s.jsx(Vt,{ref:f,onFocusFromOutsideViewport:()=>{const g=h({tabbingDirection:"backwards"});Ot(g)}})]})});xr.displayName=vr;var br="ToastFocusProxy",Vt=l.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...a}=e,o=gt(br,n);return s.jsx(pt,{"aria-hidden":!0,tabIndex:0,...a,ref:t,style:{position:"fixed"},onFocus:i=>{var d;const c=i.relatedTarget;!((d=o.viewport)!=null&&d.contains(c))&&r()}})});Vt.displayName=br;var vt="Toast",hs="toast.swipeStart",ms="toast.swipeMove",ps="toast.swipeCancel",gs="toast.swipeEnd",wr=l.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:a,onOpenChange:o,...i}=e,[c=!0,u]=tn({prop:r,defaultProp:a,onChange:o});return s.jsx(Be,{present:n||c,children:s.jsx(bs,{open:c,...i,ref:t,onClose:()=>u(!1),onPause:re(e.onPause),onResume:re(e.onResume),onSwipeStart:F(e.onSwipeStart,d=>{d.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:F(e.onSwipeMove,d=>{const{x:f,y:p}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","move"),d.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:F(e.onSwipeCancel,d=>{d.currentTarget.setAttribute("data-swipe","cancel"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:F(e.onSwipeEnd,d=>{const{x:f,y:p}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","end"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),u(!1)})})})});wr.displayName=vt;var[vs,xs]=pr(vt,{onClose(){}}),bs=l.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:a,open:o,onClose:i,onEscapeKeyDown:c,onPause:u,onResume:d,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:v,onSwipeEnd:m,...x}=e,h=gt(vt,n),[g,y]=l.useState(null),b=U(t,C=>y(C)),w=l.useRef(null),E=l.useRef(null),S=a||h.duration,P=l.useRef(0),T=l.useRef(S),j=l.useRef(0),{onToastAdd:k,onToastRemove:D}=h,W=re(()=>{var A;(g==null?void 0:g.contains(document.activeElement))&&((A=h.viewport)==null||A.focus()),i()}),I=l.useCallback(C=>{!C||C===1/0||(window.clearTimeout(j.current),P.current=new Date().getTime(),j.current=window.setTimeout(W,C))},[W]);l.useEffect(()=>{const C=h.viewport;if(C){const A=()=>{I(T.current),d==null||d()},R=()=>{const L=new Date().getTime()-P.current;T.current=T.current-L,window.clearTimeout(j.current),u==null||u()};return C.addEventListener(Bt,R),C.addEventListener(Yt,A),()=>{C.removeEventListener(Bt,R),C.removeEventListener(Yt,A)}}},[h.viewport,S,u,d,I]),l.useEffect(()=>{o&&!h.isClosePausedRef.current&&I(S)},[o,S,h.isClosePausedRef,I]),l.useEffect(()=>(k(),()=>D()),[k,D]);const _=l.useMemo(()=>g?Nr(g):null,[g]);return h.viewport?s.jsxs(s.Fragment,{children:[_&&s.jsx(ws,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:_}),s.jsx(vs,{scope:n,onClose:W,children:sr.createPortal(s.jsx(an.ItemSlot,{scope:n,children:s.jsx(as,{asChild:!0,onEscapeKeyDown:F(c,()=>{h.isFocusedToastEscapeKeyDownRef.current||W(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:s.jsx(H.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":o?"open":"closed","data-swipe-direction":h.swipeDirection,...x,ref:b,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:F(e.onKeyDown,C=>{C.key==="Escape"&&(c==null||c(C.nativeEvent),C.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,W()))}),onPointerDown:F(e.onPointerDown,C=>{C.button===0&&(w.current={x:C.clientX,y:C.clientY})}),onPointerMove:F(e.onPointerMove,C=>{if(!w.current)return;const A=C.clientX-w.current.x,R=C.clientY-w.current.y,L=!!E.current,M=["left","right"].includes(h.swipeDirection),$=["left","up"].includes(h.swipeDirection)?Math.min:Math.max,G=M?$(0,A):0,ye=M?0:$(0,R),Le=C.pointerType==="touch"?10:2,Ee={x:G,y:ye},Ve={originalEvent:C,delta:Ee};L?(E.current=Ee,Xe(ms,p,Ve,{discrete:!1})):kn(Ee,h.swipeDirection,Le)?(E.current=Ee,Xe(hs,f,Ve,{discrete:!1}),C.target.setPointerCapture(C.pointerId)):(Math.abs(A)>Le||Math.abs(R)>Le)&&(w.current=null)}),onPointerUp:F(e.onPointerUp,C=>{const A=E.current,R=C.target;if(R.hasPointerCapture(C.pointerId)&&R.releasePointerCapture(C.pointerId),E.current=null,w.current=null,A){const L=C.currentTarget,M={originalEvent:C,delta:A};kn(A,h.swipeDirection,h.swipeThreshold)?Xe(gs,m,M,{discrete:!0}):Xe(ps,v,M,{discrete:!0}),L.addEventListener("click",$=>$.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),ws=e=>{const{__scopeToast:t,children:n,...r}=e,a=gt(vt,t),[o,i]=l.useState(!1),[c,u]=l.useState(!1);return Ss(()=>i(!0)),l.useEffect(()=>{const d=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(d)},[]),c?null:s.jsx(nn,{asChild:!0,children:s.jsx(pt,{...r,children:o&&s.jsxs(s.Fragment,{children:[a.label," ",n]})})})},ys="ToastTitle",yr=l.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return s.jsx(H.div,{...r,ref:t})});yr.displayName=ys;var Es="ToastDescription",Er=l.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return s.jsx(H.div,{...r,ref:t})});Er.displayName=Es;var Sr="ToastAction",Cr=l.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?s.jsx(Pr,{altText:n,asChild:!0,children:s.jsx(on,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Sr}\`. Expected non-empty \`string\`.`),null)});Cr.displayName=Sr;var Tr="ToastClose",on=l.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,a=xs(Tr,n);return s.jsx(Pr,{asChild:!0,children:s.jsx(H.button,{type:"button",...r,ref:t,onClick:F(e.onClick,a.onClose)})})});on.displayName=Tr;var Pr=l.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...a}=e;return s.jsx(H.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...a,ref:t})});function Nr(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),Cs(r)){const a=r.ariaHidden||r.hidden||r.style.display==="none",o=r.dataset.radixToastAnnounceExclude==="";if(!a)if(o){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Nr(r))}}),t}function Xe(e,t,n,{discrete:r}){const a=n.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),r?cr(a,o):a.dispatchEvent(o)}var kn=(e,t,n=0)=>{const r=Math.abs(e.x),a=Math.abs(e.y),o=r>a;return t==="left"||t==="right"?o&&r>n:!o&&a>n};function Ss(e=()=>{}){const t=re(e);st(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function Cs(e){return e.nodeType===e.ELEMENT_NODE}function Ts(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const a=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||a?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ot(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var Ps=gr,Mr=xr,jr=wr,Or=yr,Rr=Er,kr=Cr,Dr=on;const Ns=Ps,Ar=l.forwardRef(({className:e,...t},n)=>s.jsx(Mr,{ref:n,className:N("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Ar.displayName=Mr.displayName;const Ms=Zt("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),_r=l.forwardRef(({className:e,variant:t,...n},r)=>s.jsx(jr,{ref:r,className:N(Ms({variant:t}),e),...n}));_r.displayName=jr.displayName;const js=l.forwardRef(({className:e,...t},n)=>s.jsx(kr,{ref:n,className:N("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));js.displayName=kr.displayName;const Lr=l.forwardRef(({className:e,...t},n)=>s.jsx(Dr,{ref:n,className:N("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600 min-h-[44px] min-w-[44px] flex items-center justify-center",e),"toast-close":"",...t,children:s.jsx(lr,{className:"h-4 w-4"})}));Lr.displayName=Dr.displayName;const Fr=l.forwardRef(({className:e,...t},n)=>s.jsx(Or,{ref:n,className:N("text-sm font-semibold",e),...t}));Fr.displayName=Or.displayName;const Ir=l.forwardRef(({className:e,...t},n)=>s.jsx(Rr,{ref:n,className:N("text-sm opacity-90",e),...t}));Ir.displayName=Rr.displayName;function Os(){const{toasts:e}=Ko();return s.jsxs(Ns,{children:[e.map(function({id:t,title:n,description:r,action:a,...o}){return s.jsxs(_r,{...o,children:[s.jsxs("div",{className:"grid gap-1",children:[n&&s.jsx(Fr,{children:n}),r&&s.jsx(Ir,{children:r})]}),a,s.jsx(Lr,{})]},t)}),s.jsx(Ar,{})]})}const Rt=768;function Wr(){const[e,t]=l.useState(void 0);return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Rt-1}px)`),r=()=>{t(window.innerWidth<Rt)};return n.addEventListener("change",r),t(window.innerWidth<Rt),()=>n.removeEventListener("change",r)},[]),!!e}const Rs=["top","right","bottom","left"],de=Math.min,X=Math.max,it=Math.round,qe=Math.floor,ue=e=>({x:e,y:e}),ks={left:"right",right:"left",bottom:"top",top:"bottom"},Ds={start:"end",end:"start"};function zt(e,t,n){return X(e,de(t,n))}function se(e,t){return typeof e=="function"?e(t):e}function ie(e){return e.split("-")[0]}function Ae(e){return e.split("-")[1]}function sn(e){return e==="x"?"y":"x"}function cn(e){return e==="y"?"height":"width"}function fe(e){return["top","bottom"].includes(ie(e))?"y":"x"}function ln(e){return sn(fe(e))}function As(e,t,n){n===void 0&&(n=!1);const r=Ae(e),a=ln(e),o=cn(a);let i=a==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=ct(i)),[i,ct(i)]}function _s(e){const t=ct(e);return[Xt(e),t,Xt(t)]}function Xt(e){return e.replace(/start|end/g,t=>Ds[t])}function Ls(e,t,n){const r=["left","right"],a=["right","left"],o=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?a:r:t?r:a;case"left":case"right":return t?o:i;default:return[]}}function Fs(e,t,n,r){const a=Ae(e);let o=Ls(ie(e),n==="start",r);return a&&(o=o.map(i=>i+"-"+a),t&&(o=o.concat(o.map(Xt)))),o}function ct(e){return e.replace(/left|right|bottom|top/g,t=>ks[t])}function Is(e){return{top:0,right:0,bottom:0,left:0,...e}}function $r(e){return typeof e!="number"?Is(e):{top:e,right:e,bottom:e,left:e}}function lt(e){const{x:t,y:n,width:r,height:a}=e;return{width:r,height:a,top:n,left:t,right:t+r,bottom:n+a,x:t,y:n}}function Dn(e,t,n){let{reference:r,floating:a}=e;const o=fe(t),i=ln(t),c=cn(i),u=ie(t),d=o==="y",f=r.x+r.width/2-a.width/2,p=r.y+r.height/2-a.height/2,v=r[c]/2-a[c]/2;let m;switch(u){case"top":m={x:f,y:r.y-a.height};break;case"bottom":m={x:f,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:p};break;case"left":m={x:r.x-a.width,y:p};break;default:m={x:r.x,y:r.y}}switch(Ae(t)){case"start":m[i]-=v*(n&&d?-1:1);break;case"end":m[i]+=v*(n&&d?-1:1);break}return m}const Ws=async(e,t,n)=>{const{placement:r="bottom",strategy:a="absolute",middleware:o=[],platform:i}=n,c=o.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let d=await i.getElementRects({reference:e,floating:t,strategy:a}),{x:f,y:p}=Dn(d,r,u),v=r,m={},x=0;for(let h=0;h<c.length;h++){const{name:g,fn:y}=c[h],{x:b,y:w,data:E,reset:S}=await y({x:f,y:p,initialPlacement:r,placement:v,strategy:a,middlewareData:m,rects:d,platform:i,elements:{reference:e,floating:t}});f=b??f,p=w??p,m={...m,[g]:{...m[g],...E}},S&&x<=50&&(x++,typeof S=="object"&&(S.placement&&(v=S.placement),S.rects&&(d=S.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:a}):S.rects),{x:f,y:p}=Dn(d,v,u)),h=-1)}return{x:f,y:p,placement:v,strategy:a,middlewareData:m}};async function We(e,t){var n;t===void 0&&(t={});const{x:r,y:a,platform:o,rects:i,elements:c,strategy:u}=e,{boundary:d="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:v=!1,padding:m=0}=se(t,e),x=$r(m),g=c[v?p==="floating"?"reference":"floating":p],y=lt(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(g)))==null||n?g:g.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(c.floating)),boundary:d,rootBoundary:f,strategy:u})),b=p==="floating"?{x:r,y:a,width:i.floating.width,height:i.floating.height}:i.reference,w=await(o.getOffsetParent==null?void 0:o.getOffsetParent(c.floating)),E=await(o.isElement==null?void 0:o.isElement(w))?await(o.getScale==null?void 0:o.getScale(w))||{x:1,y:1}:{x:1,y:1},S=lt(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:w,strategy:u}):b);return{top:(y.top-S.top+x.top)/E.y,bottom:(S.bottom-y.bottom+x.bottom)/E.y,left:(y.left-S.left+x.left)/E.x,right:(S.right-y.right+x.right)/E.x}}const $s=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:a,rects:o,platform:i,elements:c,middlewareData:u}=t,{element:d,padding:f=0}=se(e,t)||{};if(d==null)return{};const p=$r(f),v={x:n,y:r},m=ln(a),x=cn(m),h=await i.getDimensions(d),g=m==="y",y=g?"top":"left",b=g?"bottom":"right",w=g?"clientHeight":"clientWidth",E=o.reference[x]+o.reference[m]-v[m]-o.floating[x],S=v[m]-o.reference[m],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(d));let T=P?P[w]:0;(!T||!await(i.isElement==null?void 0:i.isElement(P)))&&(T=c.floating[w]||o.floating[x]);const j=E/2-S/2,k=T/2-h[x]/2-1,D=de(p[y],k),W=de(p[b],k),I=D,_=T-h[x]-W,C=T/2-h[x]/2+j,A=zt(I,C,_),R=!u.arrow&&Ae(a)!=null&&C!==A&&o.reference[x]/2-(C<I?D:W)-h[x]/2<0,L=R?C<I?C-I:C-_:0;return{[m]:v[m]+L,data:{[m]:A,centerOffset:C-A-L,...R&&{alignmentOffset:L}},reset:R}}}),Hs=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:a,middlewareData:o,rects:i,initialPlacement:c,platform:u,elements:d}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:h=!0,...g}=se(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const y=ie(a),b=fe(c),w=ie(c)===c,E=await(u.isRTL==null?void 0:u.isRTL(d.floating)),S=v||(w||!h?[ct(c)]:_s(c)),P=x!=="none";!v&&P&&S.push(...Fs(c,h,x,E));const T=[c,...S],j=await We(t,g),k=[];let D=((r=o.flip)==null?void 0:r.overflows)||[];if(f&&k.push(j[y]),p){const C=As(a,i,E);k.push(j[C[0]],j[C[1]])}if(D=[...D,{placement:a,overflows:k}],!k.every(C=>C<=0)){var W,I;const C=(((W=o.flip)==null?void 0:W.index)||0)+1,A=T[C];if(A)return{data:{index:C,overflows:D},reset:{placement:A}};let R=(I=D.filter(L=>L.overflows[0]<=0).sort((L,M)=>L.overflows[1]-M.overflows[1])[0])==null?void 0:I.placement;if(!R)switch(m){case"bestFit":{var _;const L=(_=D.filter(M=>{if(P){const $=fe(M.placement);return $===b||$==="y"}return!0}).map(M=>[M.placement,M.overflows.filter($=>$>0).reduce(($,G)=>$+G,0)]).sort((M,$)=>M[1]-$[1])[0])==null?void 0:_[0];L&&(R=L);break}case"initialPlacement":R=c;break}if(a!==R)return{reset:{placement:R}}}return{}}}};function An(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _n(e){return Rs.some(t=>e[t]>=0)}const Bs=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...a}=se(e,t);switch(r){case"referenceHidden":{const o=await We(t,{...a,elementContext:"reference"}),i=An(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:_n(i)}}}case"escaped":{const o=await We(t,{...a,altBoundary:!0}),i=An(o,n.floating);return{data:{escapedOffsets:i,escaped:_n(i)}}}default:return{}}}}};async function Ys(e,t){const{placement:n,platform:r,elements:a}=e,o=await(r.isRTL==null?void 0:r.isRTL(a.floating)),i=ie(n),c=Ae(n),u=fe(n)==="y",d=["left","top"].includes(i)?-1:1,f=o&&u?-1:1,p=se(t,e);let{mainAxis:v,crossAxis:m,alignmentAxis:x}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&typeof x=="number"&&(m=c==="end"?x*-1:x),u?{x:m*f,y:v*d}:{x:v*d,y:m*f}}const Vs=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:a,y:o,placement:i,middlewareData:c}=t,u=await Ys(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:a+u.x,y:o+u.y,data:{...u,placement:i}}}}},zs=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:a}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:c={fn:g=>{let{x:y,y:b}=g;return{x:y,y:b}}},...u}=se(e,t),d={x:n,y:r},f=await We(t,u),p=fe(ie(a)),v=sn(p);let m=d[v],x=d[p];if(o){const g=v==="y"?"top":"left",y=v==="y"?"bottom":"right",b=m+f[g],w=m-f[y];m=zt(b,m,w)}if(i){const g=p==="y"?"top":"left",y=p==="y"?"bottom":"right",b=x+f[g],w=x-f[y];x=zt(b,x,w)}const h=c.fn({...t,[v]:m,[p]:x});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[v]:o,[p]:i}}}}}},Xs=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:a,rects:o,middlewareData:i}=t,{offset:c=0,mainAxis:u=!0,crossAxis:d=!0}=se(e,t),f={x:n,y:r},p=fe(a),v=sn(p);let m=f[v],x=f[p];const h=se(c,t),g=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){const w=v==="y"?"height":"width",E=o.reference[v]-o.floating[w]+g.mainAxis,S=o.reference[v]+o.reference[w]-g.mainAxis;m<E?m=E:m>S&&(m=S)}if(d){var y,b;const w=v==="y"?"width":"height",E=["top","left"].includes(ie(a)),S=o.reference[p]-o.floating[w]+(E&&((y=i.offset)==null?void 0:y[p])||0)+(E?0:g.crossAxis),P=o.reference[p]+o.reference[w]+(E?0:((b=i.offset)==null?void 0:b[p])||0)-(E?g.crossAxis:0);x<S?x=S:x>P&&(x=P)}return{[v]:m,[p]:x}}}},qs=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:a,rects:o,platform:i,elements:c}=t,{apply:u=()=>{},...d}=se(e,t),f=await We(t,d),p=ie(a),v=Ae(a),m=fe(a)==="y",{width:x,height:h}=o.floating;let g,y;p==="top"||p==="bottom"?(g=p,y=v===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(y=p,g=v==="end"?"top":"bottom");const b=h-f.top-f.bottom,w=x-f.left-f.right,E=de(h-f[g],b),S=de(x-f[y],w),P=!t.middlewareData.shift;let T=E,j=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(j=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(T=b),P&&!v){const D=X(f.left,0),W=X(f.right,0),I=X(f.top,0),_=X(f.bottom,0);m?j=x-2*(D!==0||W!==0?D+W:X(f.left,f.right)):T=h-2*(I!==0||_!==0?I+_:X(f.top,f.bottom))}await u({...t,availableWidth:j,availableHeight:T});const k=await i.getDimensions(c.floating);return x!==k.width||h!==k.height?{reset:{rects:!0}}:{}}}};function xt(){return typeof window<"u"}function _e(e){return Hr(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function oe(e){var t;return(t=(Hr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Hr(e){return xt()?e instanceof Node||e instanceof q(e).Node:!1}function K(e){return xt()?e instanceof Element||e instanceof q(e).Element:!1}function ae(e){return xt()?e instanceof HTMLElement||e instanceof q(e).HTMLElement:!1}function Ln(e){return!xt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof q(e).ShadowRoot}function Ye(e){const{overflow:t,overflowX:n,overflowY:r,display:a}=Q(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(a)}function Gs(e){return["table","td","th"].includes(_e(e))}function bt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function dn(e){const t=un(),n=K(e)?Q(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Us(e){let t=he(e);for(;ae(t)&&!Re(t);){if(dn(t))return t;if(bt(t))return null;t=he(t)}return null}function un(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Re(e){return["html","body","#document"].includes(_e(e))}function Q(e){return q(e).getComputedStyle(e)}function wt(e){return K(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function he(e){if(_e(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ln(e)&&e.host||oe(e);return Ln(t)?t.host:t}function Br(e){const t=he(e);return Re(t)?e.ownerDocument?e.ownerDocument.body:e.body:ae(t)&&Ye(t)?t:Br(t)}function $e(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const a=Br(e),o=a===((r=e.ownerDocument)==null?void 0:r.body),i=q(a);if(o){const c=qt(i);return t.concat(i,i.visualViewport||[],Ye(a)?a:[],c&&n?$e(c):[])}return t.concat(a,$e(a,[],n))}function qt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Yr(e){const t=Q(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const a=ae(e),o=a?e.offsetWidth:n,i=a?e.offsetHeight:r,c=it(n)!==o||it(r)!==i;return c&&(n=o,r=i),{width:n,height:r,$:c}}function fn(e){return K(e)?e:e.contextElement}function Me(e){const t=fn(e);if(!ae(t))return ue(1);const n=t.getBoundingClientRect(),{width:r,height:a,$:o}=Yr(t);let i=(o?it(n.width):n.width)/r,c=(o?it(n.height):n.height)/a;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const Ks=ue(0);function Vr(e){const t=q(e);return!un()||!t.visualViewport?Ks:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Qs(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==q(e)?!1:t}function be(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const a=e.getBoundingClientRect(),o=fn(e);let i=ue(1);t&&(r?K(r)&&(i=Me(r)):i=Me(e));const c=Qs(o,n,r)?Vr(o):ue(0);let u=(a.left+c.x)/i.x,d=(a.top+c.y)/i.y,f=a.width/i.x,p=a.height/i.y;if(o){const v=q(o),m=r&&K(r)?q(r):r;let x=v,h=qt(x);for(;h&&r&&m!==x;){const g=Me(h),y=h.getBoundingClientRect(),b=Q(h),w=y.left+(h.clientLeft+parseFloat(b.paddingLeft))*g.x,E=y.top+(h.clientTop+parseFloat(b.paddingTop))*g.y;u*=g.x,d*=g.y,f*=g.x,p*=g.y,u+=w,d+=E,x=q(h),h=qt(x)}}return lt({width:f,height:p,x:u,y:d})}function Js(e){let{elements:t,rect:n,offsetParent:r,strategy:a}=e;const o=a==="fixed",i=oe(r),c=t?bt(t.floating):!1;if(r===i||c&&o)return n;let u={scrollLeft:0,scrollTop:0},d=ue(1);const f=ue(0),p=ae(r);if((p||!p&&!o)&&((_e(r)!=="body"||Ye(i))&&(u=wt(r)),ae(r))){const v=be(r);d=Me(r),f.x=v.x+r.clientLeft,f.y=v.y+r.clientTop}return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-u.scrollLeft*d.x+f.x,y:n.y*d.y-u.scrollTop*d.y+f.y}}function Zs(e){return Array.from(e.getClientRects())}function Gt(e,t){const n=wt(e).scrollLeft;return t?t.left+n:be(oe(e)).left+n}function ei(e){const t=oe(e),n=wt(e),r=e.ownerDocument.body,a=X(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=X(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Gt(e);const c=-n.scrollTop;return Q(r).direction==="rtl"&&(i+=X(t.clientWidth,r.clientWidth)-a),{width:a,height:o,x:i,y:c}}function ti(e,t){const n=q(e),r=oe(e),a=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,c=0,u=0;if(a){o=a.width,i=a.height;const d=un();(!d||d&&t==="fixed")&&(c=a.offsetLeft,u=a.offsetTop)}return{width:o,height:i,x:c,y:u}}function ni(e,t){const n=be(e,!0,t==="fixed"),r=n.top+e.clientTop,a=n.left+e.clientLeft,o=ae(e)?Me(e):ue(1),i=e.clientWidth*o.x,c=e.clientHeight*o.y,u=a*o.x,d=r*o.y;return{width:i,height:c,x:u,y:d}}function Fn(e,t,n){let r;if(t==="viewport")r=ti(e,n);else if(t==="document")r=ei(oe(e));else if(K(t))r=ni(t,n);else{const a=Vr(e);r={...t,x:t.x-a.x,y:t.y-a.y}}return lt(r)}function zr(e,t){const n=he(e);return n===t||!K(n)||Re(n)?!1:Q(n).position==="fixed"||zr(n,t)}function ri(e,t){const n=t.get(e);if(n)return n;let r=$e(e,[],!1).filter(c=>K(c)&&_e(c)!=="body"),a=null;const o=Q(e).position==="fixed";let i=o?he(e):e;for(;K(i)&&!Re(i);){const c=Q(i),u=dn(i);!u&&c.position==="fixed"&&(a=null),(o?!u&&!a:!u&&c.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||Ye(i)&&!u&&zr(e,i))?r=r.filter(f=>f!==i):a=c,i=he(i)}return t.set(e,r),r}function ai(e){let{element:t,boundary:n,rootBoundary:r,strategy:a}=e;const i=[...n==="clippingAncestors"?bt(t)?[]:ri(t,this._c):[].concat(n),r],c=i[0],u=i.reduce((d,f)=>{const p=Fn(t,f,a);return d.top=X(p.top,d.top),d.right=de(p.right,d.right),d.bottom=de(p.bottom,d.bottom),d.left=X(p.left,d.left),d},Fn(t,c,a));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function oi(e){const{width:t,height:n}=Yr(e);return{width:t,height:n}}function si(e,t,n){const r=ae(t),a=oe(t),o=n==="fixed",i=be(e,!0,o,t);let c={scrollLeft:0,scrollTop:0};const u=ue(0);if(r||!r&&!o)if((_e(t)!=="body"||Ye(a))&&(c=wt(t)),r){const m=be(t,!0,o,t);u.x=m.x+t.clientLeft,u.y=m.y+t.clientTop}else a&&(u.x=Gt(a));let d=0,f=0;if(a&&!r&&!o){const m=a.getBoundingClientRect();f=m.top+c.scrollTop,d=m.left+c.scrollLeft-Gt(a,m)}const p=i.left+c.scrollLeft-u.x-d,v=i.top+c.scrollTop-u.y-f;return{x:p,y:v,width:i.width,height:i.height}}function kt(e){return Q(e).position==="static"}function In(e,t){if(!ae(e)||Q(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return oe(e)===n&&(n=n.ownerDocument.body),n}function Xr(e,t){const n=q(e);if(bt(e))return n;if(!ae(e)){let a=he(e);for(;a&&!Re(a);){if(K(a)&&!kt(a))return a;a=he(a)}return n}let r=In(e,t);for(;r&&Gs(r)&&kt(r);)r=In(r,t);return r&&Re(r)&&kt(r)&&!dn(r)?n:r||Us(e)||n}const ii=async function(e){const t=this.getOffsetParent||Xr,n=this.getDimensions,r=await n(e.floating);return{reference:si(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ci(e){return Q(e).direction==="rtl"}const li={convertOffsetParentRelativeRectToViewportRelativeRect:Js,getDocumentElement:oe,getClippingRect:ai,getOffsetParent:Xr,getElementRects:ii,getClientRects:Zs,getDimensions:oi,getScale:Me,isElement:K,isRTL:ci};function di(e,t){let n=null,r;const a=oe(e);function o(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,u){c===void 0&&(c=!1),u===void 0&&(u=1),o();const{left:d,top:f,width:p,height:v}=e.getBoundingClientRect();if(c||t(),!p||!v)return;const m=qe(f),x=qe(a.clientWidth-(d+p)),h=qe(a.clientHeight-(f+v)),g=qe(d),b={rootMargin:-m+"px "+-x+"px "+-h+"px "+-g+"px",threshold:X(0,de(1,u))||1};let w=!0;function E(S){const P=S[0].intersectionRatio;if(P!==u){if(!w)return i();P?i(!1,P):r=setTimeout(()=>{i(!1,1e-7)},1e3)}w=!1}try{n=new IntersectionObserver(E,{...b,root:a.ownerDocument})}catch{n=new IntersectionObserver(E,b)}n.observe(e)}return i(!0),o}function ui(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:a=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,d=fn(e),f=a||o?[...d?$e(d):[],...$e(t)]:[];f.forEach(y=>{a&&y.addEventListener("scroll",n,{passive:!0}),o&&y.addEventListener("resize",n)});const p=d&&c?di(d,n):null;let v=-1,m=null;i&&(m=new ResizeObserver(y=>{let[b]=y;b&&b.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var w;(w=m)==null||w.observe(t)})),n()}),d&&!u&&m.observe(d),m.observe(t));let x,h=u?be(e):null;u&&g();function g(){const y=be(e);h&&(y.x!==h.x||y.y!==h.y||y.width!==h.width||y.height!==h.height)&&n(),h=y,x=requestAnimationFrame(g)}return n(),()=>{var y;f.forEach(b=>{a&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),p==null||p(),(y=m)==null||y.disconnect(),m=null,u&&cancelAnimationFrame(x)}}const fi=Vs,hi=zs,mi=Hs,pi=qs,gi=Bs,Wn=$s,vi=Xs,xi=(e,t,n)=>{const r=new Map,a={platform:li,...n},o={...a.platform,_c:r};return Ws(e,t,{...a,platform:o})};var rt=typeof document<"u"?l.useLayoutEffect:l.useEffect;function dt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,a;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!dt(e[r],t[r]))return!1;return!0}if(a=Object.keys(e),n=a.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,a[r]))return!1;for(r=n;r--!==0;){const o=a[r];if(!(o==="_owner"&&e.$$typeof)&&!dt(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function qr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function $n(e,t){const n=qr(e);return Math.round(t*n)/n}function Dt(e){const t=l.useRef(e);return rt(()=>{t.current=e}),t}function bi(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:a,elements:{reference:o,floating:i}={},transform:c=!0,whileElementsMounted:u,open:d}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,m]=l.useState(r);dt(v,r)||m(r);const[x,h]=l.useState(null),[g,y]=l.useState(null),b=l.useCallback(M=>{M!==P.current&&(P.current=M,h(M))},[]),w=l.useCallback(M=>{M!==T.current&&(T.current=M,y(M))},[]),E=o||x,S=i||g,P=l.useRef(null),T=l.useRef(null),j=l.useRef(f),k=u!=null,D=Dt(u),W=Dt(a),I=Dt(d),_=l.useCallback(()=>{if(!P.current||!T.current)return;const M={placement:t,strategy:n,middleware:v};W.current&&(M.platform=W.current),xi(P.current,T.current,M).then($=>{const G={...$,isPositioned:I.current!==!1};C.current&&!dt(j.current,G)&&(j.current=G,sr.flushSync(()=>{p(G)}))})},[v,t,n,W,I]);rt(()=>{d===!1&&j.current.isPositioned&&(j.current.isPositioned=!1,p(M=>({...M,isPositioned:!1})))},[d]);const C=l.useRef(!1);rt(()=>(C.current=!0,()=>{C.current=!1}),[]),rt(()=>{if(E&&(P.current=E),S&&(T.current=S),E&&S){if(D.current)return D.current(E,S,_);_()}},[E,S,_,D,k]);const A=l.useMemo(()=>({reference:P,floating:T,setReference:b,setFloating:w}),[b,w]),R=l.useMemo(()=>({reference:E,floating:S}),[E,S]),L=l.useMemo(()=>{const M={position:n,left:0,top:0};if(!R.floating)return M;const $=$n(R.floating,f.x),G=$n(R.floating,f.y);return c?{...M,transform:"translate("+$+"px, "+G+"px)",...qr(R.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:$,top:G}},[n,c,R.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:_,refs:A,elements:R,floatingStyles:L}),[f,_,A,R,L])}const wi=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:a}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Wn({element:r.current,padding:a}).fn(n):{}:r?Wn({element:r,padding:a}).fn(n):{}}}},yi=(e,t)=>({...fi(e),options:[e,t]}),Ei=(e,t)=>({...hi(e),options:[e,t]}),Si=(e,t)=>({...vi(e),options:[e,t]}),Ci=(e,t)=>({...mi(e),options:[e,t]}),Ti=(e,t)=>({...pi(e),options:[e,t]}),Pi=(e,t)=>({...gi(e),options:[e,t]}),Ni=(e,t)=>({...wi(e),options:[e,t]});var Mi="Arrow",Gr=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:a=5,...o}=e;return s.jsx(H.svg,{...o,ref:t,width:r,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});Gr.displayName=Mi;var ji=Gr;function Oi(e,t=[]){let n=[];function r(o,i){const c=l.createContext(i),u=n.length;n=[...n,i];function d(p){const{scope:v,children:m,...x}=p,h=(v==null?void 0:v[e][u])||c,g=l.useMemo(()=>x,Object.values(x));return s.jsx(h.Provider,{value:g,children:m})}function f(p,v){const m=(v==null?void 0:v[e][u])||c,x=l.useContext(m);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${o}\``)}return d.displayName=o+"Provider",[d,f]}const a=()=>{const o=n.map(i=>l.createContext(i));return function(c){const u=(c==null?void 0:c[e])||o;return l.useMemo(()=>({[`__scope${e}`]:{...c,[e]:u}}),[c,u])}};return a.scopeName=e,[r,Ri(a,...t)]}function Ri(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(o){const i=r.reduce((c,{useScope:u,scopeName:d})=>{const p=u(o)[`__scope${d}`];return{...c,...p}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var hn="Popper",[Ur,Kr]=Oi(hn),[ki,Qr]=Ur(hn),Jr=e=>{const{__scopePopper:t,children:n}=e,[r,a]=l.useState(null);return s.jsx(ki,{scope:t,anchor:r,onAnchorChange:a,children:n})};Jr.displayName=hn;var Zr="PopperAnchor",ea=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...a}=e,o=Qr(Zr,n),i=l.useRef(null),c=U(t,i);return l.useEffect(()=>{o.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:s.jsx(H.div,{...a,ref:c})});ea.displayName=Zr;var mn="PopperContent",[Di,Ai]=Ur(mn),ta=l.forwardRef((e,t)=>{var wn,yn,En,Sn,Cn,Tn;const{__scopePopper:n,side:r="bottom",sideOffset:a=0,align:o="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:v=!1,updatePositionStrategy:m="optimized",onPlaced:x,...h}=e,g=Qr(mn,n),[y,b]=l.useState(null),w=U(t,Fe=>b(Fe)),[E,S]=l.useState(null),P=Mo(E),T=(P==null?void 0:P.width)??0,j=(P==null?void 0:P.height)??0,k=r+(o!=="center"?"-"+o:""),D=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},W=Array.isArray(d)?d:[d],I=W.length>0,_={padding:D,boundary:W.filter(Li),altBoundary:I},{refs:C,floatingStyles:A,placement:R,isPositioned:L,middlewareData:M}=bi({strategy:"fixed",placement:k,whileElementsMounted:(...Fe)=>ui(...Fe,{animationFrame:m==="always"}),elements:{reference:g.anchor},middleware:[yi({mainAxis:a+j,alignmentAxis:i}),u&&Ei({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Si():void 0,..._}),u&&Ci({..._}),Ti({..._,apply:({elements:Fe,rects:Pn,availableWidth:ho,availableHeight:mo})=>{const{width:po,height:go}=Pn.reference,ze=Fe.floating.style;ze.setProperty("--radix-popper-available-width",`${ho}px`),ze.setProperty("--radix-popper-available-height",`${mo}px`),ze.setProperty("--radix-popper-anchor-width",`${po}px`),ze.setProperty("--radix-popper-anchor-height",`${go}px`)}}),E&&Ni({element:E,padding:c}),Fi({arrowWidth:T,arrowHeight:j}),v&&Pi({strategy:"referenceHidden",..._})]}),[$,G]=aa(R),ye=re(x);st(()=>{L&&(ye==null||ye())},[L,ye]);const Le=(wn=M.arrow)==null?void 0:wn.x,Ee=(yn=M.arrow)==null?void 0:yn.y,Ve=((En=M.arrow)==null?void 0:En.centerOffset)!==0,[uo,fo]=l.useState();return st(()=>{y&&fo(window.getComputedStyle(y).zIndex)},[y]),s.jsx("div",{ref:C.setFloating,"data-radix-popper-content-wrapper":"",style:{...A,transform:L?A.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:uo,"--radix-popper-transform-origin":[(Sn=M.transformOrigin)==null?void 0:Sn.x,(Cn=M.transformOrigin)==null?void 0:Cn.y].join(" "),...((Tn=M.hide)==null?void 0:Tn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(Di,{scope:n,placedSide:$,onArrowChange:S,arrowX:Le,arrowY:Ee,shouldHideArrow:Ve,children:s.jsx(H.div,{"data-side":$,"data-align":G,...h,ref:w,style:{...h.style,animation:L?void 0:"none"}})})})});ta.displayName=mn;var na="PopperArrow",_i={top:"bottom",right:"left",bottom:"top",left:"right"},ra=l.forwardRef(function(t,n){const{__scopePopper:r,...a}=t,o=Ai(na,r),i=_i[o.placedSide];return s.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:s.jsx(ji,{...a,ref:n,style:{...a.style,display:"block"}})})});ra.displayName=na;function Li(e){return e!==null}var Fi=e=>({name:"transformOrigin",options:e,fn(t){var g,y,b;const{placement:n,rects:r,middlewareData:a}=t,i=((g=a.arrow)==null?void 0:g.centerOffset)!==0,c=i?0:e.arrowWidth,u=i?0:e.arrowHeight,[d,f]=aa(n),p={start:"0%",center:"50%",end:"100%"}[f],v=(((y=a.arrow)==null?void 0:y.x)??0)+c/2,m=(((b=a.arrow)==null?void 0:b.y)??0)+u/2;let x="",h="";return d==="bottom"?(x=i?p:`${v}px`,h=`${-u}px`):d==="top"?(x=i?p:`${v}px`,h=`${r.floating.height+u}px`):d==="right"?(x=`${-u}px`,h=i?p:`${m}px`):d==="left"&&(x=`${r.floating.width+u}px`,h=i?p:`${m}px`),{data:{x,y:h}}}});function aa(e){const[t,n="center"]=e.split("-");return[t,n]}var Ii=Jr,Wi=ea,$i=ta,Hi=ra,[yt,ju]=en("Tooltip",[Kr]),Et=Kr(),oa="TooltipProvider",Bi=700,Ut="tooltip.open",[Yi,pn]=yt(oa),sa=e=>{const{__scopeTooltip:t,delayDuration:n=Bi,skipDelayDuration:r=300,disableHoverableContent:a=!1,children:o}=e,[i,c]=l.useState(!0),u=l.useRef(!1),d=l.useRef(0);return l.useEffect(()=>{const f=d.current;return()=>window.clearTimeout(f)},[]),s.jsx(Yi,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(d.current),c(!1)},[]),onClose:l.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:l.useCallback(f=>{u.current=f},[]),disableHoverableContent:a,children:o})};sa.displayName=oa;var St="Tooltip",[Vi,Ct]=yt(St),ia=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:a=!1,onOpenChange:o,disableHoverableContent:i,delayDuration:c}=e,u=pn(St,e.__scopeTooltip),d=Et(t),[f,p]=l.useState(null),v=et(),m=l.useRef(0),x=i??u.disableHoverableContent,h=c??u.delayDuration,g=l.useRef(!1),[y=!1,b]=tn({prop:r,defaultProp:a,onChange:T=>{T?(u.onOpen(),document.dispatchEvent(new CustomEvent(Ut))):u.onClose(),o==null||o(T)}}),w=l.useMemo(()=>y?g.current?"delayed-open":"instant-open":"closed",[y]),E=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,b(!0)},[b]),S=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),P=l.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,b(!0),m.current=0},h)},[h,b]);return l.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),s.jsx(Ii,{...d,children:s.jsx(Vi,{scope:t,contentId:v,open:y,stateAttribute:w,trigger:f,onTriggerChange:p,onTriggerEnter:l.useCallback(()=>{u.isOpenDelayed?P():E()},[u.isOpenDelayed,P,E]),onTriggerLeave:l.useCallback(()=>{x?S():(window.clearTimeout(m.current),m.current=0)},[S,x]),onOpen:E,onClose:S,disableHoverableContent:x,children:n})})};ia.displayName=St;var Kt="TooltipTrigger",ca=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,a=Ct(Kt,n),o=pn(Kt,n),i=Et(n),c=l.useRef(null),u=U(t,c,a.onTriggerChange),d=l.useRef(!1),f=l.useRef(!1),p=l.useCallback(()=>d.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),s.jsx(Wi,{asChild:!0,...i,children:s.jsx(H.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...r,ref:u,onPointerMove:F(e.onPointerMove,v=>{v.pointerType!=="touch"&&!f.current&&!o.isPointerInTransitRef.current&&(a.onTriggerEnter(),f.current=!0)}),onPointerLeave:F(e.onPointerLeave,()=>{a.onTriggerLeave(),f.current=!1}),onPointerDown:F(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:F(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:F(e.onBlur,a.onClose),onClick:F(e.onClick,a.onClose)})})});ca.displayName=Kt;var zi="TooltipPortal",[Ou,Xi]=yt(zi,{forceMount:void 0}),ke="TooltipContent",la=l.forwardRef((e,t)=>{const n=Xi(ke,e.__scopeTooltip),{forceMount:r=n.forceMount,side:a="top",...o}=e,i=Ct(ke,e.__scopeTooltip);return s.jsx(Be,{present:r||i.open,children:i.disableHoverableContent?s.jsx(da,{side:a,...o,ref:t}):s.jsx(qi,{side:a,...o,ref:t})})}),qi=l.forwardRef((e,t)=>{const n=Ct(ke,e.__scopeTooltip),r=pn(ke,e.__scopeTooltip),a=l.useRef(null),o=U(t,a),[i,c]=l.useState(null),{trigger:u,onClose:d}=n,f=a.current,{onPointerInTransitChange:p}=r,v=l.useCallback(()=>{c(null),p(!1)},[p]),m=l.useCallback((x,h)=>{const g=x.currentTarget,y={x:x.clientX,y:x.clientY},b=Qi(y,g.getBoundingClientRect()),w=Ji(y,b),E=Zi(h.getBoundingClientRect()),S=tc([...w,...E]);c(S),p(!0)},[p]);return l.useEffect(()=>()=>v(),[v]),l.useEffect(()=>{if(u&&f){const x=g=>m(g,f),h=g=>m(g,u);return u.addEventListener("pointerleave",x),f.addEventListener("pointerleave",h),()=>{u.removeEventListener("pointerleave",x),f.removeEventListener("pointerleave",h)}}},[u,f,m,v]),l.useEffect(()=>{if(i){const x=h=>{const g=h.target,y={x:h.clientX,y:h.clientY},b=(u==null?void 0:u.contains(g))||(f==null?void 0:f.contains(g)),w=!ec(y,i);b?v():w&&(v(),d())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[u,f,i,d,v]),s.jsx(da,{...e,ref:o})}),[Gi,Ui]=yt(St,{isInside:!1}),da=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":a,onEscapeKeyDown:o,onPointerDownOutside:i,...c}=e,u=Ct(ke,n),d=Et(n),{onClose:f}=u;return l.useEffect(()=>(document.addEventListener(Ut,f),()=>document.removeEventListener(Ut,f)),[f]),l.useEffect(()=>{if(u.trigger){const p=v=>{const m=v.target;m!=null&&m.contains(u.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[u.trigger,f]),s.jsx(mt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:s.jsxs($i,{"data-state":u.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(xo,{children:r}),s.jsx(Gi,{scope:n,isInside:!0,children:s.jsx(cs,{id:u.contentId,role:"tooltip",children:a||r})})]})})});la.displayName=ke;var ua="TooltipArrow",Ki=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,a=Et(n);return Ui(ua,n).isInside?null:s.jsx(Hi,{...a,...r,ref:t})});Ki.displayName=ua;function Qi(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,a,o)){case o:return"left";case a:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Ji(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Zi(e){const{top:t,right:n,bottom:r,left:a}=e;return[{x:a,y:t},{x:n,y:t},{x:n,y:r},{x:a,y:r}]}function ec(e,t){const{x:n,y:r}=e;let a=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const c=t[o].x,u=t[o].y,d=t[i].x,f=t[i].y;u>r!=f>r&&n<(d-c)*(r-u)/(f-u)+c&&(a=!a)}return a}function tc(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),nc(t)}function nc(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const a=e[r];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(a.y-i.y)>=(o.y-i.y)*(a.x-i.x))t.pop();else break}t.push(a)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const a=e[r];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(a.y-i.y)>=(o.y-i.y)*(a.x-i.x))n.pop();else break}n.push(a)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var rc=sa,ac=ia,oc=ca,fa=la;const sc=rc,ic=ac,cc=oc,ha=l.forwardRef(({className:e,sideOffset:t=4,...n},r)=>s.jsx(fa,{ref:r,sideOffset:t,className:N("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));ha.displayName=fa.displayName;const lc="sidebar:state",dc=60*60*24*7,uc="16rem",fc="3rem",hc="b",ma=l.createContext(null);function Tt(){const e=l.useContext(ma);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const pa=l.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:a,children:o,...i},c)=>{const u=Wr(),[d,f]=l.useState(!1),[p,v]=l.useState(e),m=t??p,x=l.useCallback(b=>{const w=typeof b=="function"?b(m):b;n?n(w):v(w),document.cookie=`${lc}=${w}; path=/; max-age=${dc}`},[n,m]),h=l.useCallback(()=>u?f(b=>!b):x(b=>!b),[u,x,f]);l.useEffect(()=>{const b=w=>{w.key===hc&&(w.metaKey||w.ctrlKey)&&(w.preventDefault(),h())};return window.addEventListener("keydown",b),()=>window.removeEventListener("keydown",b)},[h]);const g=m?"expanded":"collapsed",y=l.useMemo(()=>({state:g,open:m,setOpen:x,isMobile:u,openMobile:d,setOpenMobile:f,toggleSidebar:h}),[g,m,x,u,d,f,h]);return s.jsx(ma.Provider,{value:y,children:s.jsx(sc,{delayDuration:0,children:s.jsx("div",{style:{"--sidebar-width":uc,"--sidebar-width-icon":fc,...a},className:N("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:c,...i,children:o})})})});pa.displayName="SidebarProvider";var At="focusScope.autoFocusOnMount",_t="focusScope.autoFocusOnUnmount",Hn={bubbles:!1,cancelable:!0},mc="FocusScope",ga=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:o,...i}=e,[c,u]=l.useState(null),d=re(a),f=re(o),p=l.useRef(null),v=U(t,h=>u(h)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let h=function(w){if(m.paused||!c)return;const E=w.target;c.contains(E)?p.current=E:le(p.current,{select:!0})},g=function(w){if(m.paused||!c)return;const E=w.relatedTarget;E!==null&&(c.contains(E)||le(p.current,{select:!0}))},y=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&le(c)};document.addEventListener("focusin",h),document.addEventListener("focusout",g);const b=new MutationObserver(y);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",g),b.disconnect()}}},[r,c,m.paused]),l.useEffect(()=>{if(c){Yn.add(m);const h=document.activeElement;if(!c.contains(h)){const y=new CustomEvent(At,Hn);c.addEventListener(At,d),c.dispatchEvent(y),y.defaultPrevented||(pc(wc(va(c)),{select:!0}),document.activeElement===h&&le(c))}return()=>{c.removeEventListener(At,d),setTimeout(()=>{const y=new CustomEvent(_t,Hn);c.addEventListener(_t,f),c.dispatchEvent(y),y.defaultPrevented||le(h??document.body,{select:!0}),c.removeEventListener(_t,f),Yn.remove(m)},0)}}},[c,d,f,m]);const x=l.useCallback(h=>{if(!n&&!r||m.paused)return;const g=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,y=document.activeElement;if(g&&y){const b=h.currentTarget,[w,E]=gc(b);w&&E?!h.shiftKey&&y===E?(h.preventDefault(),n&&le(w,{select:!0})):h.shiftKey&&y===w&&(h.preventDefault(),n&&le(E,{select:!0})):y===b&&h.preventDefault()}},[n,r,m.paused]);return s.jsx(H.div,{tabIndex:-1,...i,ref:v,onKeyDown:x})});ga.displayName=mc;function pc(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(le(r,{select:t}),document.activeElement!==n)return}function gc(e){const t=va(e),n=Bn(t,e),r=Bn(t.reverse(),e);return[n,r]}function va(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const a=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||a?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Bn(e,t){for(const n of e)if(!vc(n,{upTo:t}))return n}function vc(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function xc(e){return e instanceof HTMLInputElement&&"select"in e}function le(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&xc(e)&&t&&e.select()}}var Yn=bc();function bc(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Vn(e,t),e.unshift(t)},remove(t){var n;e=Vn(e,t),(n=e[0])==null||n.resume()}}}function Vn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function wc(e){return e.filter(t=>t.tagName!=="A")}var Lt=0;function yc(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??zn()),document.body.insertAdjacentElement("beforeend",e[1]??zn()),Lt++,()=>{Lt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Lt--}},[])}function zn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ee=function(){return ee=Object.assign||function(t){for(var n,r=1,a=arguments.length;r<a;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ee.apply(this,arguments)};function xa(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n}function Ec(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,o;r<a;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var at="right-scroll-bar-position",ot="width-before-scroll-bar",Sc="with-scroll-bars-hidden",Cc="--removed-body-scroll-bar-size";function Ft(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Tc(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var a=n.value;a!==r&&(n.value=r,n.callback(r,a))}}}})[0];return n.callback=t,n.facade}var Pc=typeof window<"u"?l.useLayoutEffect:l.useEffect,Xn=new WeakMap;function Nc(e,t){var n=Tc(null,function(r){return e.forEach(function(a){return Ft(a,r)})});return Pc(function(){var r=Xn.get(n);if(r){var a=new Set(r),o=new Set(e),i=n.current;a.forEach(function(c){o.has(c)||Ft(c,null)}),o.forEach(function(c){a.has(c)||Ft(c,i)})}Xn.set(n,e)},[e]),n}function Mc(e){return e}function jc(e,t){t===void 0&&(t=Mc);var n=[],r=!1,a={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(o){var i=t(o,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(o){for(r=!0;n.length;){var i=n;n=[],i.forEach(o)}n={push:function(c){return o(c)},filter:function(){return n}}},assignMedium:function(o){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(o),i=n}var u=function(){var f=i;i=[],f.forEach(o)},d=function(){return Promise.resolve().then(u)};d(),n={push:function(f){i.push(f),d()},filter:function(f){return i=i.filter(f),n}}}};return a}function Oc(e){e===void 0&&(e={});var t=jc(null);return t.options=ee({async:!0,ssr:!1},e),t}var ba=function(e){var t=e.sideCar,n=xa(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,ee({},n))};ba.isSideCarExport=!0;function Rc(e,t){return e.useMedium(t),ba}var wa=Oc(),It=function(){},Pt=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:It,onWheelCapture:It,onTouchMoveCapture:It}),a=r[0],o=r[1],i=e.forwardProps,c=e.children,u=e.className,d=e.removeScrollBar,f=e.enabled,p=e.shards,v=e.sideCar,m=e.noIsolation,x=e.inert,h=e.allowPinchZoom,g=e.as,y=g===void 0?"div":g,b=e.gapMode,w=xa(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=v,S=Nc([n,t]),P=ee(ee({},w),a);return l.createElement(l.Fragment,null,f&&l.createElement(E,{sideCar:wa,removeScrollBar:d,shards:p,noIsolation:m,inert:x,setCallbacks:o,allowPinchZoom:!!h,lockRef:n,gapMode:b}),i?l.cloneElement(l.Children.only(c),ee(ee({},P),{ref:S})):l.createElement(y,ee({},P,{className:u,ref:S}),c))});Pt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Pt.classNames={fullWidth:ot,zeroRight:at};var kc=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Dc(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=kc();return t&&e.setAttribute("nonce",t),e}function Ac(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function _c(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Lc=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Dc())&&(Ac(t,n),_c(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Fc=function(){var e=Lc();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ya=function(){var e=Fc(),t=function(n){var r=n.styles,a=n.dynamic;return e(r,a),null};return t},Ic={left:0,top:0,right:0,gap:0},Wt=function(e){return parseInt(e||"",10)||0},Wc=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],a=t[e==="padding"?"paddingRight":"marginRight"];return[Wt(n),Wt(r),Wt(a)]},$c=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ic;var t=Wc(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Hc=ya(),je="data-scroll-locked",Bc=function(e,t,n,r){var a=e.left,o=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Sc,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(je,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(at,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(ot,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(at," .").concat(at,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ot," .").concat(ot,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(je,`] {
    `).concat(Cc,": ").concat(c,`px;
  }
`)},qn=function(){var e=parseInt(document.body.getAttribute(je)||"0",10);return isFinite(e)?e:0},Yc=function(){l.useEffect(function(){return document.body.setAttribute(je,(qn()+1).toString()),function(){var e=qn()-1;e<=0?document.body.removeAttribute(je):document.body.setAttribute(je,e.toString())}},[])},Vc=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=r===void 0?"margin":r;Yc();var o=l.useMemo(function(){return $c(a)},[a]);return l.createElement(Hc,{styles:Bc(o,!t,a,n?"":"!important")})},Qt=!1;if(typeof window<"u")try{var Ge=Object.defineProperty({},"passive",{get:function(){return Qt=!0,!0}});window.addEventListener("test",Ge,Ge),window.removeEventListener("test",Ge,Ge)}catch{Qt=!1}var Se=Qt?{passive:!1}:!1,zc=function(e){return e.tagName==="TEXTAREA"},Ea=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!zc(e)&&n[t]==="visible")},Xc=function(e){return Ea(e,"overflowY")},qc=function(e){return Ea(e,"overflowX")},Gn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var a=Sa(e,r);if(a){var o=Ca(e,r),i=o[1],c=o[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Gc=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Uc=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Sa=function(e,t){return e==="v"?Xc(t):qc(t)},Ca=function(e,t){return e==="v"?Gc(t):Uc(t)},Kc=function(e,t){return e==="h"&&t==="rtl"?-1:1},Qc=function(e,t,n,r,a){var o=Kc(e,window.getComputedStyle(t).direction),i=o*r,c=n.target,u=t.contains(c),d=!1,f=i>0,p=0,v=0;do{var m=Ca(e,c),x=m[0],h=m[1],g=m[2],y=h-g-o*x;(x||y)&&Sa(e,c)&&(p+=y,v+=x),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return(f&&(Math.abs(p)<1||!a)||!f&&(Math.abs(v)<1||!a))&&(d=!0),d},Ue=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Un=function(e){return[e.deltaX,e.deltaY]},Kn=function(e){return e&&"current"in e?e.current:e},Jc=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Zc=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},el=0,Ce=[];function tl(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),a=l.useState(el++)[0],o=l.useState(ya)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var h=Ec([e.lockRef.current],(e.shards||[]).map(Kn),!0).filter(Boolean);return h.forEach(function(g){return g.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),h.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(h,g){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!i.current.allowPinchZoom;var y=Ue(h),b=n.current,w="deltaX"in h?h.deltaX:b[0]-y[0],E="deltaY"in h?h.deltaY:b[1]-y[1],S,P=h.target,T=Math.abs(w)>Math.abs(E)?"h":"v";if("touches"in h&&T==="h"&&P.type==="range")return!1;var j=Gn(T,P);if(!j)return!0;if(j?S=T:(S=T==="v"?"h":"v",j=Gn(T,P)),!j)return!1;if(!r.current&&"changedTouches"in h&&(w||E)&&(r.current=S),!S)return!0;var k=r.current||S;return Qc(k,g,h,k==="h"?w:E,!0)},[]),u=l.useCallback(function(h){var g=h;if(!(!Ce.length||Ce[Ce.length-1]!==o)){var y="deltaY"in g?Un(g):Ue(g),b=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&Jc(S.delta,y)})[0];if(b&&b.should){g.cancelable&&g.preventDefault();return}if(!b){var w=(i.current.shards||[]).map(Kn).filter(Boolean).filter(function(S){return S.contains(g.target)}),E=w.length>0?c(g,w[0]):!i.current.noIsolation;E&&g.cancelable&&g.preventDefault()}}},[]),d=l.useCallback(function(h,g,y,b){var w={name:h,delta:g,target:y,should:b,shadowParent:nl(y)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(E){return E!==w})},1)},[]),f=l.useCallback(function(h){n.current=Ue(h),r.current=void 0},[]),p=l.useCallback(function(h){d(h.type,Un(h),h.target,c(h,e.lockRef.current))},[]),v=l.useCallback(function(h){d(h.type,Ue(h),h.target,c(h,e.lockRef.current))},[]);l.useEffect(function(){return Ce.push(o),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:v}),document.addEventListener("wheel",u,Se),document.addEventListener("touchmove",u,Se),document.addEventListener("touchstart",f,Se),function(){Ce=Ce.filter(function(h){return h!==o}),document.removeEventListener("wheel",u,Se),document.removeEventListener("touchmove",u,Se),document.removeEventListener("touchstart",f,Se)}},[]);var m=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(o,{styles:Zc(a)}):null,m?l.createElement(Vc,{gapMode:e.gapMode}):null)}function nl(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const rl=Rc(wa,tl);var Ta=l.forwardRef(function(e,t){return l.createElement(Pt,ee({},e,{ref:t,sideCar:rl}))});Ta.classNames=Pt.classNames;var al=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Te=new WeakMap,Ke=new WeakMap,Qe={},$t=0,Pa=function(e){return e&&(e.host||Pa(e.parentNode))},ol=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Pa(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},sl=function(e,t,n,r){var a=ol(t,Array.isArray(e)?e:[e]);Qe[n]||(Qe[n]=new WeakMap);var o=Qe[n],i=[],c=new Set,u=new Set(a),d=function(p){!p||c.has(p)||(c.add(p),d(p.parentNode))};a.forEach(d);var f=function(p){!p||u.has(p)||Array.prototype.forEach.call(p.children,function(v){if(c.has(v))f(v);else try{var m=v.getAttribute(r),x=m!==null&&m!=="false",h=(Te.get(v)||0)+1,g=(o.get(v)||0)+1;Te.set(v,h),o.set(v,g),i.push(v),h===1&&x&&Ke.set(v,!0),g===1&&v.setAttribute(n,"true"),x||v.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",v,y)}})};return f(t),c.clear(),$t++,function(){i.forEach(function(p){var v=Te.get(p)-1,m=o.get(p)-1;Te.set(p,v),o.set(p,m),v||(Ke.has(p)||p.removeAttribute(r),Ke.delete(p)),m||p.removeAttribute(n)}),$t--,$t||(Te=new WeakMap,Te=new WeakMap,Ke=new WeakMap,Qe={})}},il=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=al(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live]"))),sl(r,a,n,"aria-hidden")):function(){return null}},gn="Dialog",[Na,Ru]=en(gn),[cl,J]=Na(gn),Ma=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:o,modal:i=!0}=e,c=l.useRef(null),u=l.useRef(null),[d=!1,f]=tn({prop:r,defaultProp:a,onChange:o});return s.jsx(cl,{scope:t,triggerRef:c,contentRef:u,contentId:et(),titleId:et(),descriptionId:et(),open:d,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(p=>!p),[f]),modal:i,children:n})};Ma.displayName=gn;var ja="DialogTrigger",Oa=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=J(ja,n),o=U(t,a.triggerRef);return s.jsx(H.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":bn(a.open),...r,ref:o,onClick:F(e.onClick,a.onOpenToggle)})});Oa.displayName=ja;var vn="DialogPortal",[ll,Ra]=Na(vn,{forceMount:void 0}),ka=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:a}=e,o=J(vn,t);return s.jsx(ll,{scope:t,forceMount:n,children:l.Children.map(r,i=>s.jsx(Be,{present:n||o.open,children:s.jsx(nn,{asChild:!0,container:a,children:i})}))})};ka.displayName=vn;var ut="DialogOverlay",Da=l.forwardRef((e,t)=>{const n=Ra(ut,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=J(ut,e.__scopeDialog);return o.modal?s.jsx(Be,{present:r||o.open,children:s.jsx(dl,{...a,ref:t})}):null});Da.displayName=ut;var dl=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=J(ut,n);return s.jsx(Ta,{as:De,allowPinchZoom:!0,shards:[a.contentRef],children:s.jsx(H.div,{"data-state":bn(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),we="DialogContent",Aa=l.forwardRef((e,t)=>{const n=Ra(we,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=J(we,e.__scopeDialog);return s.jsx(Be,{present:r||o.open,children:o.modal?s.jsx(ul,{...a,ref:t}):s.jsx(fl,{...a,ref:t})})});Aa.displayName=we;var ul=l.forwardRef((e,t)=>{const n=J(we,e.__scopeDialog),r=l.useRef(null),a=U(t,n.contentRef,r);return l.useEffect(()=>{const o=r.current;if(o)return il(o)},[]),s.jsx(_a,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:F(e.onCloseAutoFocus,o=>{var i;o.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:F(e.onPointerDownOutside,o=>{const i=o.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&o.preventDefault()}),onFocusOutside:F(e.onFocusOutside,o=>o.preventDefault())})}),fl=l.forwardRef((e,t)=>{const n=J(we,e.__scopeDialog),r=l.useRef(!1),a=l.useRef(!1);return s.jsx(_a,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var i,c;(i=e.onCloseAutoFocus)==null||i.call(e,o),o.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),o.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:o=>{var u,d;(u=e.onInteractOutside)==null||u.call(e,o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const i=o.target;((d=n.triggerRef.current)==null?void 0:d.contains(i))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&a.current&&o.preventDefault()}})}),_a=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o,...i}=e,c=J(we,n),u=l.useRef(null),d=U(t,u);return yc(),s.jsxs(s.Fragment,{children:[s.jsx(ga,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:s.jsx(mt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":bn(c.open),...i,ref:d,onDismiss:()=>c.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(hl,{titleId:c.titleId}),s.jsx(pl,{contentRef:u,descriptionId:c.descriptionId})]})]})}),xn="DialogTitle",La=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=J(xn,n);return s.jsx(H.h2,{id:a.titleId,...r,ref:t})});La.displayName=xn;var Fa="DialogDescription",Ia=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=J(Fa,n);return s.jsx(H.p,{id:a.descriptionId,...r,ref:t})});Ia.displayName=Fa;var Wa="DialogClose",$a=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=J(Wa,n);return s.jsx(H.button,{type:"button",...r,ref:t,onClick:F(e.onClick,()=>a.onOpenChange(!1))})});$a.displayName=Wa;function bn(e){return e?"open":"closed"}var Ha="DialogTitleWarning",[ku,Ba]=jo(Ha,{contentName:we,titleName:xn,docsSlug:"dialog"}),hl=({titleId:e})=>{const t=Ba(Ha),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},ml="DialogDescriptionWarning",pl=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ba(ml).contentName}}.`;return l.useEffect(()=>{var o;const a=(o=e.current)==null?void 0:o.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},gl=Ma,Du=Oa,vl=ka,Ya=Da,Va=Aa,za=La,Xa=Ia,xl=$a;const bl=gl,wl=vl,qa=l.forwardRef(({className:e,...t},n)=>s.jsx(Ya,{className:N("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));qa.displayName=Ya.displayName;const yl=Zt("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Ga=l.forwardRef(({side:e="right",className:t,children:n,...r},a)=>s.jsxs(wl,{children:[s.jsx(qa,{}),s.jsxs(Va,{ref:a,className:N(yl({side:e}),t),...r,children:[n,s.jsxs(xl,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[s.jsx(lr,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Ga.displayName=Va.displayName;const El=l.forwardRef(({className:e,...t},n)=>s.jsx(za,{ref:n,className:N("text-lg font-semibold text-foreground",e),...t}));El.displayName=za.displayName;const Sl=l.forwardRef(({className:e,...t},n)=>s.jsx(Xa,{ref:n,className:N("text-sm text-muted-foreground",e),...t}));Sl.displayName=Xa.displayName;const Cl="18rem",Ua=l.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:a,...o},i)=>{const{isMobile:c,state:u,openMobile:d,setOpenMobile:f}=Tt();return n==="none"?s.jsx("div",{className:N("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...o,children:a}):c?s.jsx(bl,{open:d,onOpenChange:f,...o,children:s.jsx(Ga,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":Cl},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:a})})}):s.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":u,"data-collapsible":u==="collapsed"?n:"","data-variant":t,"data-side":e,children:[s.jsx("div",{className:N("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),s.jsx("div",{className:N("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...o,children:s.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:a})})]})});Ua.displayName="Sidebar";const Ka=l.forwardRef(({className:e,...t},n)=>s.jsx("main",{ref:n,className:N("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Ka.displayName="SidebarInset";const Qa=l.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:a}=Tt();return s.jsxs(Ze,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:N("mobile-button h-12 w-12 touch-feedback",e),onClick:o=>{t==null||t(o),a()},"data-primary":"true",...n,children:[s.jsx(Ho,{className:"h-6 w-6"}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Qa.displayName="SidebarTrigger";const Tl=l.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=Tt();return s.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:N("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});Tl.displayName="SidebarRail";function Qn({className:e,...t}){return s.jsx("div",{className:N("animate-pulse rounded-md bg-muted",e),...t})}const ge=l.forwardRef(({className:e,...t},n)=>s.jsx("ul",{ref:n,"data-sidebar":"menu",className:N("flex w-full min-w-0 flex-col gap-1",e),...t}));ge.displayName="SidebarMenu";const B=l.forwardRef(({className:e,...t},n)=>s.jsx("li",{ref:n,"data-sidebar":"menu-item",className:N("group/menu-item relative",e),...t}));B.displayName="SidebarMenuItem";const Pl=Zt("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-12 text-sm min-h-[48px]",sm:"h-10 text-xs min-h-[44px]",lg:"h-14 text-sm group-data-[collapsible=icon]:!p-0 min-h-[56px]"}},defaultVariants:{variant:"default",size:"default"}}),Y=l.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:a,className:o,...i},c)=>{const u=e?De:"button",{isMobile:d,state:f}=Tt(),p=s.jsx(u,{ref:c,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:N(Pl({variant:n,size:r}),o),...i});return a?(typeof a=="string"&&(a={children:a}),s.jsxs(ic,{children:[s.jsx(cc,{asChild:!0,children:p}),s.jsx(ha,{side:"right",align:"center",hidden:f!=="collapsed"||d,...a})]})):p});Y.displayName="SidebarMenuButton";const Nl=l.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},a)=>{const o=t?De:"button";return s.jsx(o,{ref:a,"data-sidebar":"menu-action",className:N("absolute right-1 top-1.5 flex aspect-square w-8 h-8 min-w-[44px] min-h-[44px] items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0 touch-feedback","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});Nl.displayName="SidebarMenuAction";const Ml=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:N("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));Ml.displayName="SidebarMenuBadge";const jl=l.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const a=l.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return s.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:N("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&s.jsx(Qn,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),s.jsx(Qn,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":a}})]})});jl.displayName="SidebarMenuSkeleton";const Ol=l.forwardRef(({className:e,...t},n)=>s.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:N("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));Ol.displayName="SidebarMenuSub";const Rl=l.forwardRef(({...e},t)=>s.jsx("li",{ref:t,...e}));Rl.displayName="SidebarMenuSubItem";const kl=l.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...a},o)=>{const i=e?De:"a";return s.jsx(i,{ref:o,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:N("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...a})});kl.displayName="SidebarMenuSubButton";const ve=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"group",className:N("relative flex w-full min-w-0 flex-col p-2",e),...t}));ve.displayName="SidebarGroup";const Ne=l.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const a=t?De:"div";return s.jsx(a,{ref:r,"data-sidebar":"group-label",className:N("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});Ne.displayName="SidebarGroupLabel";const Dl=l.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const a=t?De:"button";return s.jsx(a,{ref:r,"data-sidebar":"group-action",className:N("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});Dl.displayName="SidebarGroupAction";const xe=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"group-content",className:N("w-full text-sm",e),...t}));xe.displayName="SidebarGroupContent";var Al="Separator",Jn="horizontal",_l=["horizontal","vertical"],Ja=l.forwardRef((e,t)=>{const{decorative:n,orientation:r=Jn,...a}=e,o=Ll(r)?r:Jn,c=n?{role:"none"}:{"aria-orientation":o==="vertical"?o:void 0,role:"separator"};return s.jsx(H.div,{"data-orientation":o,...c,...a,ref:t})});Ja.displayName=Al;function Ll(e){return _l.includes(e)}var Za=Ja;const eo=l.forwardRef(({className:e,orientation:t="horizontal",decorative:n=!0,...r},a)=>s.jsx(Za,{ref:a,decorative:n,orientation:t,className:N("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...r}));eo.displayName=Za.displayName;const Fl=l.forwardRef(({className:e,...t},n)=>s.jsx(Oo,{ref:n,"data-sidebar":"input",className:N("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Fl.displayName="SidebarInput";const to=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"header",className:N("flex flex-col gap-2 p-2",e),...t}));to.displayName="SidebarHeader";const no=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"footer",className:N("flex flex-col gap-2 p-2",e),...t}));no.displayName="SidebarFooter";const Il=l.forwardRef(({className:e,...t},n)=>s.jsx(eo,{ref:n,"data-sidebar":"separator",className:N("mx-2 w-auto bg-sidebar-border",e),...t}));Il.displayName="SidebarSeparator";const ro=l.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"content",className:N("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));ro.displayName="SidebarContent";const Wl=({user:e})=>{if(!e)return null;switch(e.role){case"mentor":return s.jsxs(ve,{children:[s.jsx(Ne,{children:"Mentor Navigation"}),s.jsx(xe,{children:s.jsxs(ge,{children:[s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/team-overview",className:"flex items-center",children:[s.jsx(Mn,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Team Übersicht"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/berater-statistics",className:"flex items-center",children:[s.jsx(Do,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Berater Statistiken"})]})})})]})})]});case"teamleiter":return s.jsxs(ve,{children:[s.jsx(Ne,{children:"Teamleiter Navigation"}),s.jsx(xe,{children:s.jsxs(ge,{children:[s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/teams-overview",className:"flex items-center",children:[s.jsx(Nn,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Teams Übersicht"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/teams-statistics",className:"flex items-center",children:[s.jsx(ur,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Teams Statistiken"})]})})})]})})]});case"gebietsmanager":return s.jsxs(ve,{children:[s.jsx(Ne,{children:"Manager Navigation"}),s.jsx(xe,{children:s.jsx(ge,{children:s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/area-overview",className:"flex items-center",children:[s.jsx(Nn,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Gebietsübersicht"})]})})})})})]});case"admin":return s.jsxs(ve,{children:[s.jsx(Ne,{children:"Admin Navigation"}),s.jsx(xe,{children:s.jsxs(ge,{children:[s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/admin-dashboard",className:"flex items-center",children:[s.jsx(Yo,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Admin Dashboard"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/user-management",className:"flex items-center",children:[s.jsx(Mn,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Benutzerverwaltung"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/area-management",className:"flex items-center",children:[s.jsx(Io,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Gebiete verwalten"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/team-management",className:"flex items-center",children:[s.jsx(ko,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Teams verwalten"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,children:s.jsxs(Z,{to:"/settings",className:"flex items-center",children:[s.jsx(Bo,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Einstellungen"})]})})})]})})]});default:return null}};function z(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function me(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}const ao=6048e5,$l=864e5;let Hl={};function Nt(){return Hl}function He(e,t){var c,u,d,f;const n=Nt(),r=(t==null?void 0:t.weekStartsOn)??((u=(c=t==null?void 0:t.locale)==null?void 0:c.options)==null?void 0:u.weekStartsOn)??n.weekStartsOn??((f=(d=n.locale)==null?void 0:d.options)==null?void 0:f.weekStartsOn)??0,a=z(e),o=a.getDay(),i=(o<r?7:0)+o-r;return a.setDate(a.getDate()-i),a.setHours(0,0,0,0),a}function ft(e){return He(e,{weekStartsOn:1})}function oo(e){const t=z(e),n=t.getFullYear(),r=me(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const a=ft(r),o=me(e,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const i=ft(o);return t.getTime()>=a.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function ht(e){const t=z(e);return t.setHours(0,0,0,0),t}function Zn(e){const t=z(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function Bl(e,t){const n=ht(e),r=ht(t),a=+n-Zn(n),o=+r-Zn(r);return Math.round((a-o)/$l)}function Yl(e){const t=oo(e),n=me(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),ft(n)}function Vl(e){return me(e,Date.now())}function so(e,t){const n=ht(e),r=ht(t);return+n==+r}function zl(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function Xl(e){if(!zl(e)&&typeof e!="number")return!1;const t=z(e);return!isNaN(Number(t))}function ql(e){const t=z(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function Gl(e,t){const n=z(e.start),r=z(e.end);let a=+n>+r;const o=a?+n:+r,i=a?r:n;i.setHours(0,0,0,0);let c=1;const u=[];for(;+i<=o;)u.push(z(i)),i.setDate(i.getDate()+c),i.setHours(0,0,0,0);return a?u.reverse():u}function Ul(e){const t=z(e);return t.setDate(1),t.setHours(0,0,0,0),t}function Kl(e){const t=z(e),n=me(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}const Ql={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Jl=(e,t,n)=>{let r;const a=Ql[e];return typeof a=="string"?r=a:t===1?r=a.one:r=a.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function Oe(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const Zl={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},ed={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},td={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},nd={date:Oe({formats:Zl,defaultWidth:"full"}),time:Oe({formats:ed,defaultWidth:"full"}),dateTime:Oe({formats:td,defaultWidth:"full"})},rd={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},ad=(e,t,n,r)=>rd[e];function te(e){return(t,n)=>{const r=n!=null&&n.context?String(n.context):"standalone";let a;if(r==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,c=n!=null&&n.width?String(n.width):i;a=e.formattingValues[c]||e.formattingValues[i]}else{const i=e.defaultWidth,c=n!=null&&n.width?String(n.width):e.defaultWidth;a=e.values[c]||e.values[i]}const o=e.argumentCallback?e.argumentCallback(t):t;return a[o]}}const od={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},sd={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},id={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},cd={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ld={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},dd={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ud=(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},fd={ordinalNumber:ud,era:te({values:od,defaultWidth:"wide"}),quarter:te({values:sd,defaultWidth:"wide",argumentCallback:e=>e-1}),month:te({values:id,defaultWidth:"wide"}),day:te({values:cd,defaultWidth:"wide"}),dayPeriod:te({values:ld,defaultWidth:"wide",formattingValues:dd,defaultFormattingWidth:"wide"})};function ne(e){return(t,n={})=>{const r=n.width,a=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(a);if(!o)return null;const i=o[0],c=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(c)?md(c,p=>p.test(i)):hd(c,p=>p.test(i));let d;d=e.valueCallback?e.valueCallback(u):u,d=n.valueCallback?n.valueCallback(d):d;const f=t.slice(i.length);return{value:d,rest:f}}}function hd(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function md(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function io(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const a=r[0],o=t.match(e.parsePattern);if(!o)return null;let i=e.valueCallback?e.valueCallback(o[0]):o[0];i=n.valueCallback?n.valueCallback(i):i;const c=t.slice(a.length);return{value:i,rest:c}}}const pd=/^(\d+)(th|st|nd|rd)?/i,gd=/\d+/i,vd={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},xd={any:[/^b/i,/^(a|c)/i]},bd={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},wd={any:[/1/i,/2/i,/3/i,/4/i]},yd={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Ed={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Sd={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Cd={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Td={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Pd={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Nd={ordinalNumber:io({matchPattern:pd,parsePattern:gd,valueCallback:e=>parseInt(e,10)}),era:ne({matchPatterns:vd,defaultMatchWidth:"wide",parsePatterns:xd,defaultParseWidth:"any"}),quarter:ne({matchPatterns:bd,defaultMatchWidth:"wide",parsePatterns:wd,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ne({matchPatterns:yd,defaultMatchWidth:"wide",parsePatterns:Ed,defaultParseWidth:"any"}),day:ne({matchPatterns:Sd,defaultMatchWidth:"wide",parsePatterns:Cd,defaultParseWidth:"any"}),dayPeriod:ne({matchPatterns:Td,defaultMatchWidth:"any",parsePatterns:Pd,defaultParseWidth:"any"})},Md={code:"en-US",formatDistance:Jl,formatLong:nd,formatRelative:ad,localize:fd,match:Nd,options:{weekStartsOn:0,firstWeekContainsDate:1}};function jd(e){const t=z(e);return Bl(t,Kl(t))+1}function Od(e){const t=z(e),n=+ft(t)-+Yl(t);return Math.round(n/ao)+1}function co(e,t){var f,p,v,m;const n=z(e),r=n.getFullYear(),a=Nt(),o=(t==null?void 0:t.firstWeekContainsDate)??((p=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:p.firstWeekContainsDate)??a.firstWeekContainsDate??((m=(v=a.locale)==null?void 0:v.options)==null?void 0:m.firstWeekContainsDate)??1,i=me(e,0);i.setFullYear(r+1,0,o),i.setHours(0,0,0,0);const c=He(i,t),u=me(e,0);u.setFullYear(r,0,o),u.setHours(0,0,0,0);const d=He(u,t);return n.getTime()>=c.getTime()?r+1:n.getTime()>=d.getTime()?r:r-1}function Rd(e,t){var c,u,d,f;const n=Nt(),r=(t==null?void 0:t.firstWeekContainsDate)??((u=(c=t==null?void 0:t.locale)==null?void 0:c.options)==null?void 0:u.firstWeekContainsDate)??n.firstWeekContainsDate??((f=(d=n.locale)==null?void 0:d.options)==null?void 0:f.firstWeekContainsDate)??1,a=co(e,t),o=me(e,0);return o.setFullYear(a,0,r),o.setHours(0,0,0,0),He(o,t)}function kd(e,t){const n=z(e),r=+He(n,t)-+Rd(n,t);return Math.round(r/ao)+1}function O(e,t){const n=e<0?"-":"",r=Math.abs(e).toString().padStart(t,"0");return n+r}const ce={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return O(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):O(n+1,2)},d(e,t){return O(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return O(e.getHours()%12||12,t.length)},H(e,t){return O(e.getHours(),t.length)},m(e,t){return O(e.getMinutes(),t.length)},s(e,t){return O(e.getSeconds(),t.length)},S(e,t){const n=t.length,r=e.getMilliseconds(),a=Math.trunc(r*Math.pow(10,n-3));return O(a,t.length)}},Pe={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},er={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return ce.y(e,t)},Y:function(e,t,n,r){const a=co(e,r),o=a>0?a:1-a;if(t==="YY"){const i=o%100;return O(i,2)}return t==="Yo"?n.ordinalNumber(o,{unit:"year"}):O(o,t.length)},R:function(e,t){const n=oo(e);return O(n,t.length)},u:function(e,t){const n=e.getFullYear();return O(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return O(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return O(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return ce.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return O(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const a=kd(e,r);return t==="wo"?n.ordinalNumber(a,{unit:"week"}):O(a,t.length)},I:function(e,t,n){const r=Od(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):O(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):ce.d(e,t)},D:function(e,t,n){const r=jd(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):O(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const a=e.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return O(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const a=e.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return O(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return O(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let a;switch(r===12?a=Pe.noon:r===0?a=Pe.midnight:a=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let a;switch(r>=17?a=Pe.evening:r>=12?a=Pe.afternoon:r>=4?a=Pe.morning:a=Pe.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return ce.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):ce.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):ce.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):ce.s(e,t)},S:function(e,t){return ce.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return nr(r);case"XXXX":case"XX":return pe(r);case"XXXXX":case"XXX":default:return pe(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return nr(r);case"xxxx":case"xx":return pe(r);case"xxxxx":case"xxx":default:return pe(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+tr(r,":");case"OOOO":default:return"GMT"+pe(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+tr(r,":");case"zzzz":default:return"GMT"+pe(r,":")}},t:function(e,t,n){const r=Math.trunc(e.getTime()/1e3);return O(r,t.length)},T:function(e,t,n){const r=e.getTime();return O(r,t.length)}};function tr(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),a=Math.trunc(r/60),o=r%60;return o===0?n+String(a):n+String(a)+t+O(o,2)}function nr(e,t){return e%60===0?(e>0?"-":"+")+O(Math.abs(e)/60,2):pe(e,t)}function pe(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),a=O(Math.trunc(r/60),2),o=O(r%60,2);return n+a+t+o}const rr=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},lo=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Dd=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return rr(e,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",rr(r,t)).replace("{{time}}",lo(a,t))},Ad={p:lo,P:Dd},_d=/^D+$/,Ld=/^Y+$/,Fd=["D","DD","YY","YYYY"];function Id(e){return _d.test(e)}function Wd(e){return Ld.test(e)}function $d(e,t,n){const r=Hd(e,t,n);if(console.warn(r),Fd.includes(e))throw new RangeError(r)}function Hd(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Bd=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Yd=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Vd=/^'([^]*?)'?$/,zd=/''/g,Xd=/[a-zA-Z]/;function Je(e,t,n){var f,p,v,m,x,h,g,y;const r=Nt(),a=(n==null?void 0:n.locale)??r.locale??Md,o=(n==null?void 0:n.firstWeekContainsDate)??((p=(f=n==null?void 0:n.locale)==null?void 0:f.options)==null?void 0:p.firstWeekContainsDate)??r.firstWeekContainsDate??((m=(v=r.locale)==null?void 0:v.options)==null?void 0:m.firstWeekContainsDate)??1,i=(n==null?void 0:n.weekStartsOn)??((h=(x=n==null?void 0:n.locale)==null?void 0:x.options)==null?void 0:h.weekStartsOn)??r.weekStartsOn??((y=(g=r.locale)==null?void 0:g.options)==null?void 0:y.weekStartsOn)??0,c=z(e);if(!Xl(c))throw new RangeError("Invalid time value");let u=t.match(Yd).map(b=>{const w=b[0];if(w==="p"||w==="P"){const E=Ad[w];return E(b,a.formatLong)}return b}).join("").match(Bd).map(b=>{if(b==="''")return{isToken:!1,value:"'"};const w=b[0];if(w==="'")return{isToken:!1,value:qd(b)};if(er[w])return{isToken:!0,value:b};if(w.match(Xd))throw new RangeError("Format string contains an unescaped latin alphabet character `"+w+"`");return{isToken:!1,value:b}});a.localize.preprocessor&&(u=a.localize.preprocessor(c,u));const d={firstWeekContainsDate:o,weekStartsOn:i,locale:a};return u.map(b=>{if(!b.isToken)return b.value;const w=b.value;(!(n!=null&&n.useAdditionalWeekYearTokens)&&Wd(w)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&Id(w))&&$d(w,t,String(e));const E=er[w[0]];return E(c,w,a.localize,d)}).join("")}function qd(e){const t=e.match(Vd);return t?t[1].replace(zd,"'"):e}function Gd(e){return so(e,Vl(e))}const ar={lessThanXSeconds:{standalone:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"},withPreposition:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"}},xSeconds:{standalone:{one:"1 Sekunde",other:"{{count}} Sekunden"},withPreposition:{one:"1 Sekunde",other:"{{count}} Sekunden"}},halfAMinute:{standalone:"eine halbe Minute",withPreposition:"einer halben Minute"},lessThanXMinutes:{standalone:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"},withPreposition:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"}},xMinutes:{standalone:{one:"1 Minute",other:"{{count}} Minuten"},withPreposition:{one:"1 Minute",other:"{{count}} Minuten"}},aboutXHours:{standalone:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"},withPreposition:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"}},xHours:{standalone:{one:"1 Stunde",other:"{{count}} Stunden"},withPreposition:{one:"1 Stunde",other:"{{count}} Stunden"}},xDays:{standalone:{one:"1 Tag",other:"{{count}} Tage"},withPreposition:{one:"1 Tag",other:"{{count}} Tagen"}},aboutXWeeks:{standalone:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"},withPreposition:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"}},xWeeks:{standalone:{one:"1 Woche",other:"{{count}} Wochen"},withPreposition:{one:"1 Woche",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"etwa 1 Monat",other:"etwa {{count}} Monate"},withPreposition:{one:"etwa 1 Monat",other:"etwa {{count}} Monaten"}},xMonths:{standalone:{one:"1 Monat",other:"{{count}} Monate"},withPreposition:{one:"1 Monat",other:"{{count}} Monaten"}},aboutXYears:{standalone:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahre"},withPreposition:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahren"}},xYears:{standalone:{one:"1 Jahr",other:"{{count}} Jahre"},withPreposition:{one:"1 Jahr",other:"{{count}} Jahren"}},overXYears:{standalone:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahre"},withPreposition:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahren"}},almostXYears:{standalone:{one:"fast 1 Jahr",other:"fast {{count}} Jahre"},withPreposition:{one:"fast 1 Jahr",other:"fast {{count}} Jahren"}}},Ud=(e,t,n)=>{let r;const a=n!=null&&n.addSuffix?ar[e].withPreposition:ar[e].standalone;return typeof a=="string"?r=a:t===1?r=a.one:r=a.other.replace("{{count}}",String(t)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:"vor "+r:r},Kd={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},Qd={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},Jd={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},Zd={date:Oe({formats:Kd,defaultWidth:"full"}),time:Oe({formats:Qd,defaultWidth:"full"}),dateTime:Oe({formats:Jd,defaultWidth:"full"})},eu={lastWeek:"'letzten' eeee 'um' p",yesterday:"'gestern um' p",today:"'heute um' p",tomorrow:"'morgen um' p",nextWeek:"eeee 'um' p",other:"P"},tu=(e,t,n,r)=>eu[e],nu={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor Christus","nach Christus"]},ru={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},Jt={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"]},au={narrow:Jt.narrow,abbreviated:["Jan.","Feb.","März","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:Jt.wide},ou={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},su={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},iu={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},cu=e=>Number(e)+".",lu={ordinalNumber:cu,era:te({values:nu,defaultWidth:"wide"}),quarter:te({values:ru,defaultWidth:"wide",argumentCallback:e=>e-1}),month:te({values:Jt,formattingValues:au,defaultWidth:"wide"}),day:te({values:ou,defaultWidth:"wide"}),dayPeriod:te({values:su,defaultWidth:"wide",formattingValues:iu,defaultFormattingWidth:"wide"})},du=/^(\d+)(\.)?/i,uu=/\d+/i,fu={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i},hu={any:[/^v/i,/^n/i]},mu={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},pu={any:[/1/i,/2/i,/3/i,/4/i]},gu={narrow:/^[jfmasond]/i,abbreviated:/^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,wide:/^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i},vu={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^j[aä]/i,/^f/i,/^mär/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},xu={narrow:/^[smdmf]/i,short:/^(so|mo|di|mi|do|fr|sa)/i,abbreviated:/^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,wide:/^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i},bu={any:[/^so/i,/^mo/i,/^di/i,/^mi/i,/^do/i,/^f/i,/^sa/i]},wu={narrow:/^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,abbreviated:/^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,wide:/^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i},yu={any:{am:/^v/i,pm:/^n/i,midnight:/^Mitte/i,noon:/^Mitta/i,morning:/morgens/i,afternoon:/nachmittags/i,evening:/abends/i,night:/nachts/i}},Eu={ordinalNumber:io({matchPattern:du,parsePattern:uu,valueCallback:e=>parseInt(e)}),era:ne({matchPatterns:fu,defaultMatchWidth:"wide",parsePatterns:hu,defaultParseWidth:"any"}),quarter:ne({matchPatterns:mu,defaultMatchWidth:"wide",parsePatterns:pu,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ne({matchPatterns:gu,defaultMatchWidth:"wide",parsePatterns:vu,defaultParseWidth:"any"}),day:ne({matchPatterns:xu,defaultMatchWidth:"wide",parsePatterns:bu,defaultParseWidth:"any"}),dayPeriod:ne({matchPatterns:wu,defaultMatchWidth:"wide",parsePatterns:yu,defaultParseWidth:"any"})},or={code:"de",formatDistance:Ud,formatLong:Zd,formatRelative:tu,localize:lu,match:Eu,options:{weekStartsOn:1,firstWeekContainsDate:4}},Su=()=>{const[e,t]=l.useState(new Date),[n,r]=l.useState(null),{getAppointmentsByDate:a}=bo(),o=ir(),i=Ul(e),c=ql(e),u=Gl({start:i,end:c}),d=m=>{const x=new Date(e);m==="prev"?x.setMonth(e.getMonth()-1):x.setMonth(e.getMonth()+1),t(x)},f=m=>{r(m)},p=m=>{const x=Je(m,"yyyy-MM-dd");return a(x)},v=m=>p(m).length>0;return s.jsxs(wo,{className:"w-full",children:[s.jsx(yo,{className:"pb-3",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(Eo,{className:"text-sm font-semibold flex items-center gap-2",children:[s.jsx(dr,{className:"h-4 w-4"}),"Kalender"]}),s.jsx(Ze,{variant:"ghost",size:"sm",onClick:()=>o("/calendar"),className:"text-xs text-blue-600 hover:text-blue-800",children:"Alle anzeigen"})]})}),s.jsxs(So,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ze,{variant:"ghost",size:"sm",onClick:()=>d("prev"),className:"min-h-[44px] min-w-[44px] p-0",children:s.jsx(Ao,{className:"h-4 w-4"})}),s.jsx("span",{className:"text-sm font-medium",children:Je(e,"MMMM yyyy",{locale:or})}),s.jsx(Ze,{variant:"ghost",size:"sm",onClick:()=>d("next"),className:"min-h-[44px] min-w-[44px] p-0",children:s.jsx(_o,{className:"h-4 w-4"})})]}),s.jsxs("div",{className:"grid grid-cols-7 gap-1 text-xs",children:[["Mo","Di","Mi","Do","Fr","Sa","So"].map(m=>s.jsx("div",{className:"h-6 flex items-center justify-center text-gray-500 font-medium",children:m},m)),u.map(m=>{p(m);const x=v(m),h=n&&so(m,n),g=Gd(m);return s.jsxs("button",{onClick:()=>f(m),className:N("min-h-[44px] min-w-[44px] text-xs rounded flex items-center justify-center relative transition-colors","hover:bg-gray-100",g&&"bg-red-100 text-red-700 font-semibold",h&&"bg-blue-100 text-blue-700",x&&"font-bold"),children:[Je(m,"d"),x&&s.jsx("div",{className:"absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]},m.toISOString())})]}),n&&s.jsxs("div",{className:"mt-3 space-y-2",children:[s.jsx("div",{className:"text-xs font-medium text-gray-700",children:Je(n,"dd.MM.yyyy",{locale:or})}),p(n).map((m,x)=>s.jsxs("div",{className:"text-xs p-2 bg-blue-50 rounded border-l-2 border-blue-400",children:[s.jsx("div",{className:"font-medium text-blue-800",children:m.time}),s.jsx("div",{className:"text-blue-600 truncate",children:m.address})]},x)),p(n).length===0&&s.jsx("div",{className:"text-xs text-gray-500 italic",children:"Keine Termine"})]})]})]})},Cu=({user:e,logout:t})=>{const n=ir(),r=async()=>{await t(),n("/login")};return s.jsxs(Ua,{className:"border-r border-border transition-colors duration-300",children:[s.jsx(to,{className:"p-4 md:p-6 border-b border-border",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"bg-red-100 p-2 rounded-xl",children:s.jsx($o,{className:"h-6 w-6 text-red-600"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-lg font-bold text-foreground",children:"Visit Flow"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Compass"})]})]})}),s.jsxs(ro,{className:"px-2 md:px-4",children:[s.jsxs(ve,{children:[s.jsx(Ne,{className:"text-muted-foreground font-semibold px-2 py-2 text-sm",children:"Navigation"}),s.jsx(xe,{children:s.jsxs(ge,{className:"space-y-1",children:[s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/",className:"flex items-center gap-3 px-3",children:[s.jsx(Co,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Laufliste"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/map",className:"flex items-center gap-3 px-3",children:[s.jsx(Wo,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Kartenansicht"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/statistics",className:"flex items-center gap-3 px-3",children:[s.jsx(ur,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Statistiken"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/daily-view",className:"flex items-center gap-3 px-3",children:[s.jsx(Lo,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Tagesübersicht"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/calendar",className:"flex items-center gap-3 px-3",children:[s.jsx(dr,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Termine"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/pattern-analysis-demo",className:"flex items-center gap-3 px-3",children:[s.jsx(Vo,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Smart Visit Assistant"})]})})}),s.jsx(B,{children:s.jsx(Y,{asChild:!0,className:"h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group",children:s.jsxs("a",{href:"/profile",className:"flex items-center gap-3 px-3",children:[s.jsx(Ro,{className:"h-5 w-5 group-hover:scale-110 transition-transform"}),s.jsx("span",{className:"font-medium",children:"Profil"})]})})})]})})]}),s.jsx(ve,{className:"mt-6",children:s.jsx(xe,{children:s.jsx(Su,{})})}),e&&s.jsx(Wl,{user:e})]}),e&&s.jsxs(no,{className:"p-4 border-t border-border",children:[s.jsxs("div",{className:"mb-3 p-3 bg-muted rounded-xl",children:[s.jsx("p",{className:"text-sm font-medium text-foreground truncate",children:e.name}),s.jsx("p",{className:"text-xs text-muted-foreground",children:e.role})]}),s.jsx(ge,{children:s.jsx(B,{children:s.jsxs(Y,{onClick:r,className:"w-full h-12 md:h-14 rounded-xl bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-all duration-200 touch-feedback",children:[s.jsx(Fo,{className:"h-5 w-5"}),s.jsx("span",{className:"font-medium",children:"Logout"})]})})})]})]})},Tu=({title:e,user:t,isMobile:n})=>s.jsxs("header",{className:"flex items-center justify-between h-16 md:h-20 bg-gradient-to-r from-white via-red-50/30 to-white border-b border-red-100 w-full shadow-lg backdrop-blur-sm transition-all duration-300",children:[s.jsxs("div",{className:"flex items-center px-4 md:px-6",children:[s.jsx(Qa,{className:"mr-3 md:mr-4 h-10 w-10 md:h-12 md:w-12 rounded-2xl bg-white/80 hover:bg-red-50 hover:text-red-600 border border-red-100 shadow-md hover:shadow-lg transition-all duration-200 touch-feedback"}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"bg-gradient-to-br from-red-500 to-red-600 p-2 rounded-xl shadow-lg",children:s.jsx("span",{className:"text-white font-bold text-lg",children:"📋"})}),s.jsx("h1",{className:"text-lg md:text-2xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent",children:e||"Laufliste"})]})]}),t&&!n&&s.jsx("div",{className:"hidden md:flex items-center space-x-4 px-4 md:px-6",children:s.jsx("div",{className:"bg-gradient-to-r from-red-50 to-red-100 px-6 py-3 rounded-2xl border border-red-200 shadow-lg backdrop-blur-sm",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-md",children:s.jsx("span",{className:"text-white font-bold text-sm",children:t.name.charAt(0).toUpperCase()})}),s.jsxs("div",{children:[s.jsx("span",{className:"text-red-800 font-semibold block",children:t.name}),s.jsx("span",{className:"text-red-600 text-sm",children:t.role})]})]})})}),t&&n&&s.jsx("div",{className:"flex md:hidden items-center px-4",children:s.jsx("div",{className:"bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 rounded-xl border border-red-200 shadow-md",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-xs",children:t.name.charAt(0).toUpperCase()})}),s.jsx("span",{className:"text-sm font-semibold text-red-800",children:t.name})]})})})]}),Au=({title:e,children:t})=>{const{user:n,logout:r}=To(),a=Wr(),o=async()=>await r();return s.jsx(pa,{children:s.jsxs("div",{className:"min-h-screen flex w-full bg-background transition-colors duration-300",children:[s.jsx(Cu,{user:n,logout:o}),s.jsxs(Ka,{className:"flex-1 flex flex-col overflow-hidden w-full",children:[s.jsx(Tu,{title:e,user:n,isMobile:a}),s.jsx("main",{className:"flex-1 overflow-y-auto bg-background w-full scrollbar-none transition-colors duration-300",children:s.jsx("div",{className:"w-full h-full",children:t||s.jsx(Po,{})})})]}),s.jsx(Os,{})]})})};export{nn as $,ht as A,ko as B,dr as C,Xa as D,Md as E,Kl as F,ft as G,Od as H,kd as I,zl as J,Kr as K,Wi as L,Au as M,il as N,Ya as O,vl as P,Ta as Q,gl as R,Yo as S,Vo as T,Mn as U,yc as V,ku as W,ga as X,mt as Y,$i as Z,Hi as _,Io as a,Ii as a0,Vl as a1,Nt as a2,pt as a3,Wr as b,Ko as c,or as d,Bo as e,Je as f,Wo as g,ql as h,Gl as i,Ao as j,_o as k,so as l,Gd as m,Ru as n,Du as o,Va as p,za as q,xl as r,Ul as s,z as t,Tt as u,me as v,He as w,Zn as x,ao as y,Bl as z};
