import{r as m,j as e,k as F,C as H,H as W,i as M,l as R,B as y,u as T,a as G,m as J,J as g,O as B}from"./index-Cmt5neWh.js";import{I}from"./input-BK13BBqa.js";import{L as w}from"./label-NwAA2N0T.js";import{a as q,C as D,f as v}from"./MainLayout-wyzz138D.js";import{B as E}from"./Button-ETlvKXsU.js";import{A as Z,C as $,a as Q}from"./Card-CELKqcN7.js";import{B as X}from"./badge-XkNoLG2o.js";import{S as Y}from"./SimpleStatusButtons-CtnyXuDf.js";import{C as V}from"./clock-DhYcPjhn.js";import{D as _,b as ee,c as te,d as se}from"./dialog-BFTaoFLK.js";import{P as ae,a as re,b as ne,C as ie}from"./popover-BPm3A8JC.js";import{t as b,u as oe}from"./useSwipeGestures-49dHBwDT.js";const le=({onCreateDoor:s,isCreating:t})=>{const[r,a]=m.useState(""),l=i=>{i.preventDefault(),r.trim()&&s(r.trim())};return e.jsxs(F,{className:"mb-6 glass-card border-0 shadow-lg rounded-2xl",children:[e.jsxs(H,{className:"text-center pb-4",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx("div",{className:"bg-blue-100 p-3 rounded-full",children:e.jsx(W,{className:"h-6 w-6 text-blue-600"})})}),e.jsx(M,{className:"text-lg font-bold text-gray-800",children:"Tür benennen"}),e.jsx("p",{className:"text-sm text-gray-600",children:'Geben Sie einen Namen für die Tür ein (z.B. "Haupteingang", "Wohnung 1")'})]}),e.jsx(R,{children:e.jsxs("form",{onSubmit:l,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(w,{htmlFor:"door-name",className:"text-sm font-medium text-gray-700",children:"Türname"}),e.jsx(I,{id:"door-name",type:"text",value:r,onChange:i=>a(i.target.value),placeholder:"z.B. Haupteingang",className:"mt-1 h-12 rounded-xl border-2 border-gray-200 focus:border-blue-500",disabled:t,required:!0})]}),e.jsx(y,{type:"submit",disabled:!r.trim()||t,className:"w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl text-white",children:t?"Wird erstellt...":"Tür erstellen und fortfahren"})]})})]})},ce=({address:s,house:t,step:r,existingDoors:a=[]})=>e.jsx(F,{className:"w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden animate-fade-in",children:e.jsxs(H,{className:"text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx(q,{className:"h-8 w-8 text-red-600"})}),e.jsxs(M,{className:"text-2xl font-bold text-gray-800",children:["EFH Besuch - Schritt ",r]}),e.jsxs("div",{className:"mt-4 p-4 bg-white/60 rounded-xl",children:[e.jsxs("p",{className:"text-lg font-semibold text-gray-800",children:[s.street," ",t.houseNumber]}),e.jsxs("p",{className:"text-gray-600",children:[s.zipCode," ",s.city]})]}),r===2&&a.length>0&&e.jsx("div",{className:"mt-4 p-3 bg-green-50/60 rounded-xl border border-green-200",children:e.jsxs("p",{className:"text-sm font-medium text-green-800",children:["✓ Tür: ",a[0].name]})})]})}),de=({visitId:s,address:t,house:r,onCreateDoor:a,isCreating:l})=>(T(),e.jsxs("div",{className:"space-y-6",children:[e.jsx(ce,{address:t,house:r,step:1}),e.jsx(le,{onCreateDoor:a,isCreating:l})]})),me={duration:{fast:150,normal:250,slow:350,slower:500},easing:{ease:"cubic-bezier(0.4, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",easeOut:"cubic-bezier(0, 0, 0.2, 1)",easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},classes:{fadeIn:"animate-in fade-in duration-250",fadeOut:"animate-out fade-out duration-250",slideInFromLeft:"animate-in slide-in-from-left-4 duration-250",slideInFromRight:"animate-in slide-in-from-right-4 duration-250",slideInFromTop:"animate-in slide-in-from-top-4 duration-250",slideInFromBottom:"animate-in slide-in-from-bottom-4 duration-250",scaleIn:"animate-in zoom-in-95 duration-250",scaleOut:"animate-out zoom-out-95 duration-250",slideAndFade:"animate-in slide-in-from-bottom-2 fade-in duration-250",scaleAndFade:"animate-in zoom-in-95 fade-in duration-250",hoverScale:"transition-transform duration-250 hover:scale-105 active:scale-95",hoverLift:"transition-all duration-250 hover:shadow-lg hover:-translate-y-1",pulse:"animate-pulse",spin:"animate-spin",bounce:"animate-bounce"},transitions:{default:{transition:"all 250ms cubic-bezier(0.4, 0, 0.2, 1)"},fast:{transition:"all 150ms cubic-bezier(0.4, 0, 0.2, 1)"},slow:{transition:"all 350ms cubic-bezier(0.4, 0, 0.2, 1)"},transform:{transition:"transform 250ms cubic-bezier(0.4, 0, 0.2, 1)"},opacity:{transition:"opacity 250ms cubic-bezier(0.4, 0, 0.2, 1)"}}},ue=({visitId:s,visit:t,address:r,house:a,existingDoors:l,onStatusUpdate:i,isUpdating:u})=>{const x=T(),h=c=>{switch(c){case"Angetroffen → Termin":return"bg-blue-100 text-blue-800 border-blue-200";case"Angetroffen → Kein Interesse":return"bg-gray-100 text-gray-800 border-gray-200";case"Angetroffen → Sale":return"bg-green-100 text-green-800 border-green-200";default:return"bg-red-100 text-red-800 border-red-200"}};return e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx(Z,{street:r.street,houseNumber:a.houseNumber,city:r.city,zipCode:r.zipCode,houseType:a.type,step:2,totalSteps:2}),e.jsx($,{variant:"glass",className:"overflow-hidden",children:e.jsxs(Q,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-sm font-semibold text-gray-700",children:"Aktueller Status:"}),e.jsx(X,{className:h(t.status),variant:"outline",children:t.status})]}),t.status==="Angetroffen → Termin"&&t.appointmentDate&&t.appointmentTime&&e.jsx("div",{className:"mb-3 p-3 bg-blue-50/60 rounded-xl border border-blue-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-blue-800",children:[e.jsx(D,{className:"h-4 w-4"}),e.jsxs("span",{className:"font-medium",children:["Termin: ",v(new Date(t.appointmentDate),"dd.MM.yyyy")," um ",t.appointmentTime]})]})}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(V,{className:"h-4 w-4"}),e.jsx("span",{children:new Date(t.timestamp).toLocaleString("de-DE")})]})]}),e.jsx(Y,{currentStatus:t.status,onStatusUpdate:i,isUpdating:u}),t.status==="Angetroffen → Sale"&&e.jsx("div",{className:"mt-8",children:e.jsx(E,{onClick:()=>x(`/products/${s}`),variant:"success",size:"lg",fullWidth:!0,className:me.classes.hoverScale,children:"Produkte erfassen"})})]})})]})},he=({open:s,onClose:t,onConfirm:r})=>{const[a,l]=m.useState(),[i,u]=m.useState(""),x=()=>{if(a&&i){const c=v(a,"yyyy-MM-dd");r(c,i),t(),l(void 0),u("")}},h=()=>{t(),l(void 0),u("")};return e.jsx(_,{open:s,onOpenChange:h,children:e.jsxs(ee,{className:"sm:max-w-md",children:[e.jsx(te,{children:e.jsxs(se,{className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-5 w-5 text-blue-600"}),"Termin vereinbaren"]})}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium text-gray-700",children:"Datum"}),e.jsxs(ae,{children:[e.jsx(re,{asChild:!0,children:e.jsxs(y,{variant:"outline",className:G("w-full justify-start text-left font-normal mt-1",!a&&"text-muted-foreground"),children:[e.jsx(D,{className:"mr-2 h-4 w-4"}),a?v(a,"dd.MM.yyyy"):"Datum wählen"]})}),e.jsx(ne,{className:"w-auto p-0",align:"start",children:e.jsx(ie,{mode:"single",selected:a,onSelect:l,disabled:c=>c<new Date,initialFocus:!0,className:"p-3 pointer-events-auto"})})]})]}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium text-gray-700",children:"Uhrzeit"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx(V,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(I,{type:"time",value:i,onChange:c=>u(c.target.value),className:"pl-10",placeholder:"--:--"})]})]}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx(y,{variant:"outline",onClick:h,className:"flex-1",children:"Abbrechen"}),e.jsx(y,{onClick:x,disabled:!a||!i,className:"flex-1 bg-blue-600 hover:bg-blue-700",children:"Termin bestätigen"})]})]})]})})},De=({visitId:s})=>{const t=T(),{getVisit:r,getHouseById:a,getAddressById:l,updateVisitStatus:i,addDoor:u,getDoorsByVisit:x,updateDoorStatus:h}=J(),[c,p]=m.useState(!1),[L,A]=m.useState(!1),[O,z]=m.useState(!1),P=m.useMemo(()=>{const n=r(s),o=n?a(n.houseId):null,f=o?l(o.addressId):null,C=x(s);return{visit:n,house:o,address:f,existingDoors:C}},[s,r,a,l,x]),{visit:S,house:j,address:N,existingDoors:d}=P,U=m.useCallback(async n=>{A(!0),b("light");try{u({visitId:s,name:n,status:"N/A"}),g.success("Tür erfolgreich erstellt"),b("medium")}catch{g.error("Fehler beim Erstellen der Tür"),b("heavy")}finally{A(!1)}},[s,u]),k=m.useCallback(async(n,o)=>{if(n==="Angetroffen → Termin"){z(!0);return}p(!0),b("medium");try{i(s,n,void 0,void 0,o),d.length>0&&h(d[0].id,n,void 0,void 0,o);const f={"N/A":"Nicht angetroffen gespeichert","Angetroffen → Kein Interesse":"Kein Interesse gespeichert","Angetroffen → Sale":"Verkauf gespeichert! 🎉","Angetroffen → Termin":"Termin vereinbart"},C=o?`${f[n]} (mit Kommentar)`:f[n];g.success(C),n==="Angetroffen → Sale"?t(`/products/${s}`):setTimeout(()=>{t("/efh")},800)}catch{g.error("Fehler beim Aktualisieren des Status"),b("heavy")}finally{p(!1)}},[s,d,i,h,t]);if(oe({onSwipeLeft:()=>{d.length>0&&k("Angetroffen → Sale")},onSwipeRight:()=>{t(-1)},threshold:100}),console.log("EFHVisitTracker data:",{visit:S,house:j,address:N,existingDoors:d}),!S||!j||!N)return e.jsx($,{variant:"error",className:"max-w-md mx-auto m-4",children:e.jsxs("div",{className:"text-center p-8",children:[e.jsx("p",{className:"text-red-600 font-medium",children:"Besuch nicht gefunden"}),e.jsx(E,{variant:"outline",onClick:()=>t("/"),className:"mt-4",children:"Zur Startseite"})]})});const K=async(n,o)=>{p(!0);try{i(s,"Angetroffen → Termin",n,o),d.length>0&&h(d[0].id,"Angetroffen → Termin",n,o),g.success(`Termin erfolgreich vereinbart für ${v(new Date(n),"dd.MM.yyyy")} um ${o}`)}catch{g.error("Fehler beim Speichern des Termins")}finally{p(!1)}};return d.length===0?e.jsx(B,{children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:e.jsx(de,{visitId:s,address:N,house:j,onCreateDoor:U,isCreating:L})})}):e.jsx(B,{children:e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:[e.jsx(ue,{visitId:s,visit:S,address:N,house:j,existingDoors:d,onStatusUpdate:k,isUpdating:c}),e.jsx(he,{open:O,onClose:()=>z(!1),onConfirm:K}),e.jsx("div",{className:"fixed bottom-4 left-1/2 transform -translate-x-1/2 text-center text-sm text-neutral-500 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2",children:"💡 Wischen Sie nach links für schnellen Verkauf"})]})})};export{De as E,me as a};
