import{b as z,r as n,J as i,I as M,j as e,B as c,k as I,C as O,i as H,l as J}from"./index-Cmt5neWh.js";import{b as K,M as N,a as v,B as R}from"./MainLayout-wyzz138D.js";import{D as y,a as Z,b as w,c as C,d as G}from"./dialog-BFTaoFLK.js";import{I as m}from"./input-BK13BBqa.js";import{L as o}from"./label-NwAA2N0T.js";import{T as A}from"./textarea-BM1-JMTm.js";import{T as q,a as Q,b as _,c as x,d as U,e as h}from"./table-Bp6KGmPn.js";import{g as V,h as W,i as X,j as Y}from"./functions-DcGEt8N_.js";import{P as ee}from"./plus-C74OdLeW.js";import{S as $,T as se}from"./trash-2-BcpLsksG.js";import"./client-DbI4l5kI.js";const he=()=>{const{user:g}=z(),r=K(),[p,S]=n.useState([]),[D,T]=n.useState(!0),[b,j]=n.useState(null),[B,f]=n.useState(!1),[E,u]=n.useState(!1),[t,l]=n.useState({name:"",description:"",postal_codes:""});n.useEffect(()=>{d()},[]);const d=async()=>{try{const s=await V();S(s||[])}catch(s){console.error("Error loading areas:",s),i.error("Fehler beim Laden der Gebiete")}finally{T(!1)}},k=async()=>{if(!t.name.trim()){i.error("Name ist erforderlich");return}try{const s=t.postal_codes.split(",").map(a=>a.trim()).filter(a=>a.length>0);await W({name:t.name,description:t.description||void 0,postal_codes:s.length>0?s:void 0}),i.success(`Gebiet "${t.name}" wurde erstellt`),l({name:"",description:"",postal_codes:""}),f(!1),d()}catch(s){console.error("Error creating area:",s),i.error("Fehler beim Erstellen des Gebiets")}},F=async()=>{if(!b||!t.name.trim()){i.error("Name ist erforderlich");return}try{const s=t.postal_codes.split(",").map(a=>a.trim()).filter(a=>a.length>0);await X(b.id,{name:t.name,description:t.description||void 0,postal_codes:s.length>0?s:void 0}),i.success(`Gebiet "${t.name}" wurde aktualisiert`),l({name:"",description:"",postal_codes:""}),j(null),u(!1),d()}catch(s){console.error("Error updating area:",s),i.error("Fehler beim Aktualisieren des Gebiets")}},L=async s=>{if(confirm(`Möchten Sie das Gebiet "${s.name}" wirklich löschen?`))try{await Y(s.id),i.success(`Gebiet "${s.name}" wurde gelöscht`),d()}catch(a){console.error("Error deleting area:",a),i.error("Fehler beim Löschen des Gebiets")}},P=s=>{var a;j(s),l({name:s.name,description:s.description||"",postal_codes:((a=s.postal_codes)==null?void 0:a.join(", "))||""}),u(!0)};return!g||!M(g)?e.jsx(N,{title:"Gebiete verwalten",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})})})}):e.jsx(N,{title:"Gebiete verwalten",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${r?"text-2xl":"text-4xl"}`,children:"Gebiete verwalten"}),e.jsx("p",{className:`text-gray-600 ${r?"text-sm":"text-lg"}`,children:"Erstellen und verwalten Sie geografische Gebiete"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(y,{open:B,onOpenChange:f,children:[e.jsx(Z,{asChild:!0,children:e.jsxs(c,{className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Neues Gebiet"]})}),e.jsxs(w,{className:"sm:max-w-[500px]",children:[e.jsx(C,{children:e.jsxs(G,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5 text-blue-600"}),"Neues Gebiet erstellen"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(o,{htmlFor:"name",children:"Name*"}),e.jsx(m,{id:"name",value:t.name,onChange:s=>l({...t,name:s.target.value}),placeholder:"z.B. Stuttgart Nord"})]}),e.jsxs("div",{children:[e.jsx(o,{htmlFor:"description",children:"Beschreibung"}),e.jsx(A,{id:"description",value:t.description,onChange:s=>l({...t,description:s.target.value}),placeholder:"Beschreibung des Gebiets...",rows:3})]}),e.jsxs("div",{children:[e.jsx(o,{htmlFor:"postal_codes",children:"Postleitzahlen"}),e.jsx(m,{id:"postal_codes",value:t.postal_codes,onChange:s=>l({...t,postal_codes:s.target.value}),placeholder:"70173, 70174, 70176 (durch Komma getrennt)"})]}),e.jsx(c,{onClick:k,className:"w-full",children:"Gebiet erstellen"})]})]})]})}),e.jsxs(I,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsx(O,{className:`${r?"p-4 pb-2":"p-6 pb-4"}`,children:e.jsxs(H,{className:`flex items-center gap-3 ${r?"text-lg":"text-xl"} font-bold text-gray-800`,children:[e.jsx(R,{className:`${r?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Alle Gebiete (",p.length,")"]})}),e.jsx(J,{className:`${r?"p-4 pt-0":"p-6 pt-0"}`,children:D?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Lade Gebiete..."})}):p.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(v,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Noch keine Gebiete erstellt"})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(q,{children:[e.jsx(Q,{children:e.jsxs(_,{children:[e.jsx(x,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Name"}),e.jsx(x,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Beschreibung"}),e.jsx(x,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"PLZ"}),e.jsx(x,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Aktionen"})]})}),e.jsx(U,{children:p.map(s=>{var a;return e.jsxs(_,{className:"hover:bg-blue-50/50 transition-colors",children:[e.jsx(h,{className:`${r?"text-xs":"text-sm"} font-medium text-gray-800`,children:s.name}),e.jsx(h,{className:`${r?"text-xs":"text-sm"} text-gray-700`,children:s.description||"-"}),e.jsx(h,{className:`${r?"text-xs":"text-sm"} text-gray-700`,children:(a=s.postal_codes)!=null&&a.length?e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:[s.postal_codes.length," PLZ"]}):"-"}),e.jsx(h,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>P(s),className:"text-blue-600 border-blue-200 hover:bg-blue-50",children:e.jsx($,{className:"h-3 w-3"})}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>L(s),className:"text-red-600 border-red-200 hover:bg-red-50",children:e.jsx(se,{className:"h-3 w-3"})})]})})]},s.id)})})]})})})]}),e.jsx(y,{open:E,onOpenChange:u,children:e.jsxs(w,{className:"sm:max-w-[500px]",children:[e.jsx(C,{children:e.jsxs(G,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-blue-600"}),"Gebiet bearbeiten"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(o,{htmlFor:"edit-name",children:"Name*"}),e.jsx(m,{id:"edit-name",value:t.name,onChange:s=>l({...t,name:s.target.value}),placeholder:"z.B. Stuttgart Nord"})]}),e.jsxs("div",{children:[e.jsx(o,{htmlFor:"edit-description",children:"Beschreibung"}),e.jsx(A,{id:"edit-description",value:t.description,onChange:s=>l({...t,description:s.target.value}),placeholder:"Beschreibung des Gebiets...",rows:3})]}),e.jsxs("div",{children:[e.jsx(o,{htmlFor:"edit-postal-codes",children:"Postleitzahlen"}),e.jsx(m,{id:"edit-postal-codes",value:t.postal_codes,onChange:s=>l({...t,postal_codes:s.target.value}),placeholder:"70173, 70174, 70176 (durch Komma getrennt)"})]}),e.jsx(c,{onClick:F,className:"w-full",children:"Änderungen speichern"})]})]})})]})})})};export{he as default};
