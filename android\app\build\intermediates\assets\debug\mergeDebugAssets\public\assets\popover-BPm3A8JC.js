import{r as b,j as l,a as V,G as De,F as Oe,P as mt}from"./index-Cmt5neWh.js";import{a as M,e as de,s as te}from"./subDays-BLJlWEqr.js";import{t as N,v as A,w as Y,x as Ce,y as pt,s as P,l as k,z as R,h as ue,A as we,E as yt,F as bt,G as ce,H as gt,I as xt,f as W,J as Se,j as _t,k as Dt,K as Re,L as Fe,N as Ct,Q as wt,V as Nt,X as Mt,Y as kt,Z as Pt,_ as jt,$ as Ot,a0 as St}from"./MainLayout-wyzz138D.js";import{c as Rt,P as We,a as T,d as Ie,u as Ft,e as Wt}from"./input-BK13BBqa.js";function j(e,n){const t=N(e);if(isNaN(n))return A(e,NaN);if(!n)return t;const r=t.getDate(),a=A(e,t.getTime());a.setMonth(t.getMonth()+n+1,0);const o=a.getDate();return r>=o?a:(t.setFullYear(a.getFullYear(),a.getMonth(),r),t)}function ie(e,n){const t=n*7;return M(e,t)}function It(e,n){return j(e,n*12)}function Lt(e){let n;return e.forEach(function(t){const r=N(t);(n===void 0||n<r||isNaN(Number(r)))&&(n=r)}),n||new Date(NaN)}function Et(e){let n;return e.forEach(t=>{const r=N(t);(!n||n>r||isNaN(+r))&&(n=r)}),n||new Date(NaN)}function B(e,n){const t=N(e),r=N(n),a=t.getFullYear()-r.getFullYear(),o=t.getMonth()-r.getMonth();return a*12+o}function Tt(e,n,t){const r=Y(e,t),a=Y(n,t),o=+r-Ce(r),s=+a-Ce(a);return Math.round((o-s)/pt)}function Le(e){return de(e,{weekStartsOn:1})}function At(e){const n=N(e),t=n.getFullYear(),r=n.getMonth(),a=A(e,0);return a.setFullYear(t,r+1,0),a.setHours(0,0,0,0),a.getDate()}function Yt(e){return Math.trunc(+N(e)/1e3)}function Bt(e){const n=N(e),t=n.getMonth();return n.setFullYear(n.getFullYear(),t+1,0),n.setHours(0,0,0,0),n}function Ht(e,n){return Tt(Bt(e),P(e),n)+1}function le(e,n){const t=N(e),r=N(n);return t.getTime()>r.getTime()}function Ee(e,n){const t=N(e),r=N(n);return+t<+r}function fe(e,n){const t=N(e),r=N(n);return t.getFullYear()===r.getFullYear()&&t.getMonth()===r.getMonth()}function Kt(e,n){const t=N(e),r=N(n);return t.getFullYear()===r.getFullYear()}function ne(e,n){const t=N(e),r=t.getFullYear(),a=t.getDate(),o=A(e,0);o.setFullYear(r,n,15),o.setHours(0,0,0,0);const s=At(o);return t.setMonth(n,Math.min(a,s)),t}function Ne(e,n){const t=N(e);return isNaN(+t)?A(e,NaN):(t.setFullYear(n),t)}var p=function(){return p=Object.assign||function(n){for(var t,r=1,a=arguments.length;r<a;r++){t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o])}return n},p.apply(this,arguments)};function zt(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t}function Te(e,n,t){for(var r=0,a=n.length,o;r<a;r++)(o||!(r in n))&&(o||(o=Array.prototype.slice.call(n,0,r)),o[r]=n[r]);return e.concat(o||Array.prototype.slice.call(n))}function H(e){return e.mode==="multiple"}function K(e){return e.mode==="range"}function Z(e){return e.mode==="single"}var Ut={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"};function Vt(e,n){return W(e,"LLLL y",n)}function Gt(e,n){return W(e,"d",n)}function $t(e,n){return W(e,"LLLL",n)}function Zt(e){return"".concat(e)}function Xt(e,n){return W(e,"cccccc",n)}function qt(e,n){return W(e,"yyyy",n)}var Jt=Object.freeze({__proto__:null,formatCaption:Vt,formatDay:Gt,formatMonthCaption:$t,formatWeekNumber:Zt,formatWeekdayName:Xt,formatYearCaption:qt}),Qt=function(e,n,t){return W(e,"do MMMM (EEEE)",t)},en=function(){return"Month: "},tn=function(){return"Go to next month"},nn=function(){return"Go to previous month"},rn=function(e,n){return W(e,"cccc",n)},an=function(e){return"Week n. ".concat(e)},on=function(){return"Year: "},sn=Object.freeze({__proto__:null,labelDay:Qt,labelMonthDropdown:en,labelNext:tn,labelPrevious:nn,labelWeekNumber:an,labelWeekday:rn,labelYearDropdown:on});function ln(){var e="buttons",n=Ut,t=yt,r={},a={},o=1,s={},i=new Date;return{captionLayout:e,classNames:n,formatters:Jt,labels:sn,locale:t,modifiersClassNames:r,modifiers:a,numberOfMonths:o,styles:s,today:i,mode:"default"}}function dn(e){var n=e.fromYear,t=e.toYear,r=e.fromMonth,a=e.toMonth,o=e.fromDate,s=e.toDate;return r?o=P(r):n&&(o=new Date(n,0,1)),a?s=ue(a):t&&(s=new Date(t,11,31)),{fromDate:o?we(o):void 0,toDate:s?we(s):void 0}}var Ae=b.createContext(void 0);function un(e){var n,t=e.initialProps,r=ln(),a=dn(t),o=a.fromDate,s=a.toDate,i=(n=t.captionLayout)!==null&&n!==void 0?n:r.captionLayout;i!=="buttons"&&(!o||!s)&&(i="buttons");var d;(Z(t)||H(t)||K(t))&&(d=t.onSelect);var c=p(p(p({},r),t),{captionLayout:i,classNames:p(p({},r.classNames),t.classNames),components:p({},t.components),formatters:p(p({},r.formatters),t.formatters),fromDate:o,labels:p(p({},r.labels),t.labels),mode:t.mode||r.mode,modifiers:p(p({},r.modifiers),t.modifiers),modifiersClassNames:p(p({},r.modifiersClassNames),t.modifiersClassNames),onSelect:d,styles:p(p({},r.styles),t.styles),toDate:s});return l.jsx(Ae.Provider,{value:c,children:e.children})}function D(){var e=b.useContext(Ae);if(!e)throw new Error("useDayPicker must be used within a DayPickerProvider.");return e}function Ye(e){var n=D(),t=n.locale,r=n.classNames,a=n.styles,o=n.formatters.formatCaption;return l.jsx("div",{className:r.caption_label,style:a.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:o(e.displayMonth,{locale:t})})}function cn(e){return l.jsx("svg",p({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:l.jsx("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function Be(e){var n,t,r=e.onChange,a=e.value,o=e.children,s=e.caption,i=e.className,d=e.style,c=D(),u=(t=(n=c.components)===null||n===void 0?void 0:n.IconDropdown)!==null&&t!==void 0?t:cn;return l.jsxs("div",{className:i,style:d,children:[l.jsx("span",{className:c.classNames.vhidden,children:e["aria-label"]}),l.jsx("select",{name:e.name,"aria-label":e["aria-label"],className:c.classNames.dropdown,style:c.styles.dropdown,value:a,onChange:r,children:o}),l.jsxs("div",{className:c.classNames.caption_label,style:c.styles.caption_label,"aria-hidden":"true",children:[s,l.jsx(u,{className:c.classNames.dropdown_icon,style:c.styles.dropdown_icon})]})]})}function fn(e){var n,t=D(),r=t.fromDate,a=t.toDate,o=t.styles,s=t.locale,i=t.formatters.formatMonthCaption,d=t.classNames,c=t.components,u=t.labels.labelMonthDropdown;if(!r)return l.jsx(l.Fragment,{});if(!a)return l.jsx(l.Fragment,{});var f=[];if(Kt(r,a))for(var v=P(r),h=r.getMonth();h<=a.getMonth();h++)f.push(ne(v,h));else for(var v=P(new Date),h=0;h<=11;h++)f.push(ne(v,h));var g=function(x){var w=Number(x.target.value),C=ne(P(e.displayMonth),w);e.onChange(C)},_=(n=c==null?void 0:c.Dropdown)!==null&&n!==void 0?n:Be;return l.jsx(_,{name:"months","aria-label":u(),className:d.dropdown_month,style:o.dropdown_month,onChange:g,value:e.displayMonth.getMonth(),caption:i(e.displayMonth,{locale:s}),children:f.map(function(x){return l.jsx("option",{value:x.getMonth(),children:i(x,{locale:s})},x.getMonth())})})}function vn(e){var n,t=e.displayMonth,r=D(),a=r.fromDate,o=r.toDate,s=r.locale,i=r.styles,d=r.classNames,c=r.components,u=r.formatters.formatYearCaption,f=r.labels.labelYearDropdown,v=[];if(!a)return l.jsx(l.Fragment,{});if(!o)return l.jsx(l.Fragment,{});for(var h=a.getFullYear(),g=o.getFullYear(),_=h;_<=g;_++)v.push(Ne(bt(new Date),_));var x=function(C){var L=Ne(P(t),Number(C.target.value));e.onChange(L)},w=(n=c==null?void 0:c.Dropdown)!==null&&n!==void 0?n:Be;return l.jsx(w,{name:"years","aria-label":f(),className:d.dropdown_year,style:i.dropdown_year,onChange:x,value:t.getFullYear(),caption:u(t,{locale:s}),children:v.map(function(C){return l.jsx("option",{value:C.getFullYear(),children:u(C,{locale:s})},C.getFullYear())})})}function hn(e,n){var t=b.useState(e),r=t[0],a=t[1],o=n===void 0?r:n;return[o,a]}function mn(e){var n=e.month,t=e.defaultMonth,r=e.today,a=n||t||r||new Date,o=e.toDate,s=e.fromDate,i=e.numberOfMonths,d=i===void 0?1:i;if(o&&B(o,a)<0){var c=-1*(d-1);a=j(o,c)}return s&&B(a,s)<0&&(a=s),P(a)}function pn(){var e=D(),n=mn(e),t=hn(n,e.month),r=t[0],a=t[1],o=function(s){var i;if(!e.disableNavigation){var d=P(s);a(d),(i=e.onMonthChange)===null||i===void 0||i.call(e,d)}};return[r,o]}function yn(e,n){for(var t=n.reverseMonths,r=n.numberOfMonths,a=P(e),o=P(j(a,r)),s=B(o,a),i=[],d=0;d<s;d++){var c=j(a,d);i.push(c)}return t&&(i=i.reverse()),i}function bn(e,n){if(!n.disableNavigation){var t=n.toDate,r=n.pagedNavigation,a=n.numberOfMonths,o=a===void 0?1:a,s=r?o:1,i=P(e);if(!t)return j(i,s);var d=B(t,e);if(!(d<o))return j(i,s)}}function gn(e,n){if(!n.disableNavigation){var t=n.fromDate,r=n.pagedNavigation,a=n.numberOfMonths,o=a===void 0?1:a,s=r?o:1,i=P(e);if(!t)return j(i,-s);var d=B(i,t);if(!(d<=0))return j(i,-s)}}var He=b.createContext(void 0);function xn(e){var n=D(),t=pn(),r=t[0],a=t[1],o=yn(r,n),s=bn(r,n),i=gn(r,n),d=function(f){return o.some(function(v){return fe(f,v)})},c=function(f,v){d(f)||(v&&Ee(f,v)?a(j(f,1+n.numberOfMonths*-1)):a(f))},u={currentMonth:r,displayMonths:o,goToMonth:a,goToDate:c,previousMonth:i,nextMonth:s,isDateDisplayed:d};return l.jsx(He.Provider,{value:u,children:e.children})}function z(){var e=b.useContext(He);if(!e)throw new Error("useNavigation must be used within a NavigationProvider");return e}function Me(e){var n,t=D(),r=t.classNames,a=t.styles,o=t.components,s=z().goToMonth,i=function(u){s(j(u,e.displayIndex?-e.displayIndex:0))},d=(n=o==null?void 0:o.CaptionLabel)!==null&&n!==void 0?n:Ye,c=l.jsx(d,{id:e.id,displayMonth:e.displayMonth});return l.jsxs("div",{className:r.caption_dropdowns,style:a.caption_dropdowns,children:[l.jsx("div",{className:r.vhidden,children:c}),l.jsx(fn,{onChange:i,displayMonth:e.displayMonth}),l.jsx(vn,{onChange:i,displayMonth:e.displayMonth})]})}function _n(e){return l.jsx("svg",p({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:l.jsx("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function Dn(e){return l.jsx("svg",p({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:l.jsx("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var $=b.forwardRef(function(e,n){var t=D(),r=t.classNames,a=t.styles,o=[r.button_reset,r.button];e.className&&o.push(e.className);var s=o.join(" "),i=p(p({},a.button_reset),a.button);return e.style&&Object.assign(i,e.style),l.jsx("button",p({},e,{ref:n,type:"button",className:s,style:i}))});function Cn(e){var n,t,r=D(),a=r.dir,o=r.locale,s=r.classNames,i=r.styles,d=r.labels,c=d.labelPrevious,u=d.labelNext,f=r.components;if(!e.nextMonth&&!e.previousMonth)return l.jsx(l.Fragment,{});var v=c(e.previousMonth,{locale:o}),h=[s.nav_button,s.nav_button_previous].join(" "),g=u(e.nextMonth,{locale:o}),_=[s.nav_button,s.nav_button_next].join(" "),x=(n=f==null?void 0:f.IconRight)!==null&&n!==void 0?n:Dn,w=(t=f==null?void 0:f.IconLeft)!==null&&t!==void 0?t:_n;return l.jsxs("div",{className:s.nav,style:i.nav,children:[!e.hidePrevious&&l.jsx($,{name:"previous-month","aria-label":v,className:h,style:i.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:a==="rtl"?l.jsx(x,{className:s.nav_icon,style:i.nav_icon}):l.jsx(w,{className:s.nav_icon,style:i.nav_icon})}),!e.hideNext&&l.jsx($,{name:"next-month","aria-label":g,className:_,style:i.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:a==="rtl"?l.jsx(w,{className:s.nav_icon,style:i.nav_icon}):l.jsx(x,{className:s.nav_icon,style:i.nav_icon})})]})}function ke(e){var n=D().numberOfMonths,t=z(),r=t.previousMonth,a=t.nextMonth,o=t.goToMonth,s=t.displayMonths,i=s.findIndex(function(g){return fe(e.displayMonth,g)}),d=i===0,c=i===s.length-1,u=n>1&&(d||!c),f=n>1&&(c||!d),v=function(){r&&o(r)},h=function(){a&&o(a)};return l.jsx(Cn,{displayMonth:e.displayMonth,hideNext:u,hidePrevious:f,nextMonth:a,previousMonth:r,onPreviousClick:v,onNextClick:h})}function wn(e){var n,t=D(),r=t.classNames,a=t.disableNavigation,o=t.styles,s=t.captionLayout,i=t.components,d=(n=i==null?void 0:i.CaptionLabel)!==null&&n!==void 0?n:Ye,c;return a?c=l.jsx(d,{id:e.id,displayMonth:e.displayMonth}):s==="dropdown"?c=l.jsx(Me,{displayMonth:e.displayMonth,id:e.id}):s==="dropdown-buttons"?c=l.jsxs(l.Fragment,{children:[l.jsx(Me,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),l.jsx(ke,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):c=l.jsxs(l.Fragment,{children:[l.jsx(d,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),l.jsx(ke,{displayMonth:e.displayMonth,id:e.id})]}),l.jsx("div",{className:r.caption,style:o.caption,children:c})}function Nn(e){var n=D(),t=n.footer,r=n.styles,a=n.classNames.tfoot;return t?l.jsx("tfoot",{className:a,style:r.tfoot,children:l.jsx("tr",{children:l.jsx("td",{colSpan:8,children:t})})}):l.jsx(l.Fragment,{})}function Mn(e,n,t){for(var r=t?ce(new Date):Y(new Date,{locale:e,weekStartsOn:n}),a=[],o=0;o<7;o++){var s=M(r,o);a.push(s)}return a}function kn(){var e=D(),n=e.classNames,t=e.styles,r=e.showWeekNumber,a=e.locale,o=e.weekStartsOn,s=e.ISOWeek,i=e.formatters.formatWeekdayName,d=e.labels.labelWeekday,c=Mn(a,o,s);return l.jsxs("tr",{style:t.head_row,className:n.head_row,children:[r&&l.jsx("td",{style:t.head_cell,className:n.head_cell}),c.map(function(u,f){return l.jsx("th",{scope:"col",className:n.head_cell,style:t.head_cell,"aria-label":d(u,{locale:a}),children:i(u,{locale:a})},f)})]})}function Pn(){var e,n=D(),t=n.classNames,r=n.styles,a=n.components,o=(e=a==null?void 0:a.HeadRow)!==null&&e!==void 0?e:kn;return l.jsx("thead",{style:r.head,className:t.head,children:l.jsx(o,{})})}function jn(e){var n=D(),t=n.locale,r=n.formatters.formatDay;return l.jsx(l.Fragment,{children:r(e.date,{locale:t})})}var ve=b.createContext(void 0);function On(e){if(!H(e.initialProps)){var n={selected:void 0,modifiers:{disabled:[]}};return l.jsx(ve.Provider,{value:n,children:e.children})}return l.jsx(Sn,{initialProps:e.initialProps,children:e.children})}function Sn(e){var n=e.initialProps,t=e.children,r=n.selected,a=n.min,o=n.max,s=function(c,u,f){var v,h;(v=n.onDayClick)===null||v===void 0||v.call(n,c,u,f);var g=!!(u.selected&&a&&(r==null?void 0:r.length)===a);if(!g){var _=!!(!u.selected&&o&&(r==null?void 0:r.length)===o);if(!_){var x=r?Te([],r):[];if(u.selected){var w=x.findIndex(function(C){return k(c,C)});x.splice(w,1)}else x.push(c);(h=n.onSelect)===null||h===void 0||h.call(n,x,c,u,f)}}},i={disabled:[]};r&&i.disabled.push(function(c){var u=o&&r.length>o-1,f=r.some(function(v){return k(v,c)});return!!(u&&!f)});var d={selected:r,onDayClick:s,modifiers:i};return l.jsx(ve.Provider,{value:d,children:t})}function he(){var e=b.useContext(ve);if(!e)throw new Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}function Rn(e,n){var t=n||{},r=t.from,a=t.to;return r&&a?k(a,e)&&k(r,e)?void 0:k(a,e)?{from:a,to:void 0}:k(r,e)?void 0:le(r,e)?{from:e,to:a}:{from:r,to:e}:a?le(e,a)?{from:a,to:e}:{from:e,to:a}:r?Ee(e,r)?{from:e,to:r}:{from:r,to:e}:{from:e,to:void 0}}var me=b.createContext(void 0);function Fn(e){if(!K(e.initialProps)){var n={selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}};return l.jsx(me.Provider,{value:n,children:e.children})}return l.jsx(Wn,{initialProps:e.initialProps,children:e.children})}function Wn(e){var n=e.initialProps,t=e.children,r=n.selected,a=r||{},o=a.from,s=a.to,i=n.min,d=n.max,c=function(h,g,_){var x,w;(x=n.onDayClick)===null||x===void 0||x.call(n,h,g,_);var C=Rn(h,r);(w=n.onSelect)===null||w===void 0||w.call(n,C,h,g,_)},u={range_start:[],range_end:[],range_middle:[],disabled:[]};if(o?(u.range_start=[o],s?(u.range_end=[s],k(o,s)||(u.range_middle=[{after:o,before:s}])):u.range_end=[o]):s&&(u.range_start=[s],u.range_end=[s]),i&&(o&&!s&&u.disabled.push({after:te(o,i-1),before:M(o,i-1)}),o&&s&&u.disabled.push({after:o,before:M(o,i-1)}),!o&&s&&u.disabled.push({after:te(s,i-1),before:M(s,i-1)})),d){if(o&&!s&&(u.disabled.push({before:M(o,-d+1)}),u.disabled.push({after:M(o,d-1)})),o&&s){var f=R(s,o)+1,v=d-f;u.disabled.push({before:te(o,v)}),u.disabled.push({after:M(s,v)})}!o&&s&&(u.disabled.push({before:M(s,-d+1)}),u.disabled.push({after:M(s,d-1)}))}return l.jsx(me.Provider,{value:{selected:r,onDayClick:c,modifiers:u},children:t})}function pe(){var e=b.useContext(me);if(!e)throw new Error("useSelectRange must be used within a SelectRangeProvider");return e}function G(e){return Array.isArray(e)?Te([],e):e!==void 0?[e]:[]}function In(e){var n={};return Object.entries(e).forEach(function(t){var r=t[0],a=t[1];n[r]=G(a)}),n}var O;(function(e){e.Outside="outside",e.Disabled="disabled",e.Selected="selected",e.Hidden="hidden",e.Today="today",e.RangeStart="range_start",e.RangeEnd="range_end",e.RangeMiddle="range_middle"})(O||(O={}));var Ln=O.Selected,S=O.Disabled,En=O.Hidden,Tn=O.Today,re=O.RangeEnd,ae=O.RangeMiddle,oe=O.RangeStart,An=O.Outside;function Yn(e,n,t){var r,a=(r={},r[Ln]=G(e.selected),r[S]=G(e.disabled),r[En]=G(e.hidden),r[Tn]=[e.today],r[re]=[],r[ae]=[],r[oe]=[],r[An]=[],r);return e.fromDate&&a[S].push({before:e.fromDate}),e.toDate&&a[S].push({after:e.toDate}),H(e)?a[S]=a[S].concat(n.modifiers[S]):K(e)&&(a[S]=a[S].concat(t.modifiers[S]),a[oe]=t.modifiers[oe],a[ae]=t.modifiers[ae],a[re]=t.modifiers[re]),a}var Ke=b.createContext(void 0);function Bn(e){var n=D(),t=he(),r=pe(),a=Yn(n,t,r),o=In(n.modifiers),s=p(p({},a),o);return l.jsx(Ke.Provider,{value:s,children:e.children})}function ze(){var e=b.useContext(Ke);if(!e)throw new Error("useModifiers must be used within a ModifiersProvider");return e}function Hn(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function Kn(e){return!!(e&&typeof e=="object"&&"from"in e)}function zn(e){return!!(e&&typeof e=="object"&&"after"in e)}function Un(e){return!!(e&&typeof e=="object"&&"before"in e)}function Vn(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function Gn(e,n){var t,r=n.from,a=n.to;if(r&&a){var o=R(a,r)<0;o&&(t=[a,r],r=t[0],a=t[1]);var s=R(e,r)>=0&&R(a,e)>=0;return s}return a?k(a,e):r?k(r,e):!1}function $n(e){return Se(e)}function Zn(e){return Array.isArray(e)&&e.every(Se)}function Xn(e,n){return n.some(function(t){if(typeof t=="boolean")return t;if($n(t))return k(e,t);if(Zn(t))return t.includes(e);if(Kn(t))return Gn(e,t);if(Vn(t))return t.dayOfWeek.includes(e.getDay());if(Hn(t)){var r=R(t.before,e),a=R(t.after,e),o=r>0,s=a<0,i=le(t.before,t.after);return i?s&&o:o||s}return zn(t)?R(e,t.after)>0:Un(t)?R(t.before,e)>0:typeof t=="function"?t(e):!1})}function ye(e,n,t){var r=Object.keys(n).reduce(function(o,s){var i=n[s];return Xn(e,i)&&o.push(s),o},[]),a={};return r.forEach(function(o){return a[o]=!0}),t&&!fe(e,t)&&(a.outside=!0),a}function qn(e,n){for(var t=P(e[0]),r=ue(e[e.length-1]),a,o,s=t;s<=r;){var i=ye(s,n),d=!i.disabled&&!i.hidden;if(!d){s=M(s,1);continue}if(i.selected)return s;i.today&&!o&&(o=s),a||(a=s),s=M(s,1)}return o||a}var Jn=365;function Ue(e,n){var t=n.moveBy,r=n.direction,a=n.context,o=n.modifiers,s=n.retry,i=s===void 0?{count:0,lastFocused:e}:s,d=a.weekStartsOn,c=a.fromDate,u=a.toDate,f=a.locale,v={day:M,week:ie,month:j,year:It,startOfWeek:function(x){return a.ISOWeek?ce(x):Y(x,{locale:f,weekStartsOn:d})},endOfWeek:function(x){return a.ISOWeek?Le(x):de(x,{locale:f,weekStartsOn:d})}},h=v[t](e,r==="after"?1:-1);r==="before"&&c?h=Lt([c,h]):r==="after"&&u&&(h=Et([u,h]));var g=!0;if(o){var _=ye(h,o);g=!_.disabled&&!_.hidden}return g?h:i.count>Jn?i.lastFocused:Ue(h,{moveBy:t,direction:r,context:a,modifiers:o,retry:p(p({},i),{count:i.count+1})})}var Ve=b.createContext(void 0);function Qn(e){var n=z(),t=ze(),r=b.useState(),a=r[0],o=r[1],s=b.useState(),i=s[0],d=s[1],c=qn(n.displayMonths,t),u=a??(i&&n.isDateDisplayed(i))?i:c,f=function(){d(a),o(void 0)},v=function(x){o(x)},h=D(),g=function(x,w){if(a){var C=Ue(a,{moveBy:x,direction:w,context:h,modifiers:t});k(a,C)||(n.goToDate(C,a),v(C))}},_={focusedDay:a,focusTarget:u,blur:f,focus:v,focusDayAfter:function(){return g("day","after")},focusDayBefore:function(){return g("day","before")},focusWeekAfter:function(){return g("week","after")},focusWeekBefore:function(){return g("week","before")},focusMonthBefore:function(){return g("month","before")},focusMonthAfter:function(){return g("month","after")},focusYearBefore:function(){return g("year","before")},focusYearAfter:function(){return g("year","after")},focusStartOfWeek:function(){return g("startOfWeek","before")},focusEndOfWeek:function(){return g("endOfWeek","after")}};return l.jsx(Ve.Provider,{value:_,children:e.children})}function be(){var e=b.useContext(Ve);if(!e)throw new Error("useFocusContext must be used within a FocusProvider");return e}function er(e,n){var t=ze(),r=ye(e,t,n);return r}var ge=b.createContext(void 0);function tr(e){if(!Z(e.initialProps)){var n={selected:void 0};return l.jsx(ge.Provider,{value:n,children:e.children})}return l.jsx(nr,{initialProps:e.initialProps,children:e.children})}function nr(e){var n=e.initialProps,t=e.children,r=function(o,s,i){var d,c,u;if((d=n.onDayClick)===null||d===void 0||d.call(n,o,s,i),s.selected&&!n.required){(c=n.onSelect)===null||c===void 0||c.call(n,void 0,o,s,i);return}(u=n.onSelect)===null||u===void 0||u.call(n,o,o,s,i)},a={selected:n.selected,onDayClick:r};return l.jsx(ge.Provider,{value:a,children:t})}function Ge(){var e=b.useContext(ge);if(!e)throw new Error("useSelectSingle must be used within a SelectSingleProvider");return e}function rr(e,n){var t=D(),r=Ge(),a=he(),o=pe(),s=be(),i=s.focusDayAfter,d=s.focusDayBefore,c=s.focusWeekAfter,u=s.focusWeekBefore,f=s.blur,v=s.focus,h=s.focusMonthBefore,g=s.focusMonthAfter,_=s.focusYearBefore,x=s.focusYearAfter,w=s.focusStartOfWeek,C=s.focusEndOfWeek,L=function(y){var m,J,Q,ee;Z(t)?(m=r.onDayClick)===null||m===void 0||m.call(r,e,n,y):H(t)?(J=a.onDayClick)===null||J===void 0||J.call(a,e,n,y):K(t)?(Q=o.onDayClick)===null||Q===void 0||Q.call(o,e,n,y):(ee=t.onDayClick)===null||ee===void 0||ee.call(t,e,n,y)},X=function(y){var m;v(e),(m=t.onDayFocus)===null||m===void 0||m.call(t,e,n,y)},q=function(y){var m;f(),(m=t.onDayBlur)===null||m===void 0||m.call(t,e,n,y)},E=function(y){var m;(m=t.onDayMouseEnter)===null||m===void 0||m.call(t,e,n,y)},ot=function(y){var m;(m=t.onDayMouseLeave)===null||m===void 0||m.call(t,e,n,y)},st=function(y){var m;(m=t.onDayPointerEnter)===null||m===void 0||m.call(t,e,n,y)},it=function(y){var m;(m=t.onDayPointerLeave)===null||m===void 0||m.call(t,e,n,y)},lt=function(y){var m;(m=t.onDayTouchCancel)===null||m===void 0||m.call(t,e,n,y)},dt=function(y){var m;(m=t.onDayTouchEnd)===null||m===void 0||m.call(t,e,n,y)},ut=function(y){var m;(m=t.onDayTouchMove)===null||m===void 0||m.call(t,e,n,y)},ct=function(y){var m;(m=t.onDayTouchStart)===null||m===void 0||m.call(t,e,n,y)},ft=function(y){var m;(m=t.onDayKeyUp)===null||m===void 0||m.call(t,e,n,y)},vt=function(y){var m;switch(y.key){case"ArrowLeft":y.preventDefault(),y.stopPropagation(),t.dir==="rtl"?i():d();break;case"ArrowRight":y.preventDefault(),y.stopPropagation(),t.dir==="rtl"?d():i();break;case"ArrowDown":y.preventDefault(),y.stopPropagation(),c();break;case"ArrowUp":y.preventDefault(),y.stopPropagation(),u();break;case"PageUp":y.preventDefault(),y.stopPropagation(),y.shiftKey?_():h();break;case"PageDown":y.preventDefault(),y.stopPropagation(),y.shiftKey?x():g();break;case"Home":y.preventDefault(),y.stopPropagation(),w();break;case"End":y.preventDefault(),y.stopPropagation(),C();break}(m=t.onDayKeyDown)===null||m===void 0||m.call(t,e,n,y)},ht={onClick:L,onFocus:X,onBlur:q,onKeyDown:vt,onKeyUp:ft,onMouseEnter:E,onMouseLeave:ot,onPointerEnter:st,onPointerLeave:it,onTouchCancel:lt,onTouchEnd:dt,onTouchMove:ut,onTouchStart:ct};return ht}function ar(){var e=D(),n=Ge(),t=he(),r=pe(),a=Z(e)?n.selected:H(e)?t.selected:K(e)?r.selected:void 0;return a}function or(e){return Object.values(O).includes(e)}function sr(e,n){var t=[e.classNames.day];return Object.keys(n).forEach(function(r){var a=e.modifiersClassNames[r];if(a)t.push(a);else if(or(r)){var o=e.classNames["day_".concat(r)];o&&t.push(o)}}),t}function ir(e,n){var t=p({},e.styles.day);return Object.keys(n).forEach(function(r){var a;t=p(p({},t),(a=e.modifiersStyles)===null||a===void 0?void 0:a[r])}),t}function lr(e,n,t){var r,a,o,s=D(),i=be(),d=er(e,n),c=rr(e,d),u=ar(),f=!!(s.onDayClick||s.mode!=="default");b.useEffect(function(){var E;d.outside||i.focusedDay&&f&&k(i.focusedDay,e)&&((E=t.current)===null||E===void 0||E.focus())},[i.focusedDay,e,t,f,d.outside]);var v=sr(s,d).join(" "),h=ir(s,d),g=!!(d.outside&&!s.showOutsideDays||d.hidden),_=(o=(a=s.components)===null||a===void 0?void 0:a.DayContent)!==null&&o!==void 0?o:jn,x=l.jsx(_,{date:e,displayMonth:n,activeModifiers:d}),w={style:h,className:v,children:x,role:"gridcell"},C=i.focusTarget&&k(i.focusTarget,e)&&!d.outside,L=i.focusedDay&&k(i.focusedDay,e),X=p(p(p({},w),(r={disabled:d.disabled,role:"gridcell"},r["aria-selected"]=d.selected,r.tabIndex=L||C?0:-1,r)),c),q={isButton:f,isHidden:g,activeModifiers:d,selectedDays:u,buttonProps:X,divProps:w};return q}function dr(e){var n=b.useRef(null),t=lr(e.date,e.displayMonth,n);return t.isHidden?l.jsx("div",{role:"gridcell"}):t.isButton?l.jsx($,p({name:"day",ref:n},t.buttonProps)):l.jsx("div",p({},t.divProps))}function ur(e){var n=e.number,t=e.dates,r=D(),a=r.onWeekNumberClick,o=r.styles,s=r.classNames,i=r.locale,d=r.labels.labelWeekNumber,c=r.formatters.formatWeekNumber,u=c(Number(n),{locale:i});if(!a)return l.jsx("span",{className:s.weeknumber,style:o.weeknumber,children:u});var f=d(Number(n),{locale:i}),v=function(h){a(n,t,h)};return l.jsx($,{name:"week-number","aria-label":f,className:s.weeknumber,style:o.weeknumber,onClick:v,children:u})}function cr(e){var n,t,r=D(),a=r.styles,o=r.classNames,s=r.showWeekNumber,i=r.components,d=(n=i==null?void 0:i.Day)!==null&&n!==void 0?n:dr,c=(t=i==null?void 0:i.WeekNumber)!==null&&t!==void 0?t:ur,u;return s&&(u=l.jsx("td",{className:o.cell,style:a.cell,children:l.jsx(c,{number:e.weekNumber,dates:e.dates})})),l.jsxs("tr",{className:o.row,style:a.row,children:[u,e.dates.map(function(f){return l.jsx("td",{className:o.cell,style:a.cell,role:"presentation",children:l.jsx(d,{displayMonth:e.displayMonth,date:f})},Yt(f))})]})}function Pe(e,n,t){for(var r=t!=null&&t.ISOWeek?Le(n):de(n,t),a=t!=null&&t.ISOWeek?ce(e):Y(e,t),o=R(r,a),s=[],i=0;i<=o;i++)s.push(M(a,i));var d=s.reduce(function(c,u){var f=t!=null&&t.ISOWeek?gt(u):xt(u,t),v=c.find(function(h){return h.weekNumber===f});return v?(v.dates.push(u),c):(c.push({weekNumber:f,dates:[u]}),c)},[]);return d}function fr(e,n){var t=Pe(P(e),ue(e),n);if(n!=null&&n.useFixedWeeks){var r=Ht(e,n);if(r<6){var a=t[t.length-1],o=a.dates[a.dates.length-1],s=ie(o,6-r),i=Pe(ie(o,1),s,n);t.push.apply(t,i)}}return t}function vr(e){var n,t,r,a=D(),o=a.locale,s=a.classNames,i=a.styles,d=a.hideHead,c=a.fixedWeeks,u=a.components,f=a.weekStartsOn,v=a.firstWeekContainsDate,h=a.ISOWeek,g=fr(e.displayMonth,{useFixedWeeks:!!c,ISOWeek:h,locale:o,weekStartsOn:f,firstWeekContainsDate:v}),_=(n=u==null?void 0:u.Head)!==null&&n!==void 0?n:Pn,x=(t=u==null?void 0:u.Row)!==null&&t!==void 0?t:cr,w=(r=u==null?void 0:u.Footer)!==null&&r!==void 0?r:Nn;return l.jsxs("table",{id:e.id,className:s.table,style:i.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!d&&l.jsx(_,{}),l.jsx("tbody",{className:s.tbody,style:i.tbody,children:g.map(function(C){return l.jsx(x,{displayMonth:e.displayMonth,dates:C.dates,weekNumber:C.weekNumber},C.weekNumber)})}),l.jsx(w,{displayMonth:e.displayMonth})]})}function hr(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var mr=hr()?b.useLayoutEffect:b.useEffect,se=!1,pr=0;function je(){return"react-day-picker-".concat(++pr)}function yr(e){var n,t=e??(se?je():null),r=b.useState(t),a=r[0],o=r[1];return mr(function(){a===null&&o(je())},[]),b.useEffect(function(){se===!1&&(se=!0)},[]),(n=e??a)!==null&&n!==void 0?n:void 0}function br(e){var n,t,r=D(),a=r.dir,o=r.classNames,s=r.styles,i=r.components,d=z().displayMonths,c=yr(r.id?"".concat(r.id,"-").concat(e.displayIndex):void 0),u=r.id?"".concat(r.id,"-grid-").concat(e.displayIndex):void 0,f=[o.month],v=s.month,h=e.displayIndex===0,g=e.displayIndex===d.length-1,_=!h&&!g;a==="rtl"&&(n=[h,g],g=n[0],h=n[1]),h&&(f.push(o.caption_start),v=p(p({},v),s.caption_start)),g&&(f.push(o.caption_end),v=p(p({},v),s.caption_end)),_&&(f.push(o.caption_between),v=p(p({},v),s.caption_between));var x=(t=i==null?void 0:i.Caption)!==null&&t!==void 0?t:wn;return l.jsxs("div",{className:f.join(" "),style:v,children:[l.jsx(x,{id:c,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),l.jsx(vr,{id:u,"aria-labelledby":c,displayMonth:e.displayMonth})]},e.displayIndex)}function gr(e){var n=D(),t=n.classNames,r=n.styles;return l.jsx("div",{className:t.months,style:r.months,children:e.children})}function xr(e){var n,t,r=e.initialProps,a=D(),o=be(),s=z(),i=b.useState(!1),d=i[0],c=i[1];b.useEffect(function(){a.initialFocus&&o.focusTarget&&(d||(o.focus(o.focusTarget),c(!0)))},[a.initialFocus,d,o.focus,o.focusTarget,o]);var u=[a.classNames.root,a.className];a.numberOfMonths>1&&u.push(a.classNames.multiple_months),a.showWeekNumber&&u.push(a.classNames.with_weeknumber);var f=p(p({},a.styles.root),a.style),v=Object.keys(r).filter(function(g){return g.startsWith("data-")}).reduce(function(g,_){var x;return p(p({},g),(x={},x[_]=r[_],x))},{}),h=(t=(n=r.components)===null||n===void 0?void 0:n.Months)!==null&&t!==void 0?t:gr;return l.jsx("div",p({className:u.join(" "),style:f,dir:a.dir,id:a.id,nonce:r.nonce,title:r.title,lang:r.lang},v,{children:l.jsx(h,{children:s.displayMonths.map(function(g,_){return l.jsx(br,{displayIndex:_,displayMonth:g},_)})})}))}function _r(e){var n=e.children,t=zt(e,["children"]);return l.jsx(un,{initialProps:t,children:l.jsx(xn,{children:l.jsx(tr,{initialProps:t,children:l.jsx(On,{initialProps:t,children:l.jsx(Fn,{initialProps:t,children:l.jsx(Bn,{children:l.jsx(Qn,{children:n})})})})})})})}function Dr(e){return l.jsx(_r,p({},e,{children:l.jsx(xr,{initialProps:e})}))}function Cr({className:e,classNames:n,showOutsideDays:t=!0,...r}){return l.jsx(Dr,{showOutsideDays:t,className:V("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:V(De({variant:"outline"}),"min-h-[44px] min-w-[44px] bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md min-w-[44px] font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"min-h-[44px] min-w-[44px] text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:V(De({variant:"ghost"}),"min-h-[44px] min-w-[44px] p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},components:{IconLeft:({...a})=>l.jsx(_t,{className:"h-4 w-4"}),IconRight:({...a})=>l.jsx(Dt,{className:"h-4 w-4"})},...r})}Cr.displayName="Calendar";var xe="Popover",[$e,Br]=Rt(xe,[Re]),U=Re(),[wr,F]=$e(xe),Ze=e=>{const{__scopePopover:n,children:t,open:r,defaultOpen:a,onOpenChange:o,modal:s=!1}=e,i=U(n),d=b.useRef(null),[c,u]=b.useState(!1),[f=!1,v]=Ft({prop:r,defaultProp:a,onChange:o});return l.jsx(St,{...i,children:l.jsx(wr,{scope:n,contentId:Wt(),triggerRef:d,open:f,onOpenChange:v,onOpenToggle:b.useCallback(()=>v(h=>!h),[v]),hasCustomAnchor:c,onCustomAnchorAdd:b.useCallback(()=>u(!0),[]),onCustomAnchorRemove:b.useCallback(()=>u(!1),[]),modal:s,children:t})})};Ze.displayName=xe;var Xe="PopoverAnchor",Nr=b.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,a=F(Xe,t),o=U(t),{onCustomAnchorAdd:s,onCustomAnchorRemove:i}=a;return b.useEffect(()=>(s(),()=>i()),[s,i]),l.jsx(Fe,{...o,...r,ref:n})});Nr.displayName=Xe;var qe="PopoverTrigger",Je=b.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,a=F(qe,t),o=U(t),s=Oe(n,a.triggerRef),i=l.jsx(We.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":rt(a.open),...r,ref:s,onClick:T(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?i:l.jsx(Fe,{asChild:!0,...o,children:i})});Je.displayName=qe;var _e="PopoverPortal",[Mr,kr]=$e(_e,{forceMount:void 0}),Qe=e=>{const{__scopePopover:n,forceMount:t,children:r,container:a}=e,o=F(_e,n);return l.jsx(Mr,{scope:n,forceMount:t,children:l.jsx(Ie,{present:t||o.open,children:l.jsx(Ot,{asChild:!0,container:a,children:r})})})};Qe.displayName=_e;var I="PopoverContent",et=b.forwardRef((e,n)=>{const t=kr(I,e.__scopePopover),{forceMount:r=t.forceMount,...a}=e,o=F(I,e.__scopePopover);return l.jsx(Ie,{present:r||o.open,children:o.modal?l.jsx(Pr,{...a,ref:n}):l.jsx(jr,{...a,ref:n})})});et.displayName=I;var Pr=b.forwardRef((e,n)=>{const t=F(I,e.__scopePopover),r=b.useRef(null),a=Oe(n,r),o=b.useRef(!1);return b.useEffect(()=>{const s=r.current;if(s)return Ct(s)},[]),l.jsx(wt,{as:mt,allowPinchZoom:!0,children:l.jsx(tt,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:T(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),o.current||(i=t.triggerRef.current)==null||i.focus()}),onPointerDownOutside:T(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,d=i.button===0&&i.ctrlKey===!0,c=i.button===2||d;o.current=c},{checkForDefaultPrevented:!1}),onFocusOutside:T(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1})})})}),jr=b.forwardRef((e,n)=>{const t=F(I,e.__scopePopover),r=b.useRef(!1),a=b.useRef(!1);return l.jsx(tt,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var s,i;(s=e.onCloseAutoFocus)==null||s.call(e,o),o.defaultPrevented||(r.current||(i=t.triggerRef.current)==null||i.focus(),o.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:o=>{var d,c;(d=e.onInteractOutside)==null||d.call(e,o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const s=o.target;((c=t.triggerRef.current)==null?void 0:c.contains(s))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&a.current&&o.preventDefault()}})}),tt=b.forwardRef((e,n)=>{const{__scopePopover:t,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o,disableOutsidePointerEvents:s,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:u,...f}=e,v=F(I,t),h=U(t);return Nt(),l.jsx(Mt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:l.jsx(kt,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:u,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:c,onDismiss:()=>v.onOpenChange(!1),children:l.jsx(Pt,{"data-state":rt(v.open),role:"dialog",id:v.contentId,...h,...f,ref:n,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),nt="PopoverClose",Or=b.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,a=F(nt,t);return l.jsx(We.button,{type:"button",...r,ref:n,onClick:T(e.onClick,()=>a.onOpenChange(!1))})});Or.displayName=nt;var Sr="PopoverArrow",Rr=b.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,a=U(t);return l.jsx(jt,{...a,...r,ref:n})});Rr.displayName=Sr;function rt(e){return e?"open":"closed"}var Fr=Ze,Wr=Je,Ir=Qe,at=et;const Hr=Fr,Kr=Wr,Lr=b.forwardRef(({className:e,align:n="center",sideOffset:t=4,...r},a)=>l.jsx(Ir,{children:l.jsx(at,{ref:a,align:n,sideOffset:t,className:V("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));Lr.displayName=at.displayName;export{Cr as C,Hr as P,Kr as a,Lr as b};
