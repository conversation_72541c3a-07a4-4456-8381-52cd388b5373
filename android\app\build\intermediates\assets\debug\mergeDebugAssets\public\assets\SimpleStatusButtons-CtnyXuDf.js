import{r as c,j as t}from"./index-Cmt5neWh.js";const m=({currentStatus:l,onStatusUpdate:d,isUpdating:r})=>{var f;const[o,s]=c.useState(null),[u,a]=c.useState(""),[p,i]=c.useState(!1),x=[{status:"Angetroffen → Sale",label:"💰 Verkauf!",bgColor:"bg-green-600 hover:bg-green-700"},{status:"Angetroffen → Termin",label:"📅 Termin vereinbaren",bgColor:"bg-blue-600 hover:bg-blue-700"},{status:"Angetroffen → Kein Interesse",label:"❌ Kein Interesse",bgColor:"bg-neutral-500 hover:bg-neutral-600"},{status:"N/A",label:"❓ Nicht angetroffen",bgColor:"bg-red-600 hover:bg-red-700"}],g=e=>{s(e),i(!0)},b=()=>{o&&(d(o,u.trim()||void 0),s(null),a(""),i(!1))},h=()=>{s(null),a(""),i(!1)};return t.jsxs("div",{style:{padding:"16px"},children:[t.jsx("h3",{style:{fontSize:"20px",fontWeight:"bold",textAlign:"center",marginBottom:"24px",color:"#1f2937"},children:"Was ist passiert?"}),p?t.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[t.jsx("div",{style:{padding:"16px",borderRadius:"12px",backgroundColor:"#f3f4f6",border:"2px solid #e5e7eb",textAlign:"center"},children:t.jsxs("div",{style:{fontSize:"16px",fontWeight:"600",color:"#374151"},children:["Gewählt: ",(f=x.find(e=>e.status===o))==null?void 0:f.label]})}),t.jsxs("div",{children:[t.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"600",color:"#374151",marginBottom:"8px"},children:"Kommentar (optional)"}),t.jsx("textarea",{value:u,onChange:e=>a(e.target.value),placeholder:"z.B. 'Niemand zu Hause', 'Hund gebellt', 'Termin für morgen'...",style:{width:"100%",minHeight:"80px",padding:"12px",borderRadius:"8px",border:"2px solid #e5e7eb",fontSize:"16px",fontFamily:"inherit",resize:"vertical",outline:"none",transition:"border-color 0.2s ease"},onFocus:e=>{e.target.style.borderColor="#3b82f6"},onBlur:e=>{e.target.style.borderColor="#e5e7eb"},onKeyDown:e=>{e.ctrlKey&&e.key==="Enter"&&b()}}),t.jsx("div",{style:{fontSize:"12px",color:"#6b7280",marginTop:"4px"},children:"💡 Tipp: Strg+Enter zum schnellen Speichern"})]}),t.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[t.jsx("button",{onClick:()=>{o&&(d(o),s(null),a(""),i(!1))},disabled:r||!o,style:{width:"100%",height:"48px",borderRadius:"8px",fontSize:"16px",fontWeight:"600",color:"white",backgroundColor:"#3b82f6",border:"none",cursor:r||!o?"not-allowed":"pointer",opacity:r||!o?.5:1,transition:"all 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"},onMouseOver:e=>{!r&&o&&(e.currentTarget.style.backgroundColor="#2563eb")},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#3b82f6"},children:r?"⏳ Speichern...":"⚡ Schnell speichern (ohne Kommentar)"}),t.jsxs("div",{style:{display:"flex",gap:"12px"},children:[t.jsx("button",{onClick:h,disabled:r,style:{flex:"1",height:"48px",borderRadius:"8px",fontSize:"16px",fontWeight:"600",color:"#6b7280",backgroundColor:"#f9fafb",border:"2px solid #e5e7eb",cursor:r?"not-allowed":"pointer",opacity:r?.5:1,transition:"all 0.2s ease"},onMouseOver:e=>{r||(e.currentTarget.style.backgroundColor="#f3f4f6",e.currentTarget.style.borderColor="#d1d5db")},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f9fafb",e.currentTarget.style.borderColor="#e5e7eb"},children:"Abbrechen"}),t.jsx("button",{onClick:b,disabled:r||!o,style:{flex:"2",height:"48px",borderRadius:"8px",fontSize:"16px",fontWeight:"600",color:"white",backgroundColor:"#10b981",border:"none",cursor:r||!o?"not-allowed":"pointer",opacity:r||!o?.5:1,transition:"all 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"},onMouseOver:e=>{!r&&o&&(e.currentTarget.style.backgroundColor="#059669")},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#10b981"},children:r?"⏳ Speichern...":"💾 Mit Kommentar speichern"})]})]})]}):t.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:x.map(e=>t.jsx("button",{onClick:()=>g(e.status),disabled:r||l===e.status,style:{width:"100%",height:"64px",borderRadius:"12px",fontSize:"18px",fontWeight:"600",color:"white",border:"none",cursor:r||l===e.status?"not-allowed":"pointer",opacity:r||l===e.status?.5:1,transition:"all 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},className:e.bgColor,onMouseDown:n=>{n.currentTarget.style.transform="scale(0.95)"},onMouseUp:n=>{n.currentTarget.style.transform="scale(1)"},onMouseLeave:n=>{n.currentTarget.style.transform="scale(1)"},children:e.label},e.status))}),l!=="N/A"&&t.jsxs("div",{style:{marginTop:"16px",textAlign:"center",fontSize:"14px",color:"#059669",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[t.jsx("span",{children:"✅"}),t.jsxs("span",{children:["Aktueller Status: ",l]})]})]})};export{m as S};
