const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Index-BGwUgqRu.js","assets/MainLayout-wyzz138D.js","assets/input-BK13BBqa.js","assets/tabs-BJh52NhZ.js","assets/index-C8JwcrUT.js","assets/ModernAddressForm-C2Sa-x79.js","assets/label-NwAA2N0T.js","assets/check-abM7k-xd.js","assets/EFHVisitTracker-BC_kU7m6.js","assets/Button-ETlvKXsU.js","assets/loader-circle-Brkx1kW_.js","assets/Card-CELKqcN7.js","assets/badge-XkNoLG2o.js","assets/SimpleStatusButtons-CtnyXuDf.js","assets/clock-DhYcPjhn.js","assets/dialog-BFTaoFLK.js","assets/popover-BPm3A8JC.js","assets/subDays-BLJlWEqr.js","assets/useSwipeGestures-49dHBwDT.js","assets/zap-BNKcOEAu.js","assets/user-x-DrICOSZg.js","assets/geocodingService-r9QfKNdP.js","assets/navigation-B6ya6VuX.js","assets/Login-BUsjEoap.js","assets/switch-DOMi03D3.js","assets/index-CEhV84jC.js","assets/index-CB2fuKla.js","assets/circle-check-big-DK6RP7UF.js","assets/eye-off-j0pHjYk_.js","assets/eye-DwLHs0eg.js","assets/circle-alert-m_xa0j25.js","assets/chevron-down-I8tOk39n.js","assets/lock-B_L7eRBi.js","assets/mail-4oZ8O593.js","assets/arrow-left-CPLDG87G.js","assets/EFHPage-DMM_tLtI.js","assets/MFHPage-8AEXcdx5.js","assets/MapPage-B69IH5bG.js","assets/checkbox-ClMhnSPl.js","assets/filter-DvioeHYD.js","assets/DailyViewPage-DvHskOIl.js","assets/VisitRecommendations-4l1HIKdN.js","assets/download-BHlV_KY3.js","assets/StatisticsPage-8qINX5Rj.js","assets/target-Cbp8QShB.js","assets/BarChart-DJI4ZcuR.js","assets/TeamsStatisticsPage-Cd6sXFHc.js","assets/BeraterStatisticsPage-Das5Pbh1.js","assets/chart-column-TdCSSvzn.js","assets/ProfilePage-DyzMFiGF.js","assets/user-check-BIUVRotN.js","assets/select-IVIdgARa.js","assets/MentorDashboard-CXkcQl70.js","assets/table-Bp6KGmPn.js","assets/TeamleiterDashboard-B_jNRP8o.js","assets/client-DbI4l5kI.js","assets/GebietsmanagerDashboard-DqTFTS2M.js","assets/functions-DcGEt8N_.js","assets/upload-CWJxBjPA.js","assets/database-DPAjLTxQ.js","assets/rotate-ccw-YT9XFzuk.js","assets/SettingsPage-B4Djqc5y.js","assets/CalendarPage-SQS4x_Yq.js","assets/UserManagementPage-l3IFNP6h.js","assets/trash-2-BcpLsksG.js","assets/plus-C74OdLeW.js","assets/search-BlRJyMtb.js","assets/TeamManagementPage-DuP4jJt0.js","assets/textarea-BM1-JMTm.js","assets/AreaManagementPage-DSL8ruvD.js","assets/AdminDashboardPage-Bmw0DA-1.js","assets/progress-DeyBaPEi.js","assets/circle-x-IVLZ_bbf.js","assets/AdminTestPage-PYJHbxwC.js","assets/TeamOverviewPage-BrHek2KD.js","assets/TeamsOverviewPage-CcQZrY1e.js","assets/AreaOverviewPage-cmvYm83d.js","assets/VisitStatusPage-DUhk-biP.js","assets/ProductSelectionPage-CWUbhSM6.js","assets/PatternAnalysisDemoPage-BdB8Yspw.js","assets/MFHManagerPage-BF13aXK8.js","assets/ButtonTest-DM7XAVUz.js","assets/TouchTargetValidatorPage-Ahn2en4Z.js"])))=>i.map(i=>d[i]);
var ch=Object.defineProperty;var ru=e=>{throw TypeError(e)};var dh=(e,t,n)=>t in e?ch(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ps=(e,t,n)=>dh(e,typeof t!="symbol"?t+"":t,n),hs=(e,t,n)=>t.has(e)||ru("Cannot "+n);var C=(e,t,n)=>(hs(e,t,"read from private field"),n?n.call(e):t.get(e)),ee=(e,t,n)=>t.has(e)?ru("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),G=(e,t,n,r)=>(hs(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Oe=(e,t,n)=>(hs(e,t,"access private method"),n);var Uo=(e,t,n,r)=>({set _(o){G(e,t,o,n)},get _(){return C(e,t,r)}});function fh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var q1=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function qc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function X1(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}),n}var Xc={exports:{}},Qi={},Jc={exports:{}},X={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bo=Symbol.for("react.element"),ph=Symbol.for("react.portal"),hh=Symbol.for("react.fragment"),mh=Symbol.for("react.strict_mode"),gh=Symbol.for("react.profiler"),vh=Symbol.for("react.provider"),yh=Symbol.for("react.context"),wh=Symbol.for("react.forward_ref"),xh=Symbol.for("react.suspense"),Sh=Symbol.for("react.memo"),Eh=Symbol.for("react.lazy"),ou=Symbol.iterator;function kh(e){return e===null||typeof e!="object"?null:(e=ou&&e[ou]||e["@@iterator"],typeof e=="function"?e:null)}var Zc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ed=Object.assign,td={};function Ir(e,t,n){this.props=e,this.context=t,this.refs=td,this.updater=n||Zc}Ir.prototype.isReactComponent={};Ir.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ir.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function nd(){}nd.prototype=Ir.prototype;function Gl(e,t,n){this.props=e,this.context=t,this.refs=td,this.updater=n||Zc}var Kl=Gl.prototype=new nd;Kl.constructor=Gl;ed(Kl,Ir.prototype);Kl.isPureReactComponent=!0;var iu=Array.isArray,rd=Object.prototype.hasOwnProperty,Yl={current:null},od={key:!0,ref:!0,__self:!0,__source:!0};function id(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)rd.call(t,r)&&!od.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:bo,type:e,key:i,ref:s,props:o,_owner:Yl.current}}function Ch(e,t){return{$$typeof:bo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ql(e){return typeof e=="object"&&e!==null&&e.$$typeof===bo}function Ph(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var su=/\/+/g;function ms(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ph(""+e.key):t.toString(36)}function li(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case bo:case ph:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+ms(s,0):r,iu(o)?(n="",e!=null&&(n=e.replace(su,"$&/")+"/"),li(o,t,n,"",function(u){return u})):o!=null&&(ql(o)&&(o=Ch(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(su,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",iu(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+ms(i,l);s+=li(i,t,n,a,o)}else if(a=kh(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+ms(i,l++),s+=li(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Vo(e,t,n){if(e==null)return e;var r=[],o=0;return li(e,r,"","",function(i){return t.call(n,i,o++)}),r}function _h(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var He={current:null},ai={transition:null},Nh={ReactCurrentDispatcher:He,ReactCurrentBatchConfig:ai,ReactCurrentOwner:Yl};function sd(){throw Error("act(...) is not supported in production builds of React.")}X.Children={map:Vo,forEach:function(e,t,n){Vo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Vo(e,function(){t++}),t},toArray:function(e){return Vo(e,function(t){return t})||[]},only:function(e){if(!ql(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};X.Component=Ir;X.Fragment=hh;X.Profiler=gh;X.PureComponent=Gl;X.StrictMode=mh;X.Suspense=xh;X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Nh;X.act=sd;X.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ed({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Yl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)rd.call(t,a)&&!od.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:bo,type:e.type,key:o,ref:i,props:r,_owner:s}};X.createContext=function(e){return e={$$typeof:yh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:vh,_context:e},e.Consumer=e};X.createElement=id;X.createFactory=function(e){var t=id.bind(null,e);return t.type=e,t};X.createRef=function(){return{current:null}};X.forwardRef=function(e){return{$$typeof:wh,render:e}};X.isValidElement=ql;X.lazy=function(e){return{$$typeof:Eh,_payload:{_status:-1,_result:e},_init:_h}};X.memo=function(e,t){return{$$typeof:Sh,type:e,compare:t===void 0?null:t}};X.startTransition=function(e){var t=ai.transition;ai.transition={};try{e()}finally{ai.transition=t}};X.unstable_act=sd;X.useCallback=function(e,t){return He.current.useCallback(e,t)};X.useContext=function(e){return He.current.useContext(e)};X.useDebugValue=function(){};X.useDeferredValue=function(e){return He.current.useDeferredValue(e)};X.useEffect=function(e,t){return He.current.useEffect(e,t)};X.useId=function(){return He.current.useId()};X.useImperativeHandle=function(e,t,n){return He.current.useImperativeHandle(e,t,n)};X.useInsertionEffect=function(e,t){return He.current.useInsertionEffect(e,t)};X.useLayoutEffect=function(e,t){return He.current.useLayoutEffect(e,t)};X.useMemo=function(e,t){return He.current.useMemo(e,t)};X.useReducer=function(e,t,n){return He.current.useReducer(e,t,n)};X.useRef=function(e){return He.current.useRef(e)};X.useState=function(e){return He.current.useState(e)};X.useSyncExternalStore=function(e,t,n){return He.current.useSyncExternalStore(e,t,n)};X.useTransition=function(){return He.current.useTransition()};X.version="18.3.1";Jc.exports=X;var v=Jc.exports;const j=qc(v),Rh=fh({__proto__:null,default:j},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Th=v,Oh=Symbol.for("react.element"),Ih=Symbol.for("react.fragment"),bh=Object.prototype.hasOwnProperty,Dh=Th.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,jh={key:!0,ref:!0,__self:!0,__source:!0};function ld(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)bh.call(t,r)&&!jh.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Oh,type:e,key:i,ref:s,props:o,_owner:Dh.current}}Qi.Fragment=Ih;Qi.jsx=ld;Qi.jsxs=ld;Xc.exports=Qi;var w=Xc.exports,ad={exports:{}},ot={},ud={exports:{}},cd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,M){var B=_.length;_.push(M);e:for(;0<B;){var F=B-1>>>1,$=_[F];if(0<o($,M))_[F]=M,_[B]=$,B=F;else break e}}function n(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var M=_[0],B=_.pop();if(B!==M){_[0]=B;e:for(var F=0,$=_.length,T=$>>>1;F<T;){var b=2*(F+1)-1,D=_[b],V=b+1,K=_[V];if(0>o(D,B))V<$&&0>o(K,D)?(_[F]=K,_[V]=B,F=V):(_[F]=D,_[b]=B,F=b);else if(V<$&&0>o(K,B))_[F]=K,_[V]=B,F=V;else break e}}return M}function o(_,M){var B=_.sortIndex-M.sortIndex;return B!==0?B:_.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],f=1,d=null,c=3,y=!1,S=!1,g=!1,k=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(_){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=_)r(u),M.sortIndex=M.expirationTime,t(a,M);else break;M=n(u)}}function x(_){if(g=!1,m(_),!S)if(n(a)!==null)S=!0,pe(P);else{var M=n(u);M!==null&&Le(x,M.startTime-_)}}function P(_,M){S=!1,g&&(g=!1,h(E),E=-1),y=!0;var B=c;try{for(m(M),d=n(a);d!==null&&(!(d.expirationTime>M)||_&&!U());){var F=d.callback;if(typeof F=="function"){d.callback=null,c=d.priorityLevel;var $=F(d.expirationTime<=M);M=e.unstable_now(),typeof $=="function"?d.callback=$:d===n(a)&&r(a),m(M)}else r(a);d=n(a)}if(d!==null)var T=!0;else{var b=n(u);b!==null&&Le(x,b.startTime-M),T=!1}return T}finally{d=null,c=B,y=!1}}var O=!1,z=null,E=-1,I=5,R=-1;function U(){return!(e.unstable_now()-R<I)}function L(){if(z!==null){var _=e.unstable_now();R=_;var M=!0;try{M=z(!0,_)}finally{M?H():(O=!1,z=null)}}else O=!1}var H;if(typeof p=="function")H=function(){p(L)};else if(typeof MessageChannel<"u"){var ne=new MessageChannel,de=ne.port2;ne.port1.onmessage=L,H=function(){de.postMessage(null)}}else H=function(){k(L,0)};function pe(_){z=_,O||(O=!0,H())}function Le(_,M){E=k(function(){_(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){S||y||(S=!0,pe(P))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(_){switch(c){case 1:case 2:case 3:var M=3;break;default:M=c}var B=c;c=M;try{return _()}finally{c=B}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,M){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var B=c;c=_;try{return M()}finally{c=B}},e.unstable_scheduleCallback=function(_,M,B){var F=e.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?F+B:F):B=F,_){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=B+$,_={id:f++,callback:M,priorityLevel:_,startTime:B,expirationTime:$,sortIndex:-1},B>F?(_.sortIndex=B,t(u,_),n(a)===null&&_===n(u)&&(g?(h(E),E=-1):g=!0,Le(x,B-F))):(_.sortIndex=$,t(a,_),S||y||(S=!0,pe(P))),_},e.unstable_shouldYield=U,e.unstable_wrapCallback=function(_){var M=c;return function(){var B=c;c=M;try{return _.apply(this,arguments)}finally{c=B}}}})(cd);ud.exports=cd;var zh=ud.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh=v,rt=zh;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var dd=new Set,ao={};function Bn(e,t){Cr(e,t),Cr(e+"Capture",t)}function Cr(e,t){for(ao[e]=t,e=0;e<t.length;e++)dd.add(t[e])}var Vt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Qs=Object.prototype.hasOwnProperty,Mh=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,lu={},au={};function Ah(e){return Qs.call(au,e)?!0:Qs.call(lu,e)?!1:Mh.test(e)?au[e]=!0:(lu[e]=!0,!1)}function Fh(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Uh(e,t,n,r){if(t===null||typeof t>"u"||Fh(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function We(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Te={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Te[e]=new We(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Te[t]=new We(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Te[e]=new We(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Te[e]=new We(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Te[e]=new We(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Te[e]=new We(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Te[e]=new We(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Te[e]=new We(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Te[e]=new We(e,5,!1,e.toLowerCase(),null,!1,!1)});var Xl=/[\-:]([a-z])/g;function Jl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Xl,Jl);Te[t]=new We(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Xl,Jl);Te[t]=new We(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Xl,Jl);Te[t]=new We(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Te[e]=new We(e,1,!1,e.toLowerCase(),null,!1,!1)});Te.xlinkHref=new We("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Te[e]=new We(e,1,!1,e.toLowerCase(),null,!0,!0)});function Zl(e,t,n,r){var o=Te.hasOwnProperty(t)?Te[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Uh(t,n,o,r)&&(n=null),r||o===null?Ah(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Wt=Lh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Bo=Symbol.for("react.element"),qn=Symbol.for("react.portal"),Xn=Symbol.for("react.fragment"),ea=Symbol.for("react.strict_mode"),Gs=Symbol.for("react.profiler"),fd=Symbol.for("react.provider"),pd=Symbol.for("react.context"),ta=Symbol.for("react.forward_ref"),Ks=Symbol.for("react.suspense"),Ys=Symbol.for("react.suspense_list"),na=Symbol.for("react.memo"),Xt=Symbol.for("react.lazy"),hd=Symbol.for("react.offscreen"),uu=Symbol.iterator;function Fr(e){return e===null||typeof e!="object"?null:(e=uu&&e[uu]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Object.assign,gs;function Yr(e){if(gs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);gs=t&&t[1]||""}return`
`+gs+e}var vs=!1;function ys(e,t){if(!e||vs)return"";vs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{vs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Yr(e):""}function Vh(e){switch(e.tag){case 5:return Yr(e.type);case 16:return Yr("Lazy");case 13:return Yr("Suspense");case 19:return Yr("SuspenseList");case 0:case 2:case 15:return e=ys(e.type,!1),e;case 11:return e=ys(e.type.render,!1),e;case 1:return e=ys(e.type,!0),e;default:return""}}function qs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Xn:return"Fragment";case qn:return"Portal";case Gs:return"Profiler";case ea:return"StrictMode";case Ks:return"Suspense";case Ys:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pd:return(e.displayName||"Context")+".Consumer";case fd:return(e._context.displayName||"Context")+".Provider";case ta:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case na:return t=e.displayName||null,t!==null?t:qs(e.type)||"Memo";case Xt:t=e._payload,e=e._init;try{return qs(e(t))}catch{}}return null}function Bh(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return qs(t);case 8:return t===ea?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function wn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function md(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function $h(e){var t=md(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function $o(e){e._valueTracker||(e._valueTracker=$h(e))}function gd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=md(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function wi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Xs(e,t){var n=t.checked;return ge({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function cu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=wn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function vd(e,t){t=t.checked,t!=null&&Zl(e,"checked",t,!1)}function Js(e,t){vd(e,t);var n=wn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Zs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Zs(e,t.type,wn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function du(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Zs(e,t,n){(t!=="number"||wi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var qr=Array.isArray;function ar(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+wn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function el(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return ge({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(qr(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:wn(n)}}function yd(e,t){var n=wn(t.value),r=wn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function pu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function wd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function tl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?wd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ho,xd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ho=Ho||document.createElement("div"),Ho.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ho.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function uo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Hh=["Webkit","ms","Moz","O"];Object.keys(Zr).forEach(function(e){Hh.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zr[t]=Zr[e]})});function Sd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Zr.hasOwnProperty(e)&&Zr[e]?(""+t).trim():t+"px"}function Ed(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Sd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Wh=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function nl(e,t){if(t){if(Wh[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function rl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ol=null;function ra(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var il=null,ur=null,cr=null;function hu(e){if(e=zo(e)){if(typeof il!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Xi(t),il(e.stateNode,e.type,t))}}function kd(e){ur?cr?cr.push(e):cr=[e]:ur=e}function Cd(){if(ur){var e=ur,t=cr;if(cr=ur=null,hu(e),t)for(e=0;e<t.length;e++)hu(t[e])}}function Pd(e,t){return e(t)}function _d(){}var ws=!1;function Nd(e,t,n){if(ws)return e(t,n);ws=!0;try{return Pd(e,t,n)}finally{ws=!1,(ur!==null||cr!==null)&&(_d(),Cd())}}function co(e,t){var n=e.stateNode;if(n===null)return null;var r=Xi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var sl=!1;if(Vt)try{var Ur={};Object.defineProperty(Ur,"passive",{get:function(){sl=!0}}),window.addEventListener("test",Ur,Ur),window.removeEventListener("test",Ur,Ur)}catch{sl=!1}function Qh(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var eo=!1,xi=null,Si=!1,ll=null,Gh={onError:function(e){eo=!0,xi=e}};function Kh(e,t,n,r,o,i,s,l,a){eo=!1,xi=null,Qh.apply(Gh,arguments)}function Yh(e,t,n,r,o,i,s,l,a){if(Kh.apply(this,arguments),eo){if(eo){var u=xi;eo=!1,xi=null}else throw Error(N(198));Si||(Si=!0,ll=u)}}function $n(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Rd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function mu(e){if($n(e)!==e)throw Error(N(188))}function qh(e){var t=e.alternate;if(!t){if(t=$n(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return mu(o),e;if(i===r)return mu(o),t;i=i.sibling}throw Error(N(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function Td(e){return e=qh(e),e!==null?Od(e):null}function Od(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Od(e);if(t!==null)return t;e=e.sibling}return null}var Id=rt.unstable_scheduleCallback,gu=rt.unstable_cancelCallback,Xh=rt.unstable_shouldYield,Jh=rt.unstable_requestPaint,we=rt.unstable_now,Zh=rt.unstable_getCurrentPriorityLevel,oa=rt.unstable_ImmediatePriority,bd=rt.unstable_UserBlockingPriority,Ei=rt.unstable_NormalPriority,em=rt.unstable_LowPriority,Dd=rt.unstable_IdlePriority,Gi=null,It=null;function tm(e){if(It&&typeof It.onCommitFiberRoot=="function")try{It.onCommitFiberRoot(Gi,e,void 0,(e.current.flags&128)===128)}catch{}}var xt=Math.clz32?Math.clz32:om,nm=Math.log,rm=Math.LN2;function om(e){return e>>>=0,e===0?32:31-(nm(e)/rm|0)|0}var Wo=64,Qo=4194304;function Xr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ki(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=Xr(l):(i&=s,i!==0&&(r=Xr(i)))}else s=n&~o,s!==0?r=Xr(s):i!==0&&(r=Xr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-xt(t),o=1<<n,r|=e[n],t&=~o;return r}function im(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function sm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-xt(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=im(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function al(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function jd(){var e=Wo;return Wo<<=1,!(Wo&4194240)&&(Wo=64),e}function xs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Do(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-xt(t),e[t]=n}function lm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-xt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ia(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-xt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var te=0;function zd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ld,sa,Md,Ad,Fd,ul=!1,Go=[],cn=null,dn=null,fn=null,fo=new Map,po=new Map,Zt=[],am="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function vu(e,t){switch(e){case"focusin":case"focusout":cn=null;break;case"dragenter":case"dragleave":dn=null;break;case"mouseover":case"mouseout":fn=null;break;case"pointerover":case"pointerout":fo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":po.delete(t.pointerId)}}function Vr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=zo(t),t!==null&&sa(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function um(e,t,n,r,o){switch(t){case"focusin":return cn=Vr(cn,e,t,n,r,o),!0;case"dragenter":return dn=Vr(dn,e,t,n,r,o),!0;case"mouseover":return fn=Vr(fn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return fo.set(i,Vr(fo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,po.set(i,Vr(po.get(i)||null,e,t,n,r,o)),!0}return!1}function Ud(e){var t=Rn(e.target);if(t!==null){var n=$n(t);if(n!==null){if(t=n.tag,t===13){if(t=Rd(n),t!==null){e.blockedOn=t,Fd(e.priority,function(){Md(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ui(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=cl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ol=r,n.target.dispatchEvent(r),ol=null}else return t=zo(n),t!==null&&sa(t),e.blockedOn=n,!1;t.shift()}return!0}function yu(e,t,n){ui(e)&&n.delete(t)}function cm(){ul=!1,cn!==null&&ui(cn)&&(cn=null),dn!==null&&ui(dn)&&(dn=null),fn!==null&&ui(fn)&&(fn=null),fo.forEach(yu),po.forEach(yu)}function Br(e,t){e.blockedOn===t&&(e.blockedOn=null,ul||(ul=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,cm)))}function ho(e){function t(o){return Br(o,e)}if(0<Go.length){Br(Go[0],e);for(var n=1;n<Go.length;n++){var r=Go[n];r.blockedOn===e&&(r.blockedOn=null)}}for(cn!==null&&Br(cn,e),dn!==null&&Br(dn,e),fn!==null&&Br(fn,e),fo.forEach(t),po.forEach(t),n=0;n<Zt.length;n++)r=Zt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Zt.length&&(n=Zt[0],n.blockedOn===null);)Ud(n),n.blockedOn===null&&Zt.shift()}var dr=Wt.ReactCurrentBatchConfig,Ci=!0;function dm(e,t,n,r){var o=te,i=dr.transition;dr.transition=null;try{te=1,la(e,t,n,r)}finally{te=o,dr.transition=i}}function fm(e,t,n,r){var o=te,i=dr.transition;dr.transition=null;try{te=4,la(e,t,n,r)}finally{te=o,dr.transition=i}}function la(e,t,n,r){if(Ci){var o=cl(e,t,n,r);if(o===null)Os(e,t,r,Pi,n),vu(e,r);else if(um(o,e,t,n,r))r.stopPropagation();else if(vu(e,r),t&4&&-1<am.indexOf(e)){for(;o!==null;){var i=zo(o);if(i!==null&&Ld(i),i=cl(e,t,n,r),i===null&&Os(e,t,r,Pi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Os(e,t,r,null,n)}}var Pi=null;function cl(e,t,n,r){if(Pi=null,e=ra(r),e=Rn(e),e!==null)if(t=$n(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Rd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Pi=e,null}function Vd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zh()){case oa:return 1;case bd:return 4;case Ei:case em:return 16;case Dd:return 536870912;default:return 16}default:return 16}}var ln=null,aa=null,ci=null;function Bd(){if(ci)return ci;var e,t=aa,n=t.length,r,o="value"in ln?ln.value:ln.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ci=o.slice(e,1<r?1-r:void 0)}function di(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ko(){return!0}function wu(){return!1}function it(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ko:wu,this.isPropagationStopped=wu,this}return ge(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ko)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ko)},persist:function(){},isPersistent:Ko}),t}var br={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ua=it(br),jo=ge({},br,{view:0,detail:0}),pm=it(jo),Ss,Es,$r,Ki=ge({},jo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ca,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$r&&($r&&e.type==="mousemove"?(Ss=e.screenX-$r.screenX,Es=e.screenY-$r.screenY):Es=Ss=0,$r=e),Ss)},movementY:function(e){return"movementY"in e?e.movementY:Es}}),xu=it(Ki),hm=ge({},Ki,{dataTransfer:0}),mm=it(hm),gm=ge({},jo,{relatedTarget:0}),ks=it(gm),vm=ge({},br,{animationName:0,elapsedTime:0,pseudoElement:0}),ym=it(vm),wm=ge({},br,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xm=it(wm),Sm=ge({},br,{data:0}),Su=it(Sm),Em={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},km={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Cm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Pm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Cm[e])?!!t[e]:!1}function ca(){return Pm}var _m=ge({},jo,{key:function(e){if(e.key){var t=Em[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=di(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?km[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ca,charCode:function(e){return e.type==="keypress"?di(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?di(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nm=it(_m),Rm=ge({},Ki,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Eu=it(Rm),Tm=ge({},jo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ca}),Om=it(Tm),Im=ge({},br,{propertyName:0,elapsedTime:0,pseudoElement:0}),bm=it(Im),Dm=ge({},Ki,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jm=it(Dm),zm=[9,13,27,32],da=Vt&&"CompositionEvent"in window,to=null;Vt&&"documentMode"in document&&(to=document.documentMode);var Lm=Vt&&"TextEvent"in window&&!to,$d=Vt&&(!da||to&&8<to&&11>=to),ku=" ",Cu=!1;function Hd(e,t){switch(e){case"keyup":return zm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Jn=!1;function Mm(e,t){switch(e){case"compositionend":return Wd(t);case"keypress":return t.which!==32?null:(Cu=!0,ku);case"textInput":return e=t.data,e===ku&&Cu?null:e;default:return null}}function Am(e,t){if(Jn)return e==="compositionend"||!da&&Hd(e,t)?(e=Bd(),ci=aa=ln=null,Jn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return $d&&t.locale!=="ko"?null:t.data;default:return null}}var Fm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Pu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Fm[e.type]:t==="textarea"}function Qd(e,t,n,r){kd(r),t=_i(t,"onChange"),0<t.length&&(n=new ua("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var no=null,mo=null;function Um(e){rf(e,0)}function Yi(e){var t=tr(e);if(gd(t))return e}function Vm(e,t){if(e==="change")return t}var Gd=!1;if(Vt){var Cs;if(Vt){var Ps="oninput"in document;if(!Ps){var _u=document.createElement("div");_u.setAttribute("oninput","return;"),Ps=typeof _u.oninput=="function"}Cs=Ps}else Cs=!1;Gd=Cs&&(!document.documentMode||9<document.documentMode)}function Nu(){no&&(no.detachEvent("onpropertychange",Kd),mo=no=null)}function Kd(e){if(e.propertyName==="value"&&Yi(mo)){var t=[];Qd(t,mo,e,ra(e)),Nd(Um,t)}}function Bm(e,t,n){e==="focusin"?(Nu(),no=t,mo=n,no.attachEvent("onpropertychange",Kd)):e==="focusout"&&Nu()}function $m(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Yi(mo)}function Hm(e,t){if(e==="click")return Yi(t)}function Wm(e,t){if(e==="input"||e==="change")return Yi(t)}function Qm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Qm;function go(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Qs.call(t,o)||!Et(e[o],t[o]))return!1}return!0}function Ru(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Tu(e,t){var n=Ru(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ru(n)}}function Yd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Yd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function qd(){for(var e=window,t=wi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=wi(e.document)}return t}function fa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gm(e){var t=qd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Yd(n.ownerDocument.documentElement,n)){if(r!==null&&fa(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Tu(n,i);var s=Tu(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Km=Vt&&"documentMode"in document&&11>=document.documentMode,Zn=null,dl=null,ro=null,fl=!1;function Ou(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;fl||Zn==null||Zn!==wi(r)||(r=Zn,"selectionStart"in r&&fa(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ro&&go(ro,r)||(ro=r,r=_i(dl,"onSelect"),0<r.length&&(t=new ua("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Zn)))}function Yo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var er={animationend:Yo("Animation","AnimationEnd"),animationiteration:Yo("Animation","AnimationIteration"),animationstart:Yo("Animation","AnimationStart"),transitionend:Yo("Transition","TransitionEnd")},_s={},Xd={};Vt&&(Xd=document.createElement("div").style,"AnimationEvent"in window||(delete er.animationend.animation,delete er.animationiteration.animation,delete er.animationstart.animation),"TransitionEvent"in window||delete er.transitionend.transition);function qi(e){if(_s[e])return _s[e];if(!er[e])return e;var t=er[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Xd)return _s[e]=t[n];return e}var Jd=qi("animationend"),Zd=qi("animationiteration"),ef=qi("animationstart"),tf=qi("transitionend"),nf=new Map,Iu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sn(e,t){nf.set(e,t),Bn(t,[e])}for(var Ns=0;Ns<Iu.length;Ns++){var Rs=Iu[Ns],Ym=Rs.toLowerCase(),qm=Rs[0].toUpperCase()+Rs.slice(1);Sn(Ym,"on"+qm)}Sn(Jd,"onAnimationEnd");Sn(Zd,"onAnimationIteration");Sn(ef,"onAnimationStart");Sn("dblclick","onDoubleClick");Sn("focusin","onFocus");Sn("focusout","onBlur");Sn(tf,"onTransitionEnd");Cr("onMouseEnter",["mouseout","mouseover"]);Cr("onMouseLeave",["mouseout","mouseover"]);Cr("onPointerEnter",["pointerout","pointerover"]);Cr("onPointerLeave",["pointerout","pointerover"]);Bn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Bn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Bn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Bn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Bn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Bn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xm=new Set("cancel close invalid load scroll toggle".split(" ").concat(Jr));function bu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Yh(r,t,void 0,e),e.currentTarget=null}function rf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;bu(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;bu(o,l,u),i=a}}}if(Si)throw e=ll,Si=!1,ll=null,e}function ue(e,t){var n=t[vl];n===void 0&&(n=t[vl]=new Set);var r=e+"__bubble";n.has(r)||(of(t,e,2,!1),n.add(r))}function Ts(e,t,n){var r=0;t&&(r|=4),of(n,e,r,t)}var qo="_reactListening"+Math.random().toString(36).slice(2);function vo(e){if(!e[qo]){e[qo]=!0,dd.forEach(function(n){n!=="selectionchange"&&(Xm.has(n)||Ts(n,!1,e),Ts(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[qo]||(t[qo]=!0,Ts("selectionchange",!1,t))}}function of(e,t,n,r){switch(Vd(t)){case 1:var o=dm;break;case 4:o=fm;break;default:o=la}n=o.bind(null,t,n,e),o=void 0,!sl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Os(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Rn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Nd(function(){var u=i,f=ra(n),d=[];e:{var c=nf.get(e);if(c!==void 0){var y=ua,S=e;switch(e){case"keypress":if(di(n)===0)break e;case"keydown":case"keyup":y=Nm;break;case"focusin":S="focus",y=ks;break;case"focusout":S="blur",y=ks;break;case"beforeblur":case"afterblur":y=ks;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=xu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=mm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Om;break;case Jd:case Zd:case ef:y=ym;break;case tf:y=bm;break;case"scroll":y=pm;break;case"wheel":y=jm;break;case"copy":case"cut":case"paste":y=xm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Eu}var g=(t&4)!==0,k=!g&&e==="scroll",h=g?c!==null?c+"Capture":null:c;g=[];for(var p=u,m;p!==null;){m=p;var x=m.stateNode;if(m.tag===5&&x!==null&&(m=x,h!==null&&(x=co(p,h),x!=null&&g.push(yo(p,x,m)))),k)break;p=p.return}0<g.length&&(c=new y(c,S,null,n,f),d.push({event:c,listeners:g}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",c&&n!==ol&&(S=n.relatedTarget||n.fromElement)&&(Rn(S)||S[Bt]))break e;if((y||c)&&(c=f.window===f?f:(c=f.ownerDocument)?c.defaultView||c.parentWindow:window,y?(S=n.relatedTarget||n.toElement,y=u,S=S?Rn(S):null,S!==null&&(k=$n(S),S!==k||S.tag!==5&&S.tag!==6)&&(S=null)):(y=null,S=u),y!==S)){if(g=xu,x="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(g=Eu,x="onPointerLeave",h="onPointerEnter",p="pointer"),k=y==null?c:tr(y),m=S==null?c:tr(S),c=new g(x,p+"leave",y,n,f),c.target=k,c.relatedTarget=m,x=null,Rn(f)===u&&(g=new g(h,p+"enter",S,n,f),g.target=m,g.relatedTarget=k,x=g),k=x,y&&S)t:{for(g=y,h=S,p=0,m=g;m;m=Yn(m))p++;for(m=0,x=h;x;x=Yn(x))m++;for(;0<p-m;)g=Yn(g),p--;for(;0<m-p;)h=Yn(h),m--;for(;p--;){if(g===h||h!==null&&g===h.alternate)break t;g=Yn(g),h=Yn(h)}g=null}else g=null;y!==null&&Du(d,c,y,g,!1),S!==null&&k!==null&&Du(d,k,S,g,!0)}}e:{if(c=u?tr(u):window,y=c.nodeName&&c.nodeName.toLowerCase(),y==="select"||y==="input"&&c.type==="file")var P=Vm;else if(Pu(c))if(Gd)P=Wm;else{P=$m;var O=Bm}else(y=c.nodeName)&&y.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(P=Hm);if(P&&(P=P(e,u))){Qd(d,P,n,f);break e}O&&O(e,c,u),e==="focusout"&&(O=c._wrapperState)&&O.controlled&&c.type==="number"&&Zs(c,"number",c.value)}switch(O=u?tr(u):window,e){case"focusin":(Pu(O)||O.contentEditable==="true")&&(Zn=O,dl=u,ro=null);break;case"focusout":ro=dl=Zn=null;break;case"mousedown":fl=!0;break;case"contextmenu":case"mouseup":case"dragend":fl=!1,Ou(d,n,f);break;case"selectionchange":if(Km)break;case"keydown":case"keyup":Ou(d,n,f)}var z;if(da)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else Jn?Hd(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&($d&&n.locale!=="ko"&&(Jn||E!=="onCompositionStart"?E==="onCompositionEnd"&&Jn&&(z=Bd()):(ln=f,aa="value"in ln?ln.value:ln.textContent,Jn=!0)),O=_i(u,E),0<O.length&&(E=new Su(E,e,null,n,f),d.push({event:E,listeners:O}),z?E.data=z:(z=Wd(n),z!==null&&(E.data=z)))),(z=Lm?Mm(e,n):Am(e,n))&&(u=_i(u,"onBeforeInput"),0<u.length&&(f=new Su("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=z))}rf(d,t)})}function yo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function _i(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=co(e,n),i!=null&&r.unshift(yo(e,i,o)),i=co(e,t),i!=null&&r.push(yo(e,i,o))),e=e.return}return r}function Yn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Du(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=co(n,i),a!=null&&s.unshift(yo(n,a,l))):o||(a=co(n,i),a!=null&&s.push(yo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Jm=/\r\n?/g,Zm=/\u0000|\uFFFD/g;function ju(e){return(typeof e=="string"?e:""+e).replace(Jm,`
`).replace(Zm,"")}function Xo(e,t,n){if(t=ju(t),ju(e)!==t&&n)throw Error(N(425))}function Ni(){}var pl=null,hl=null;function ml(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var gl=typeof setTimeout=="function"?setTimeout:void 0,eg=typeof clearTimeout=="function"?clearTimeout:void 0,zu=typeof Promise=="function"?Promise:void 0,tg=typeof queueMicrotask=="function"?queueMicrotask:typeof zu<"u"?function(e){return zu.resolve(null).then(e).catch(ng)}:gl;function ng(e){setTimeout(function(){throw e})}function Is(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ho(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ho(t)}function pn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Lu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Dr=Math.random().toString(36).slice(2),Ot="__reactFiber$"+Dr,wo="__reactProps$"+Dr,Bt="__reactContainer$"+Dr,vl="__reactEvents$"+Dr,rg="__reactListeners$"+Dr,og="__reactHandles$"+Dr;function Rn(e){var t=e[Ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Bt]||n[Ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Lu(e);e!==null;){if(n=e[Ot])return n;e=Lu(e)}return t}e=n,n=e.parentNode}return null}function zo(e){return e=e[Ot]||e[Bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function tr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Xi(e){return e[wo]||null}var yl=[],nr=-1;function En(e){return{current:e}}function ce(e){0>nr||(e.current=yl[nr],yl[nr]=null,nr--)}function se(e,t){nr++,yl[nr]=e.current,e.current=t}var xn={},ze=En(xn),Ye=En(!1),Mn=xn;function Pr(e,t){var n=e.type.contextTypes;if(!n)return xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function qe(e){return e=e.childContextTypes,e!=null}function Ri(){ce(Ye),ce(ze)}function Mu(e,t,n){if(ze.current!==xn)throw Error(N(168));se(ze,t),se(Ye,n)}function sf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(N(108,Bh(e)||"Unknown",o));return ge({},n,r)}function Ti(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||xn,Mn=ze.current,se(ze,e),se(Ye,Ye.current),!0}function Au(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=sf(e,t,Mn),r.__reactInternalMemoizedMergedChildContext=e,ce(Ye),ce(ze),se(ze,e)):ce(Ye),se(Ye,n)}var Mt=null,Ji=!1,bs=!1;function lf(e){Mt===null?Mt=[e]:Mt.push(e)}function ig(e){Ji=!0,lf(e)}function kn(){if(!bs&&Mt!==null){bs=!0;var e=0,t=te;try{var n=Mt;for(te=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Mt=null,Ji=!1}catch(o){throw Mt!==null&&(Mt=Mt.slice(e+1)),Id(oa,kn),o}finally{te=t,bs=!1}}return null}var rr=[],or=0,Oi=null,Ii=0,at=[],ut=0,An=null,At=1,Ft="";function Pn(e,t){rr[or++]=Ii,rr[or++]=Oi,Oi=e,Ii=t}function af(e,t,n){at[ut++]=At,at[ut++]=Ft,at[ut++]=An,An=e;var r=At;e=Ft;var o=32-xt(r)-1;r&=~(1<<o),n+=1;var i=32-xt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,At=1<<32-xt(t)+o|n<<o|r,Ft=i+e}else At=1<<i|n<<o|r,Ft=e}function pa(e){e.return!==null&&(Pn(e,1),af(e,1,0))}function ha(e){for(;e===Oi;)Oi=rr[--or],rr[or]=null,Ii=rr[--or],rr[or]=null;for(;e===An;)An=at[--ut],at[ut]=null,Ft=at[--ut],at[ut]=null,At=at[--ut],at[ut]=null}var nt=null,tt=null,fe=!1,wt=null;function uf(e,t){var n=ct(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Fu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,tt=pn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,tt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=An!==null?{id:At,overflow:Ft}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ct(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,nt=e,tt=null,!0):!1;default:return!1}}function wl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function xl(e){if(fe){var t=tt;if(t){var n=t;if(!Fu(e,t)){if(wl(e))throw Error(N(418));t=pn(n.nextSibling);var r=nt;t&&Fu(e,t)?uf(r,n):(e.flags=e.flags&-4097|2,fe=!1,nt=e)}}else{if(wl(e))throw Error(N(418));e.flags=e.flags&-4097|2,fe=!1,nt=e}}}function Uu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function Jo(e){if(e!==nt)return!1;if(!fe)return Uu(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ml(e.type,e.memoizedProps)),t&&(t=tt)){if(wl(e))throw cf(),Error(N(418));for(;t;)uf(e,t),t=pn(t.nextSibling)}if(Uu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){tt=pn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}tt=null}}else tt=nt?pn(e.stateNode.nextSibling):null;return!0}function cf(){for(var e=tt;e;)e=pn(e.nextSibling)}function _r(){tt=nt=null,fe=!1}function ma(e){wt===null?wt=[e]:wt.push(e)}var sg=Wt.ReactCurrentBatchConfig;function Hr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function Zo(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Vu(e){var t=e._init;return t(e._payload)}function df(e){function t(h,p){if(e){var m=h.deletions;m===null?(h.deletions=[p],h.flags|=16):m.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=vn(h,p),h.index=0,h.sibling=null,h}function i(h,p,m){return h.index=m,e?(m=h.alternate,m!==null?(m=m.index,m<p?(h.flags|=2,p):m):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,m,x){return p===null||p.tag!==6?(p=Fs(m,h.mode,x),p.return=h,p):(p=o(p,m),p.return=h,p)}function a(h,p,m,x){var P=m.type;return P===Xn?f(h,p,m.props.children,x,m.key):p!==null&&(p.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Xt&&Vu(P)===p.type)?(x=o(p,m.props),x.ref=Hr(h,p,m),x.return=h,x):(x=yi(m.type,m.key,m.props,null,h.mode,x),x.ref=Hr(h,p,m),x.return=h,x)}function u(h,p,m,x){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=Us(m,h.mode,x),p.return=h,p):(p=o(p,m.children||[]),p.return=h,p)}function f(h,p,m,x,P){return p===null||p.tag!==7?(p=Ln(m,h.mode,x,P),p.return=h,p):(p=o(p,m),p.return=h,p)}function d(h,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Fs(""+p,h.mode,m),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Bo:return m=yi(p.type,p.key,p.props,null,h.mode,m),m.ref=Hr(h,null,p),m.return=h,m;case qn:return p=Us(p,h.mode,m),p.return=h,p;case Xt:var x=p._init;return d(h,x(p._payload),m)}if(qr(p)||Fr(p))return p=Ln(p,h.mode,m,null),p.return=h,p;Zo(h,p)}return null}function c(h,p,m,x){var P=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return P!==null?null:l(h,p,""+m,x);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Bo:return m.key===P?a(h,p,m,x):null;case qn:return m.key===P?u(h,p,m,x):null;case Xt:return P=m._init,c(h,p,P(m._payload),x)}if(qr(m)||Fr(m))return P!==null?null:f(h,p,m,x,null);Zo(h,m)}return null}function y(h,p,m,x,P){if(typeof x=="string"&&x!==""||typeof x=="number")return h=h.get(m)||null,l(p,h,""+x,P);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Bo:return h=h.get(x.key===null?m:x.key)||null,a(p,h,x,P);case qn:return h=h.get(x.key===null?m:x.key)||null,u(p,h,x,P);case Xt:var O=x._init;return y(h,p,m,O(x._payload),P)}if(qr(x)||Fr(x))return h=h.get(m)||null,f(p,h,x,P,null);Zo(p,x)}return null}function S(h,p,m,x){for(var P=null,O=null,z=p,E=p=0,I=null;z!==null&&E<m.length;E++){z.index>E?(I=z,z=null):I=z.sibling;var R=c(h,z,m[E],x);if(R===null){z===null&&(z=I);break}e&&z&&R.alternate===null&&t(h,z),p=i(R,p,E),O===null?P=R:O.sibling=R,O=R,z=I}if(E===m.length)return n(h,z),fe&&Pn(h,E),P;if(z===null){for(;E<m.length;E++)z=d(h,m[E],x),z!==null&&(p=i(z,p,E),O===null?P=z:O.sibling=z,O=z);return fe&&Pn(h,E),P}for(z=r(h,z);E<m.length;E++)I=y(z,h,E,m[E],x),I!==null&&(e&&I.alternate!==null&&z.delete(I.key===null?E:I.key),p=i(I,p,E),O===null?P=I:O.sibling=I,O=I);return e&&z.forEach(function(U){return t(h,U)}),fe&&Pn(h,E),P}function g(h,p,m,x){var P=Fr(m);if(typeof P!="function")throw Error(N(150));if(m=P.call(m),m==null)throw Error(N(151));for(var O=P=null,z=p,E=p=0,I=null,R=m.next();z!==null&&!R.done;E++,R=m.next()){z.index>E?(I=z,z=null):I=z.sibling;var U=c(h,z,R.value,x);if(U===null){z===null&&(z=I);break}e&&z&&U.alternate===null&&t(h,z),p=i(U,p,E),O===null?P=U:O.sibling=U,O=U,z=I}if(R.done)return n(h,z),fe&&Pn(h,E),P;if(z===null){for(;!R.done;E++,R=m.next())R=d(h,R.value,x),R!==null&&(p=i(R,p,E),O===null?P=R:O.sibling=R,O=R);return fe&&Pn(h,E),P}for(z=r(h,z);!R.done;E++,R=m.next())R=y(z,h,E,R.value,x),R!==null&&(e&&R.alternate!==null&&z.delete(R.key===null?E:R.key),p=i(R,p,E),O===null?P=R:O.sibling=R,O=R);return e&&z.forEach(function(L){return t(h,L)}),fe&&Pn(h,E),P}function k(h,p,m,x){if(typeof m=="object"&&m!==null&&m.type===Xn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Bo:e:{for(var P=m.key,O=p;O!==null;){if(O.key===P){if(P=m.type,P===Xn){if(O.tag===7){n(h,O.sibling),p=o(O,m.props.children),p.return=h,h=p;break e}}else if(O.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Xt&&Vu(P)===O.type){n(h,O.sibling),p=o(O,m.props),p.ref=Hr(h,O,m),p.return=h,h=p;break e}n(h,O);break}else t(h,O);O=O.sibling}m.type===Xn?(p=Ln(m.props.children,h.mode,x,m.key),p.return=h,h=p):(x=yi(m.type,m.key,m.props,null,h.mode,x),x.ref=Hr(h,p,m),x.return=h,h=x)}return s(h);case qn:e:{for(O=m.key;p!==null;){if(p.key===O)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(h,p.sibling),p=o(p,m.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=Us(m,h.mode,x),p.return=h,h=p}return s(h);case Xt:return O=m._init,k(h,p,O(m._payload),x)}if(qr(m))return S(h,p,m,x);if(Fr(m))return g(h,p,m,x);Zo(h,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,m),p.return=h,h=p):(n(h,p),p=Fs(m,h.mode,x),p.return=h,h=p),s(h)):n(h,p)}return k}var Nr=df(!0),ff=df(!1),bi=En(null),Di=null,ir=null,ga=null;function va(){ga=ir=Di=null}function ya(e){var t=bi.current;ce(bi),e._currentValue=t}function Sl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function fr(e,t){Di=e,ga=ir=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ke=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(ga!==e)if(e={context:e,memoizedValue:t,next:null},ir===null){if(Di===null)throw Error(N(308));ir=e,Di.dependencies={lanes:0,firstContext:e}}else ir=ir.next=e;return t}var Tn=null;function wa(e){Tn===null?Tn=[e]:Tn.push(e)}function pf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,wa(t)):(n.next=o.next,o.next=n),t.interleaved=n,$t(e,r)}function $t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Jt=!1;function xa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function hf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ut(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function hn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,J&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,$t(e,n)}return o=r.interleaved,o===null?(t.next=t,wa(r)):(t.next=o.next,o.next=t),r.interleaved=t,$t(e,n)}function fi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ia(e,n)}}function Bu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ji(e,t,n,r){var o=e.updateQueue;Jt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(i!==null){var d=o.baseState;s=0,f=u=a=null,l=i;do{var c=l.lane,y=l.eventTime;if((r&c)===c){f!==null&&(f=f.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var S=e,g=l;switch(c=t,y=n,g.tag){case 1:if(S=g.payload,typeof S=="function"){d=S.call(y,d,c);break e}d=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=g.payload,c=typeof S=="function"?S.call(y,d,c):S,c==null)break e;d=ge({},d,c);break e;case 2:Jt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,c=o.effects,c===null?o.effects=[l]:c.push(l))}else y={eventTime:y,lane:c,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=y,a=d):f=f.next=y,s|=c;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;c=l,l=c.next,c.next=null,o.lastBaseUpdate=c,o.shared.pending=null}}while(!0);if(f===null&&(a=d),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Un|=s,e.lanes=s,e.memoizedState=d}}function $u(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(N(191,o));o.call(r)}}}var Lo={},bt=En(Lo),xo=En(Lo),So=En(Lo);function On(e){if(e===Lo)throw Error(N(174));return e}function Sa(e,t){switch(se(So,t),se(xo,e),se(bt,Lo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:tl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=tl(t,e)}ce(bt),se(bt,t)}function Rr(){ce(bt),ce(xo),ce(So)}function mf(e){On(So.current);var t=On(bt.current),n=tl(t,e.type);t!==n&&(se(xo,e),se(bt,n))}function Ea(e){xo.current===e&&(ce(bt),ce(xo))}var he=En(0);function zi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ds=[];function ka(){for(var e=0;e<Ds.length;e++)Ds[e]._workInProgressVersionPrimary=null;Ds.length=0}var pi=Wt.ReactCurrentDispatcher,js=Wt.ReactCurrentBatchConfig,Fn=0,me=null,Ee=null,Ce=null,Li=!1,oo=!1,Eo=0,lg=0;function Ie(){throw Error(N(321))}function Ca(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Et(e[n],t[n]))return!1;return!0}function Pa(e,t,n,r,o,i){if(Fn=i,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,pi.current=e===null||e.memoizedState===null?dg:fg,e=n(r,o),oo){i=0;do{if(oo=!1,Eo=0,25<=i)throw Error(N(301));i+=1,Ce=Ee=null,t.updateQueue=null,pi.current=pg,e=n(r,o)}while(oo)}if(pi.current=Mi,t=Ee!==null&&Ee.next!==null,Fn=0,Ce=Ee=me=null,Li=!1,t)throw Error(N(300));return e}function _a(){var e=Eo!==0;return Eo=0,e}function _t(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?me.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function pt(){if(Ee===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=Ee.next;var t=Ce===null?me.memoizedState:Ce.next;if(t!==null)Ce=t,Ee=e;else{if(e===null)throw Error(N(310));Ee=e,e={memoizedState:Ee.memoizedState,baseState:Ee.baseState,baseQueue:Ee.baseQueue,queue:Ee.queue,next:null},Ce===null?me.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function ko(e,t){return typeof t=="function"?t(e):t}function zs(e){var t=pt(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=Ee,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var f=u.lane;if((Fn&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,me.lanes|=f,Un|=f}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,Et(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,me.lanes|=i,Un|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ls(e){var t=pt(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);Et(i,t.memoizedState)||(Ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function gf(){}function vf(e,t){var n=me,r=pt(),o=t(),i=!Et(r.memoizedState,o);if(i&&(r.memoizedState=o,Ke=!0),r=r.queue,Na(xf.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ce!==null&&Ce.memoizedState.tag&1){if(n.flags|=2048,Co(9,wf.bind(null,n,r,o,t),void 0,null),Pe===null)throw Error(N(349));Fn&30||yf(n,t,o)}return o}function yf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function wf(e,t,n,r){t.value=n,t.getSnapshot=r,Sf(t)&&Ef(e)}function xf(e,t,n){return n(function(){Sf(t)&&Ef(e)})}function Sf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Et(e,n)}catch{return!0}}function Ef(e){var t=$t(e,1);t!==null&&St(t,e,1,-1)}function Hu(e){var t=_t();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ko,lastRenderedState:e},t.queue=e,e=e.dispatch=cg.bind(null,me,e),[t.memoizedState,e]}function Co(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function kf(){return pt().memoizedState}function hi(e,t,n,r){var o=_t();me.flags|=e,o.memoizedState=Co(1|t,n,void 0,r===void 0?null:r)}function Zi(e,t,n,r){var o=pt();r=r===void 0?null:r;var i=void 0;if(Ee!==null){var s=Ee.memoizedState;if(i=s.destroy,r!==null&&Ca(r,s.deps)){o.memoizedState=Co(t,n,i,r);return}}me.flags|=e,o.memoizedState=Co(1|t,n,i,r)}function Wu(e,t){return hi(8390656,8,e,t)}function Na(e,t){return Zi(2048,8,e,t)}function Cf(e,t){return Zi(4,2,e,t)}function Pf(e,t){return Zi(4,4,e,t)}function _f(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Nf(e,t,n){return n=n!=null?n.concat([e]):null,Zi(4,4,_f.bind(null,t,e),n)}function Ra(){}function Rf(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ca(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Tf(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ca(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Of(e,t,n){return Fn&21?(Et(n,t)||(n=jd(),me.lanes|=n,Un|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=n)}function ag(e,t){var n=te;te=n!==0&&4>n?n:4,e(!0);var r=js.transition;js.transition={};try{e(!1),t()}finally{te=n,js.transition=r}}function If(){return pt().memoizedState}function ug(e,t,n){var r=gn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},bf(e))Df(t,n);else if(n=pf(e,t,n,r),n!==null){var o=$e();St(n,e,r,o),jf(n,t,r)}}function cg(e,t,n){var r=gn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(bf(e))Df(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,Et(l,s)){var a=t.interleaved;a===null?(o.next=o,wa(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=pf(e,t,o,r),n!==null&&(o=$e(),St(n,e,r,o),jf(n,t,r))}}function bf(e){var t=e.alternate;return e===me||t!==null&&t===me}function Df(e,t){oo=Li=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ia(e,n)}}var Mi={readContext:ft,useCallback:Ie,useContext:Ie,useEffect:Ie,useImperativeHandle:Ie,useInsertionEffect:Ie,useLayoutEffect:Ie,useMemo:Ie,useReducer:Ie,useRef:Ie,useState:Ie,useDebugValue:Ie,useDeferredValue:Ie,useTransition:Ie,useMutableSource:Ie,useSyncExternalStore:Ie,useId:Ie,unstable_isNewReconciler:!1},dg={readContext:ft,useCallback:function(e,t){return _t().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:Wu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,hi(4194308,4,_f.bind(null,t,e),n)},useLayoutEffect:function(e,t){return hi(4194308,4,e,t)},useInsertionEffect:function(e,t){return hi(4,2,e,t)},useMemo:function(e,t){var n=_t();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=_t();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ug.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=_t();return e={current:e},t.memoizedState=e},useState:Hu,useDebugValue:Ra,useDeferredValue:function(e){return _t().memoizedState=e},useTransition:function(){var e=Hu(!1),t=e[0];return e=ag.bind(null,e[1]),_t().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,o=_t();if(fe){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),Pe===null)throw Error(N(349));Fn&30||yf(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Wu(xf.bind(null,r,i,e),[e]),r.flags|=2048,Co(9,wf.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=_t(),t=Pe.identifierPrefix;if(fe){var n=Ft,r=At;n=(r&~(1<<32-xt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Eo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=lg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},fg={readContext:ft,useCallback:Rf,useContext:ft,useEffect:Na,useImperativeHandle:Nf,useInsertionEffect:Cf,useLayoutEffect:Pf,useMemo:Tf,useReducer:zs,useRef:kf,useState:function(){return zs(ko)},useDebugValue:Ra,useDeferredValue:function(e){var t=pt();return Of(t,Ee.memoizedState,e)},useTransition:function(){var e=zs(ko)[0],t=pt().memoizedState;return[e,t]},useMutableSource:gf,useSyncExternalStore:vf,useId:If,unstable_isNewReconciler:!1},pg={readContext:ft,useCallback:Rf,useContext:ft,useEffect:Na,useImperativeHandle:Nf,useInsertionEffect:Cf,useLayoutEffect:Pf,useMemo:Tf,useReducer:Ls,useRef:kf,useState:function(){return Ls(ko)},useDebugValue:Ra,useDeferredValue:function(e){var t=pt();return Ee===null?t.memoizedState=e:Of(t,Ee.memoizedState,e)},useTransition:function(){var e=Ls(ko)[0],t=pt().memoizedState;return[e,t]},useMutableSource:gf,useSyncExternalStore:vf,useId:If,unstable_isNewReconciler:!1};function mt(e,t){if(e&&e.defaultProps){t=ge({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function El(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ge({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var es={isMounted:function(e){return(e=e._reactInternals)?$n(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=$e(),o=gn(e),i=Ut(r,o);i.payload=t,n!=null&&(i.callback=n),t=hn(e,i,o),t!==null&&(St(t,e,o,r),fi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=$e(),o=gn(e),i=Ut(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=hn(e,i,o),t!==null&&(St(t,e,o,r),fi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=$e(),r=gn(e),o=Ut(n,r);o.tag=2,t!=null&&(o.callback=t),t=hn(e,o,r),t!==null&&(St(t,e,r,n),fi(t,e,r))}};function Qu(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!go(n,r)||!go(o,i):!0}function zf(e,t,n){var r=!1,o=xn,i=t.contextType;return typeof i=="object"&&i!==null?i=ft(i):(o=qe(t)?Mn:ze.current,r=t.contextTypes,i=(r=r!=null)?Pr(e,o):xn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=es,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Gu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&es.enqueueReplaceState(t,t.state,null)}function kl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},xa(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=ft(i):(i=qe(t)?Mn:ze.current,o.context=Pr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(El(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&es.enqueueReplaceState(o,o.state,null),ji(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Tr(e,t){try{var n="",r=t;do n+=Vh(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ms(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Cl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var hg=typeof WeakMap=="function"?WeakMap:Map;function Lf(e,t,n){n=Ut(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Fi||(Fi=!0,jl=r),Cl(e,t)},n}function Mf(e,t,n){n=Ut(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Cl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Cl(e,t),typeof r!="function"&&(mn===null?mn=new Set([this]):mn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Ku(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new hg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Rg.bind(null,e,t,n),t.then(e,e))}function Yu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function qu(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ut(-1,1),t.tag=2,hn(n,t,1))),n.lanes|=1),e)}var mg=Wt.ReactCurrentOwner,Ke=!1;function Ue(e,t,n,r){t.child=e===null?ff(t,null,n,r):Nr(t,e.child,n,r)}function Xu(e,t,n,r,o){n=n.render;var i=t.ref;return fr(t,o),r=Pa(e,t,n,r,i,o),n=_a(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ht(e,t,o)):(fe&&n&&pa(t),t.flags|=1,Ue(e,t,r,o),t.child)}function Ju(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!La(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Af(e,t,i,r,o)):(e=yi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:go,n(s,r)&&e.ref===t.ref)return Ht(e,t,o)}return t.flags|=1,e=vn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Af(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(go(i,r)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ke=!0);else return t.lanes=e.lanes,Ht(e,t,o)}return Pl(e,t,n,r,o)}function Ff(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},se(lr,et),et|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,se(lr,et),et|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,se(lr,et),et|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,se(lr,et),et|=r;return Ue(e,t,o,n),t.child}function Uf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Pl(e,t,n,r,o){var i=qe(n)?Mn:ze.current;return i=Pr(t,i),fr(t,o),n=Pa(e,t,n,r,i,o),r=_a(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ht(e,t,o)):(fe&&r&&pa(t),t.flags|=1,Ue(e,t,n,o),t.child)}function Zu(e,t,n,r,o){if(qe(n)){var i=!0;Ti(t)}else i=!1;if(fr(t,o),t.stateNode===null)mi(e,t),zf(t,n,r),kl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=ft(u):(u=qe(n)?Mn:ze.current,u=Pr(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Gu(t,s,r,u),Jt=!1;var c=t.memoizedState;s.state=c,ji(t,r,s,o),a=t.memoizedState,l!==r||c!==a||Ye.current||Jt?(typeof f=="function"&&(El(t,n,f,r),a=t.memoizedState),(l=Jt||Qu(t,n,l,r,c,a,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,hf(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:mt(t.type,l),s.props=u,d=t.pendingProps,c=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=ft(a):(a=qe(n)?Mn:ze.current,a=Pr(t,a));var y=n.getDerivedStateFromProps;(f=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||c!==a)&&Gu(t,s,r,a),Jt=!1,c=t.memoizedState,s.state=c,ji(t,r,s,o);var S=t.memoizedState;l!==d||c!==S||Ye.current||Jt?(typeof y=="function"&&(El(t,n,y,r),S=t.memoizedState),(u=Jt||Qu(t,n,u,r,c,S,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,S,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,S,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=S),s.props=r,s.state=S,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return _l(e,t,n,r,i,o)}function _l(e,t,n,r,o,i){Uf(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Au(t,n,!1),Ht(e,t,i);r=t.stateNode,mg.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Nr(t,e.child,null,i),t.child=Nr(t,null,l,i)):Ue(e,t,l,i),t.memoizedState=r.state,o&&Au(t,n,!0),t.child}function Vf(e){var t=e.stateNode;t.pendingContext?Mu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mu(e,t.context,!1),Sa(e,t.containerInfo)}function ec(e,t,n,r,o){return _r(),ma(o),t.flags|=256,Ue(e,t,n,r),t.child}var Nl={dehydrated:null,treeContext:null,retryLane:0};function Rl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Bf(e,t,n){var r=t.pendingProps,o=he.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),se(he,o&1),e===null)return xl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=rs(s,r,0,null),e=Ln(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Rl(n),t.memoizedState=Nl,e):Ta(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return gg(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=vn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=vn(l,i):(i=Ln(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Rl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Nl,r}return i=e.child,e=i.sibling,r=vn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ta(e,t){return t=rs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ei(e,t,n,r){return r!==null&&ma(r),Nr(t,e.child,null,n),e=Ta(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function gg(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ms(Error(N(422))),ei(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=rs({mode:"visible",children:r.children},o,0,null),i=Ln(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Nr(t,e.child,null,s),t.child.memoizedState=Rl(s),t.memoizedState=Nl,i);if(!(t.mode&1))return ei(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(N(419)),r=Ms(i,r,void 0),ei(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ke||l){if(r=Pe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,$t(e,o),St(r,e,o,-1))}return za(),r=Ms(Error(N(421))),ei(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Tg.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,tt=pn(o.nextSibling),nt=t,fe=!0,wt=null,e!==null&&(at[ut++]=At,at[ut++]=Ft,at[ut++]=An,At=e.id,Ft=e.overflow,An=t),t=Ta(t,r.children),t.flags|=4096,t)}function tc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Sl(e.return,t,n)}function As(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function $f(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ue(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&tc(e,n,t);else if(e.tag===19)tc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(se(he,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&zi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),As(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&zi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}As(t,!0,n,null,i);break;case"together":As(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function mi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Un|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=vn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=vn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function vg(e,t,n){switch(t.tag){case 3:Vf(t),_r();break;case 5:mf(t);break;case 1:qe(t.type)&&Ti(t);break;case 4:Sa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;se(bi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(se(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Bf(e,t,n):(se(he,he.current&1),e=Ht(e,t,n),e!==null?e.sibling:null);se(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return $f(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),se(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,Ff(e,t,n)}return Ht(e,t,n)}var Hf,Tl,Wf,Qf;Hf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Tl=function(){};Wf=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,On(bt.current);var i=null;switch(n){case"input":o=Xs(e,o),r=Xs(e,r),i=[];break;case"select":o=ge({},o,{value:void 0}),r=ge({},r,{value:void 0}),i=[];break;case"textarea":o=el(e,o),r=el(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ni)}nl(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ao.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ao.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&ue("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Qf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Wr(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function yg(e,t,n){var r=t.pendingProps;switch(ha(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return qe(t.type)&&Ri(),be(t),null;case 3:return r=t.stateNode,Rr(),ce(Ye),ce(ze),ka(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Jo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,wt!==null&&(Ml(wt),wt=null))),Tl(e,t),be(t),null;case 5:Ea(t);var o=On(So.current);if(n=t.type,e!==null&&t.stateNode!=null)Wf(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return be(t),null}if(e=On(bt.current),Jo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ot]=t,r[wo]=i,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(o=0;o<Jr.length;o++)ue(Jr[o],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":cu(r,i),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ue("invalid",r);break;case"textarea":fu(r,i),ue("invalid",r)}nl(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Xo(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Xo(r.textContent,l,e),o=["children",""+l]):ao.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&ue("scroll",r)}switch(n){case"input":$o(r),du(r,i,!0);break;case"textarea":$o(r),pu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ni)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=wd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Ot]=t,e[wo]=r,Hf(e,t,!1,!1),t.stateNode=e;e:{switch(s=rl(n,r),n){case"dialog":ue("cancel",e),ue("close",e),o=r;break;case"iframe":case"object":case"embed":ue("load",e),o=r;break;case"video":case"audio":for(o=0;o<Jr.length;o++)ue(Jr[o],e);o=r;break;case"source":ue("error",e),o=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),o=r;break;case"details":ue("toggle",e),o=r;break;case"input":cu(e,r),o=Xs(e,r),ue("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ge({},r,{value:void 0}),ue("invalid",e);break;case"textarea":fu(e,r),o=el(e,r),ue("invalid",e);break;default:o=r}nl(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Ed(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&xd(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&uo(e,a):typeof a=="number"&&uo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(ao.hasOwnProperty(i)?a!=null&&i==="onScroll"&&ue("scroll",e):a!=null&&Zl(e,i,a,s))}switch(n){case"input":$o(e),du(e,r,!1);break;case"textarea":$o(e),pu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+wn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?ar(e,!!r.multiple,i,!1):r.defaultValue!=null&&ar(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ni)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)Qf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=On(So.current),On(bt.current),Jo(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ot]=t,(i=r.nodeValue!==n)&&(e=nt,e!==null))switch(e.tag){case 3:Xo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Xo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ot]=t,t.stateNode=r}return be(t),null;case 13:if(ce(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&tt!==null&&t.mode&1&&!(t.flags&128))cf(),_r(),t.flags|=98560,i=!1;else if(i=Jo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[Ot]=t}else _r(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),i=!1}else wt!==null&&(Ml(wt),wt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?ke===0&&(ke=3):za())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return Rr(),Tl(e,t),e===null&&vo(t.stateNode.containerInfo),be(t),null;case 10:return ya(t.type._context),be(t),null;case 17:return qe(t.type)&&Ri(),be(t),null;case 19:if(ce(he),i=t.memoizedState,i===null)return be(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Wr(i,!1);else{if(ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=zi(e),s!==null){for(t.flags|=128,Wr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return se(he,he.current&1|2),t.child}e=e.sibling}i.tail!==null&&we()>Or&&(t.flags|=128,r=!0,Wr(i,!1),t.lanes=4194304)}else{if(!r)if(e=zi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Wr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!fe)return be(t),null}else 2*we()-i.renderingStartTime>Or&&n!==1073741824&&(t.flags|=128,r=!0,Wr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=we(),t.sibling=null,n=he.current,se(he,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return ja(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?et&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function wg(e,t){switch(ha(t),t.tag){case 1:return qe(t.type)&&Ri(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Rr(),ce(Ye),ce(ze),ka(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ea(t),null;case 13:if(ce(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));_r()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(he),null;case 4:return Rr(),null;case 10:return ya(t.type._context),null;case 22:case 23:return ja(),null;case 24:return null;default:return null}}var ti=!1,je=!1,xg=typeof WeakSet=="function"?WeakSet:Set,A=null;function sr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ye(e,t,r)}else n.current=null}function Ol(e,t,n){try{n()}catch(r){ye(e,t,r)}}var nc=!1;function Sg(e,t){if(pl=Ci,e=qd(),fa(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,f=0,d=e,c=null;t:for(;;){for(var y;d!==n||o!==0&&d.nodeType!==3||(l=s+o),d!==i||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(y=d.firstChild)!==null;)c=d,d=y;for(;;){if(d===e)break t;if(c===n&&++u===o&&(l=s),c===i&&++f===r&&(a=s),(y=d.nextSibling)!==null)break;d=c,c=d.parentNode}d=y}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(hl={focusedElem:e,selectionRange:n},Ci=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var g=S.memoizedProps,k=S.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?g:mt(t.type,g),k);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(x){ye(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return S=nc,nc=!1,S}function io(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Ol(t,n,i)}o=o.next}while(o!==r)}}function ts(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Il(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Gf(e){var t=e.alternate;t!==null&&(e.alternate=null,Gf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ot],delete t[wo],delete t[vl],delete t[rg],delete t[og])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Kf(e){return e.tag===5||e.tag===3||e.tag===4}function rc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function bl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ni));else if(r!==4&&(e=e.child,e!==null))for(bl(e,t,n),e=e.sibling;e!==null;)bl(e,t,n),e=e.sibling}function Dl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Dl(e,t,n),e=e.sibling;e!==null;)Dl(e,t,n),e=e.sibling}var Ne=null,yt=!1;function Gt(e,t,n){for(n=n.child;n!==null;)Yf(e,t,n),n=n.sibling}function Yf(e,t,n){if(It&&typeof It.onCommitFiberUnmount=="function")try{It.onCommitFiberUnmount(Gi,n)}catch{}switch(n.tag){case 5:je||sr(n,t);case 6:var r=Ne,o=yt;Ne=null,Gt(e,t,n),Ne=r,yt=o,Ne!==null&&(yt?(e=Ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(yt?(e=Ne,n=n.stateNode,e.nodeType===8?Is(e.parentNode,n):e.nodeType===1&&Is(e,n),ho(e)):Is(Ne,n.stateNode));break;case 4:r=Ne,o=yt,Ne=n.stateNode.containerInfo,yt=!0,Gt(e,t,n),Ne=r,yt=o;break;case 0:case 11:case 14:case 15:if(!je&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Ol(n,t,s),o=o.next}while(o!==r)}Gt(e,t,n);break;case 1:if(!je&&(sr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ye(n,t,l)}Gt(e,t,n);break;case 21:Gt(e,t,n);break;case 22:n.mode&1?(je=(r=je)||n.memoizedState!==null,Gt(e,t,n),je=r):Gt(e,t,n);break;default:Gt(e,t,n)}}function oc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new xg),t.forEach(function(r){var o=Og.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ht(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Ne=l.stateNode,yt=!1;break e;case 3:Ne=l.stateNode.containerInfo,yt=!0;break e;case 4:Ne=l.stateNode.containerInfo,yt=!0;break e}l=l.return}if(Ne===null)throw Error(N(160));Yf(i,s,o),Ne=null,yt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){ye(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)qf(t,e),t=t.sibling}function qf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ht(t,e),Pt(e),r&4){try{io(3,e,e.return),ts(3,e)}catch(g){ye(e,e.return,g)}try{io(5,e,e.return)}catch(g){ye(e,e.return,g)}}break;case 1:ht(t,e),Pt(e),r&512&&n!==null&&sr(n,n.return);break;case 5:if(ht(t,e),Pt(e),r&512&&n!==null&&sr(n,n.return),e.flags&32){var o=e.stateNode;try{uo(o,"")}catch(g){ye(e,e.return,g)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&vd(o,i),rl(l,s);var u=rl(l,i);for(s=0;s<a.length;s+=2){var f=a[s],d=a[s+1];f==="style"?Ed(o,d):f==="dangerouslySetInnerHTML"?xd(o,d):f==="children"?uo(o,d):Zl(o,f,d,u)}switch(l){case"input":Js(o,i);break;case"textarea":yd(o,i);break;case"select":var c=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?ar(o,!!i.multiple,y,!1):c!==!!i.multiple&&(i.defaultValue!=null?ar(o,!!i.multiple,i.defaultValue,!0):ar(o,!!i.multiple,i.multiple?[]:"",!1))}o[wo]=i}catch(g){ye(e,e.return,g)}}break;case 6:if(ht(t,e),Pt(e),r&4){if(e.stateNode===null)throw Error(N(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){ye(e,e.return,g)}}break;case 3:if(ht(t,e),Pt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ho(t.containerInfo)}catch(g){ye(e,e.return,g)}break;case 4:ht(t,e),Pt(e);break;case 13:ht(t,e),Pt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ba=we())),r&4&&oc(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(je=(u=je)||f,ht(t,e),je=u):ht(t,e),Pt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(A=e,f=e.child;f!==null;){for(d=A=f;A!==null;){switch(c=A,y=c.child,c.tag){case 0:case 11:case 14:case 15:io(4,c,c.return);break;case 1:sr(c,c.return);var S=c.stateNode;if(typeof S.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(g){ye(r,n,g)}}break;case 5:sr(c,c.return);break;case 22:if(c.memoizedState!==null){sc(d);continue}}y!==null?(y.return=c,A=y):sc(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Sd("display",s))}catch(g){ye(e,e.return,g)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(g){ye(e,e.return,g)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:ht(t,e),Pt(e),r&4&&oc(e);break;case 21:break;default:ht(t,e),Pt(e)}}function Pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Kf(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(uo(o,""),r.flags&=-33);var i=rc(e);Dl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=rc(e);bl(e,l,s);break;default:throw Error(N(161))}}catch(a){ye(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Eg(e,t,n){A=e,Xf(e)}function Xf(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var o=A,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||ti;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||je;l=ti;var u=je;if(ti=s,(je=a)&&!u)for(A=o;A!==null;)s=A,a=s.child,s.tag===22&&s.memoizedState!==null?lc(o):a!==null?(a.return=s,A=a):lc(o);for(;i!==null;)A=i,Xf(i),i=i.sibling;A=o,ti=l,je=u}ic(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,A=i):ic(e)}}function ic(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:je||ts(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!je)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:mt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&$u(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}$u(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&ho(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}je||t.flags&512&&Il(t)}catch(c){ye(t,t.return,c)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function sc(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function lc(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ts(4,t)}catch(a){ye(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ye(t,o,a)}}var i=t.return;try{Il(t)}catch(a){ye(t,i,a)}break;case 5:var s=t.return;try{Il(t)}catch(a){ye(t,s,a)}}}catch(a){ye(t,t.return,a)}if(t===e){A=null;break}var l=t.sibling;if(l!==null){l.return=t.return,A=l;break}A=t.return}}var kg=Math.ceil,Ai=Wt.ReactCurrentDispatcher,Oa=Wt.ReactCurrentOwner,dt=Wt.ReactCurrentBatchConfig,J=0,Pe=null,Se=null,Re=0,et=0,lr=En(0),ke=0,Po=null,Un=0,ns=0,Ia=0,so=null,Ge=null,ba=0,Or=1/0,Lt=null,Fi=!1,jl=null,mn=null,ni=!1,an=null,Ui=0,lo=0,zl=null,gi=-1,vi=0;function $e(){return J&6?we():gi!==-1?gi:gi=we()}function gn(e){return e.mode&1?J&2&&Re!==0?Re&-Re:sg.transition!==null?(vi===0&&(vi=jd()),vi):(e=te,e!==0||(e=window.event,e=e===void 0?16:Vd(e.type)),e):1}function St(e,t,n,r){if(50<lo)throw lo=0,zl=null,Error(N(185));Do(e,n,r),(!(J&2)||e!==Pe)&&(e===Pe&&(!(J&2)&&(ns|=n),ke===4&&en(e,Re)),Xe(e,r),n===1&&J===0&&!(t.mode&1)&&(Or=we()+500,Ji&&kn()))}function Xe(e,t){var n=e.callbackNode;sm(e,t);var r=ki(e,e===Pe?Re:0);if(r===0)n!==null&&gu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&gu(n),t===1)e.tag===0?ig(ac.bind(null,e)):lf(ac.bind(null,e)),tg(function(){!(J&6)&&kn()}),n=null;else{switch(zd(r)){case 1:n=oa;break;case 4:n=bd;break;case 16:n=Ei;break;case 536870912:n=Dd;break;default:n=Ei}n=ip(n,Jf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Jf(e,t){if(gi=-1,vi=0,J&6)throw Error(N(327));var n=e.callbackNode;if(pr()&&e.callbackNode!==n)return null;var r=ki(e,e===Pe?Re:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Vi(e,r);else{t=r;var o=J;J|=2;var i=ep();(Pe!==e||Re!==t)&&(Lt=null,Or=we()+500,zn(e,t));do try{_g();break}catch(l){Zf(e,l)}while(!0);va(),Ai.current=i,J=o,Se!==null?t=0:(Pe=null,Re=0,t=ke)}if(t!==0){if(t===2&&(o=al(e),o!==0&&(r=o,t=Ll(e,o))),t===1)throw n=Po,zn(e,0),en(e,r),Xe(e,we()),n;if(t===6)en(e,r);else{if(o=e.current.alternate,!(r&30)&&!Cg(o)&&(t=Vi(e,r),t===2&&(i=al(e),i!==0&&(r=i,t=Ll(e,i))),t===1))throw n=Po,zn(e,0),en(e,r),Xe(e,we()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:_n(e,Ge,Lt);break;case 3:if(en(e,r),(r&130023424)===r&&(t=ba+500-we(),10<t)){if(ki(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){$e(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=gl(_n.bind(null,e,Ge,Lt),t);break}_n(e,Ge,Lt);break;case 4:if(en(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-xt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=we()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*kg(r/1960))-r,10<r){e.timeoutHandle=gl(_n.bind(null,e,Ge,Lt),r);break}_n(e,Ge,Lt);break;case 5:_n(e,Ge,Lt);break;default:throw Error(N(329))}}}return Xe(e,we()),e.callbackNode===n?Jf.bind(null,e):null}function Ll(e,t){var n=so;return e.current.memoizedState.isDehydrated&&(zn(e,t).flags|=256),e=Vi(e,t),e!==2&&(t=Ge,Ge=n,t!==null&&Ml(t)),e}function Ml(e){Ge===null?Ge=e:Ge.push.apply(Ge,e)}function Cg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Et(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function en(e,t){for(t&=~Ia,t&=~ns,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-xt(t),r=1<<n;e[n]=-1,t&=~r}}function ac(e){if(J&6)throw Error(N(327));pr();var t=ki(e,0);if(!(t&1))return Xe(e,we()),null;var n=Vi(e,t);if(e.tag!==0&&n===2){var r=al(e);r!==0&&(t=r,n=Ll(e,r))}if(n===1)throw n=Po,zn(e,0),en(e,t),Xe(e,we()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,_n(e,Ge,Lt),Xe(e,we()),null}function Da(e,t){var n=J;J|=1;try{return e(t)}finally{J=n,J===0&&(Or=we()+500,Ji&&kn())}}function Vn(e){an!==null&&an.tag===0&&!(J&6)&&pr();var t=J;J|=1;var n=dt.transition,r=te;try{if(dt.transition=null,te=1,e)return e()}finally{te=r,dt.transition=n,J=t,!(J&6)&&kn()}}function ja(){et=lr.current,ce(lr)}function zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,eg(n)),Se!==null)for(n=Se.return;n!==null;){var r=n;switch(ha(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ri();break;case 3:Rr(),ce(Ye),ce(ze),ka();break;case 5:Ea(r);break;case 4:Rr();break;case 13:ce(he);break;case 19:ce(he);break;case 10:ya(r.type._context);break;case 22:case 23:ja()}n=n.return}if(Pe=e,Se=e=vn(e.current,null),Re=et=t,ke=0,Po=null,Ia=ns=Un=0,Ge=so=null,Tn!==null){for(t=0;t<Tn.length;t++)if(n=Tn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Tn=null}return e}function Zf(e,t){do{var n=Se;try{if(va(),pi.current=Mi,Li){for(var r=me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Li=!1}if(Fn=0,Ce=Ee=me=null,oo=!1,Eo=0,Oa.current=null,n===null||n.return===null){ke=1,Po=t,Se=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Re,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var c=f.alternate;c?(f.updateQueue=c.updateQueue,f.memoizedState=c.memoizedState,f.lanes=c.lanes):(f.updateQueue=null,f.memoizedState=null)}var y=Yu(s);if(y!==null){y.flags&=-257,qu(y,s,l,i,t),y.mode&1&&Ku(i,u,t),t=y,a=u;var S=t.updateQueue;if(S===null){var g=new Set;g.add(a),t.updateQueue=g}else S.add(a);break e}else{if(!(t&1)){Ku(i,u,t),za();break e}a=Error(N(426))}}else if(fe&&l.mode&1){var k=Yu(s);if(k!==null){!(k.flags&65536)&&(k.flags|=256),qu(k,s,l,i,t),ma(Tr(a,l));break e}}i=a=Tr(a,l),ke!==4&&(ke=2),so===null?so=[i]:so.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Lf(i,a,t);Bu(i,h);break e;case 1:l=a;var p=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(mn===null||!mn.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Mf(i,l,t);Bu(i,x);break e}}i=i.return}while(i!==null)}np(n)}catch(P){t=P,Se===n&&n!==null&&(Se=n=n.return);continue}break}while(!0)}function ep(){var e=Ai.current;return Ai.current=Mi,e===null?Mi:e}function za(){(ke===0||ke===3||ke===2)&&(ke=4),Pe===null||!(Un&268435455)&&!(ns&268435455)||en(Pe,Re)}function Vi(e,t){var n=J;J|=2;var r=ep();(Pe!==e||Re!==t)&&(Lt=null,zn(e,t));do try{Pg();break}catch(o){Zf(e,o)}while(!0);if(va(),J=n,Ai.current=r,Se!==null)throw Error(N(261));return Pe=null,Re=0,ke}function Pg(){for(;Se!==null;)tp(Se)}function _g(){for(;Se!==null&&!Xh();)tp(Se)}function tp(e){var t=op(e.alternate,e,et);e.memoizedProps=e.pendingProps,t===null?np(e):Se=t,Oa.current=null}function np(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=wg(n,t),n!==null){n.flags&=32767,Se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ke=6,Se=null;return}}else if(n=yg(n,t,et),n!==null){Se=n;return}if(t=t.sibling,t!==null){Se=t;return}Se=t=e}while(t!==null);ke===0&&(ke=5)}function _n(e,t,n){var r=te,o=dt.transition;try{dt.transition=null,te=1,Ng(e,t,n,r)}finally{dt.transition=o,te=r}return null}function Ng(e,t,n,r){do pr();while(an!==null);if(J&6)throw Error(N(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(lm(e,i),e===Pe&&(Se=Pe=null,Re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ni||(ni=!0,ip(Ei,function(){return pr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=dt.transition,dt.transition=null;var s=te;te=1;var l=J;J|=4,Oa.current=null,Sg(e,n),qf(n,e),Gm(hl),Ci=!!pl,hl=pl=null,e.current=n,Eg(n),Jh(),J=l,te=s,dt.transition=i}else e.current=n;if(ni&&(ni=!1,an=e,Ui=o),i=e.pendingLanes,i===0&&(mn=null),tm(n.stateNode),Xe(e,we()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Fi)throw Fi=!1,e=jl,jl=null,e;return Ui&1&&e.tag!==0&&pr(),i=e.pendingLanes,i&1?e===zl?lo++:(lo=0,zl=e):lo=0,kn(),null}function pr(){if(an!==null){var e=zd(Ui),t=dt.transition,n=te;try{if(dt.transition=null,te=16>e?16:e,an===null)var r=!1;else{if(e=an,an=null,Ui=0,J&6)throw Error(N(331));var o=J;for(J|=4,A=e.current;A!==null;){var i=A,s=i.child;if(A.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(A=u;A!==null;){var f=A;switch(f.tag){case 0:case 11:case 15:io(8,f,i)}var d=f.child;if(d!==null)d.return=f,A=d;else for(;A!==null;){f=A;var c=f.sibling,y=f.return;if(Gf(f),f===u){A=null;break}if(c!==null){c.return=y,A=c;break}A=y}}}var S=i.alternate;if(S!==null){var g=S.child;if(g!==null){S.child=null;do{var k=g.sibling;g.sibling=null,g=k}while(g!==null)}}A=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,A=s;else e:for(;A!==null;){if(i=A,i.flags&2048)switch(i.tag){case 0:case 11:case 15:io(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,A=h;break e}A=i.return}}var p=e.current;for(A=p;A!==null;){s=A;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,A=m;else e:for(s=p;A!==null;){if(l=A,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ts(9,l)}}catch(P){ye(l,l.return,P)}if(l===s){A=null;break e}var x=l.sibling;if(x!==null){x.return=l.return,A=x;break e}A=l.return}}if(J=o,kn(),It&&typeof It.onPostCommitFiberRoot=="function")try{It.onPostCommitFiberRoot(Gi,e)}catch{}r=!0}return r}finally{te=n,dt.transition=t}}return!1}function uc(e,t,n){t=Tr(n,t),t=Lf(e,t,1),e=hn(e,t,1),t=$e(),e!==null&&(Do(e,1,t),Xe(e,t))}function ye(e,t,n){if(e.tag===3)uc(e,e,n);else for(;t!==null;){if(t.tag===3){uc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(mn===null||!mn.has(r))){e=Tr(n,e),e=Mf(t,e,1),t=hn(t,e,1),e=$e(),t!==null&&(Do(t,1,e),Xe(t,e));break}}t=t.return}}function Rg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=$e(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Re&n)===n&&(ke===4||ke===3&&(Re&130023424)===Re&&500>we()-ba?zn(e,0):Ia|=n),Xe(e,t)}function rp(e,t){t===0&&(e.mode&1?(t=Qo,Qo<<=1,!(Qo&130023424)&&(Qo=4194304)):t=1);var n=$e();e=$t(e,t),e!==null&&(Do(e,t,n),Xe(e,n))}function Tg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),rp(e,n)}function Og(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),rp(e,n)}var op;op=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ye.current)Ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ke=!1,vg(e,t,n);Ke=!!(e.flags&131072)}else Ke=!1,fe&&t.flags&1048576&&af(t,Ii,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;mi(e,t),e=t.pendingProps;var o=Pr(t,ze.current);fr(t,n),o=Pa(null,t,r,e,o,n);var i=_a();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(r)?(i=!0,Ti(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,xa(t),o.updater=es,t.stateNode=o,o._reactInternals=t,kl(t,r,e,n),t=_l(null,t,r,!0,i,n)):(t.tag=0,fe&&i&&pa(t),Ue(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(mi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=bg(r),e=mt(r,e),o){case 0:t=Pl(null,t,r,e,n);break e;case 1:t=Zu(null,t,r,e,n);break e;case 11:t=Xu(null,t,r,e,n);break e;case 14:t=Ju(null,t,r,mt(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:mt(r,o),Pl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:mt(r,o),Zu(e,t,r,o,n);case 3:e:{if(Vf(t),e===null)throw Error(N(387));r=t.pendingProps,i=t.memoizedState,o=i.element,hf(e,t),ji(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Tr(Error(N(423)),t),t=ec(e,t,r,n,o);break e}else if(r!==o){o=Tr(Error(N(424)),t),t=ec(e,t,r,n,o);break e}else for(tt=pn(t.stateNode.containerInfo.firstChild),nt=t,fe=!0,wt=null,n=ff(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(_r(),r===o){t=Ht(e,t,n);break e}Ue(e,t,r,n)}t=t.child}return t;case 5:return mf(t),e===null&&xl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,ml(r,o)?s=null:i!==null&&ml(r,i)&&(t.flags|=32),Uf(e,t),Ue(e,t,s,n),t.child;case 6:return e===null&&xl(t),null;case 13:return Bf(e,t,n);case 4:return Sa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Nr(t,null,r,n):Ue(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:mt(r,o),Xu(e,t,r,o,n);case 7:return Ue(e,t,t.pendingProps,n),t.child;case 8:return Ue(e,t,t.pendingProps.children,n),t.child;case 12:return Ue(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,se(bi,r._currentValue),r._currentValue=s,i!==null)if(Et(i.value,s)){if(i.children===o.children&&!Ye.current){t=Ht(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Ut(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Sl(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(N(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Sl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Ue(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,fr(t,n),o=ft(o),r=r(o),t.flags|=1,Ue(e,t,r,n),t.child;case 14:return r=t.type,o=mt(r,t.pendingProps),o=mt(r.type,o),Ju(e,t,r,o,n);case 15:return Af(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:mt(r,o),mi(e,t),t.tag=1,qe(r)?(e=!0,Ti(t)):e=!1,fr(t,n),zf(t,r,o),kl(t,r,o,n),_l(null,t,r,!0,e,n);case 19:return $f(e,t,n);case 22:return Ff(e,t,n)}throw Error(N(156,t.tag))};function ip(e,t){return Id(e,t)}function Ig(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(e,t,n,r){return new Ig(e,t,n,r)}function La(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bg(e){if(typeof e=="function")return La(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ta)return 11;if(e===na)return 14}return 2}function vn(e,t){var n=e.alternate;return n===null?(n=ct(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function yi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")La(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Xn:return Ln(n.children,o,i,t);case ea:s=8,o|=8;break;case Gs:return e=ct(12,n,t,o|2),e.elementType=Gs,e.lanes=i,e;case Ks:return e=ct(13,n,t,o),e.elementType=Ks,e.lanes=i,e;case Ys:return e=ct(19,n,t,o),e.elementType=Ys,e.lanes=i,e;case hd:return rs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case fd:s=10;break e;case pd:s=9;break e;case ta:s=11;break e;case na:s=14;break e;case Xt:s=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=ct(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Ln(e,t,n,r){return e=ct(7,e,r,t),e.lanes=n,e}function rs(e,t,n,r){return e=ct(22,e,r,t),e.elementType=hd,e.lanes=n,e.stateNode={isHidden:!1},e}function Fs(e,t,n){return e=ct(6,e,null,t),e.lanes=n,e}function Us(e,t,n){return t=ct(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Dg(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=xs(0),this.expirationTimes=xs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Ma(e,t,n,r,o,i,s,l,a){return e=new Dg(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ct(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},xa(i),e}function jg(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:qn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function sp(e){if(!e)return xn;e=e._reactInternals;e:{if($n(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(qe(n))return sf(e,n,t)}return t}function lp(e,t,n,r,o,i,s,l,a){return e=Ma(n,r,!0,e,o,i,s,l,a),e.context=sp(null),n=e.current,r=$e(),o=gn(n),i=Ut(r,o),i.callback=t??null,hn(n,i,o),e.current.lanes=o,Do(e,o,r),Xe(e,r),e}function os(e,t,n,r){var o=t.current,i=$e(),s=gn(o);return n=sp(n),t.context===null?t.context=n:t.pendingContext=n,t=Ut(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=hn(o,t,s),e!==null&&(St(e,o,s,i),fi(e,o,s)),s}function Bi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function cc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Aa(e,t){cc(e,t),(e=e.alternate)&&cc(e,t)}function zg(){return null}var ap=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fa(e){this._internalRoot=e}is.prototype.render=Fa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));os(e,t,null,null)};is.prototype.unmount=Fa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vn(function(){os(null,e,null,null)}),t[Bt]=null}};function is(e){this._internalRoot=e}is.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ad();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Zt.length&&t!==0&&t<Zt[n].priority;n++);Zt.splice(n,0,e),n===0&&Ud(e)}};function Ua(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ss(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function dc(){}function Lg(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Bi(s);i.call(u)}}var s=lp(t,r,e,0,null,!1,!1,"",dc);return e._reactRootContainer=s,e[Bt]=s.current,vo(e.nodeType===8?e.parentNode:e),Vn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=Bi(a);l.call(u)}}var a=Ma(e,0,!1,null,null,!1,!1,"",dc);return e._reactRootContainer=a,e[Bt]=a.current,vo(e.nodeType===8?e.parentNode:e),Vn(function(){os(t,a,n,r)}),a}function ls(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=Bi(s);l.call(a)}}os(t,s,e,o)}else s=Lg(n,t,e,o,r);return Bi(s)}Ld=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Xr(t.pendingLanes);n!==0&&(ia(t,n|1),Xe(t,we()),!(J&6)&&(Or=we()+500,kn()))}break;case 13:Vn(function(){var r=$t(e,1);if(r!==null){var o=$e();St(r,e,1,o)}}),Aa(e,1)}};sa=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var n=$e();St(t,e,134217728,n)}Aa(e,134217728)}};Md=function(e){if(e.tag===13){var t=gn(e),n=$t(e,t);if(n!==null){var r=$e();St(n,e,t,r)}Aa(e,t)}};Ad=function(){return te};Fd=function(e,t){var n=te;try{return te=e,t()}finally{te=n}};il=function(e,t,n){switch(t){case"input":if(Js(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Xi(r);if(!o)throw Error(N(90));gd(r),Js(r,o)}}}break;case"textarea":yd(e,n);break;case"select":t=n.value,t!=null&&ar(e,!!n.multiple,t,!1)}};Pd=Da;_d=Vn;var Mg={usingClientEntryPoint:!1,Events:[zo,tr,Xi,kd,Cd,Da]},Qr={findFiberByHostInstance:Rn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ag={bundleType:Qr.bundleType,version:Qr.version,rendererPackageName:Qr.rendererPackageName,rendererConfig:Qr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Wt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Td(e),e===null?null:e.stateNode},findFiberByHostInstance:Qr.findFiberByHostInstance||zg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ri.isDisabled&&ri.supportsFiber)try{Gi=ri.inject(Ag),It=ri}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mg;ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ua(t))throw Error(N(200));return jg(e,t,null,n)};ot.createRoot=function(e,t){if(!Ua(e))throw Error(N(299));var n=!1,r="",o=ap;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Ma(e,1,!1,null,null,n,!1,r,o),e[Bt]=t.current,vo(e.nodeType===8?e.parentNode:e),new Fa(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=Td(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return Vn(e)};ot.hydrate=function(e,t,n){if(!ss(t))throw Error(N(200));return ls(null,e,t,!0,n)};ot.hydrateRoot=function(e,t,n){if(!Ua(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=ap;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=lp(t,null,e,1,n??null,o,!1,i,s),e[Bt]=t.current,vo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new is(t)};ot.render=function(e,t,n){if(!ss(t))throw Error(N(200));return ls(null,e,t,!1,n)};ot.unmountComponentAtNode=function(e){if(!ss(e))throw Error(N(40));return e._reactRootContainer?(Vn(function(){ls(null,null,e,!1,function(){e._reactRootContainer=null,e[Bt]=null})}),!0):!1};ot.unstable_batchedUpdates=Da;ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ss(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return ls(e,t,n,!1,r)};ot.version="18.3.1-next-f1338f8080-20240426";function up(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(up)}catch(e){console.error(e)}}up(),ad.exports=ot;var cp=ad.exports;const Fg=qc(cp);var dp,fc=cp;dp=fc.createRoot,fc.hydrateRoot;const Ug="modulepreload",Vg=function(e){return"/"+e},pc={},oe=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),l=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));o=Promise.allSettled(n.map(a=>{if(a=Vg(a),a in pc)return;pc[a]=!0;const u=a.endsWith(".css"),f=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${f}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":Ug,u||(d.as="script"),d.crossOrigin="",d.href=a,l&&d.setAttribute("nonce",l),document.head.appendChild(d),u)return new Promise((c,y)=>{d.addEventListener("load",c),d.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${a}`)))})}))}function i(s){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=s,window.dispatchEvent(l),!l.defaultPrevented)throw s}return o.then(s=>{for(const l of s||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})};/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _o(){return _o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_o.apply(this,arguments)}var un;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(un||(un={}));const hc="popstate";function Bg(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:l}=r.location;return Al("",{pathname:i,search:s,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:$i(o)}return Hg(t,n,null,e)}function xe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function fp(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function $g(){return Math.random().toString(36).substr(2,8)}function mc(e,t){return{usr:e.state,key:e.key,idx:t}}function Al(e,t,n,r){return n===void 0&&(n=null),_o({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?jr(t):t,{state:n,key:t&&t.key||r||$g()})}function $i(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function jr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Hg(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,l=un.Pop,a=null,u=f();u==null&&(u=0,s.replaceState(_o({},s.state,{idx:u}),""));function f(){return(s.state||{idx:null}).idx}function d(){l=un.Pop;let k=f(),h=k==null?null:k-u;u=k,a&&a({action:l,location:g.location,delta:h})}function c(k,h){l=un.Push;let p=Al(g.location,k,h);u=f()+1;let m=mc(p,u),x=g.createHref(p);try{s.pushState(m,"",x)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;o.location.assign(x)}i&&a&&a({action:l,location:g.location,delta:1})}function y(k,h){l=un.Replace;let p=Al(g.location,k,h);u=f();let m=mc(p,u),x=g.createHref(p);s.replaceState(m,"",x),i&&a&&a({action:l,location:g.location,delta:0})}function S(k){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof k=="string"?k:$i(k);return p=p.replace(/ $/,"%20"),xe(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let g={get action(){return l},get location(){return e(o,s)},listen(k){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(hc,d),a=k,()=>{o.removeEventListener(hc,d),a=null}},createHref(k){return t(o,k)},createURL:S,encodeLocation(k){let h=S(k);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:c,replace:y,go(k){return s.go(k)}};return g}var gc;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(gc||(gc={}));function Wg(e,t,n){return n===void 0&&(n="/"),Qg(e,t,n,!1)}function Qg(e,t,n,r){let o=typeof t=="string"?jr(t):t,i=Va(o.pathname||"/",n);if(i==null)return null;let s=pp(e);Gg(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=ov(i);l=nv(s[a],u,r)}return l}function pp(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};a.relativePath.startsWith("/")&&(xe(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=yn([r,a.relativePath]),f=n.concat(a);i.children&&i.children.length>0&&(xe(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),pp(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:ev(u,i.index),routesMeta:f})};return e.forEach((i,s)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,s);else for(let a of hp(i.path))o(i,s,a)}),t}function hp(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=hp(r.join("/")),l=[];return l.push(...s.map(a=>a===""?i:[i,a].join("/"))),o&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function Gg(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:tv(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Kg=/^:[\w-]+$/,Yg=3,qg=2,Xg=1,Jg=10,Zg=-2,vc=e=>e==="*";function ev(e,t){let n=e.split("/"),r=n.length;return n.some(vc)&&(r+=Zg),t&&(r+=qg),n.filter(o=>!vc(o)).reduce((o,i)=>o+(Kg.test(i)?Yg:i===""?Xg:Jg),r)}function tv(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function nv(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",d=yc({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},f),c=a.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=yc({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},f)),!d)return null;Object.assign(o,d.params),s.push({params:o,pathname:yn([i,d.pathname]),pathnameBase:av(yn([i,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(i=yn([i,d.pathnameBase]))}return s}function yc(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=rv(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,f,d)=>{let{paramName:c,isOptional:y}=f;if(c==="*"){let g=l[d]||"";s=i.slice(0,i.length-g.length).replace(/(.)\/+$/,"$1")}const S=l[d];return y&&!S?u[c]=void 0:u[c]=(S||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function rv(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),fp(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function ov(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return fp(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Va(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function iv(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?jr(e):e;return{pathname:n?n.startsWith("/")?n:sv(n,t):t,search:uv(r),hash:cv(o)}}function sv(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Vs(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function lv(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Ba(e,t){let n=lv(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function $a(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=jr(e):(o=_o({},e),xe(!o.pathname||!o.pathname.includes("?"),Vs("?","pathname","search",o)),xe(!o.pathname||!o.pathname.includes("#"),Vs("#","pathname","hash",o)),xe(!o.search||!o.search.includes("#"),Vs("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,l;if(s==null)l=n;else{let d=t.length-1;if(!r&&s.startsWith("..")){let c=s.split("/");for(;c[0]==="..";)c.shift(),d-=1;o.pathname=c.join("/")}l=d>=0?t[d]:"/"}let a=iv(o,l),u=s&&s!=="/"&&s.endsWith("/"),f=(i||s===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||f)&&(a.pathname+="/"),a}const yn=e=>e.join("/").replace(/\/\/+/g,"/"),av=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),uv=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,cv=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function dv(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const mp=["post","put","patch","delete"];new Set(mp);const fv=["get",...mp];new Set(fv);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function No(){return No=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},No.apply(this,arguments)}const Ha=v.createContext(null),pv=v.createContext(null),Cn=v.createContext(null),as=v.createContext(null),Dt=v.createContext({outlet:null,matches:[],isDataRoute:!1}),gp=v.createContext(null);function hv(e,t){let{relative:n}=t===void 0?{}:t;zr()||xe(!1);let{basename:r,navigator:o}=v.useContext(Cn),{hash:i,pathname:s,search:l}=wp(e,{relative:n}),a=s;return r!=="/"&&(a=s==="/"?r:yn([r,s])),o.createHref({pathname:a,search:l,hash:i})}function zr(){return v.useContext(as)!=null}function Lr(){return zr()||xe(!1),v.useContext(as).location}function vp(e){v.useContext(Cn).static||v.useLayoutEffect(e)}function yp(){let{isDataRoute:e}=v.useContext(Dt);return e?Tv():mv()}function mv(){zr()||xe(!1);let e=v.useContext(Ha),{basename:t,future:n,navigator:r}=v.useContext(Cn),{matches:o}=v.useContext(Dt),{pathname:i}=Lr(),s=JSON.stringify(Ba(o,n.v7_relativeSplatPath)),l=v.useRef(!1);return vp(()=>{l.current=!0}),v.useCallback(function(u,f){if(f===void 0&&(f={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let d=$a(u,JSON.parse(s),i,f.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:yn([t,d.pathname])),(f.replace?r.replace:r.push)(d,f.state,f)},[t,r,s,i,e])}const gv=v.createContext(null);function vv(e){let t=v.useContext(Dt).outlet;return t&&v.createElement(gv.Provider,{value:e},t)}function J1(){let{matches:e}=v.useContext(Dt),t=e[e.length-1];return t?t.params:{}}function wp(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=v.useContext(Cn),{matches:o}=v.useContext(Dt),{pathname:i}=Lr(),s=JSON.stringify(Ba(o,r.v7_relativeSplatPath));return v.useMemo(()=>$a(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function yv(e,t){return wv(e,t)}function wv(e,t,n,r){zr()||xe(!1);let{navigator:o}=v.useContext(Cn),{matches:i}=v.useContext(Dt),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=Lr(),f;if(t){var d;let k=typeof t=="string"?jr(t):t;a==="/"||(d=k.pathname)!=null&&d.startsWith(a)||xe(!1),f=k}else f=u;let c=f.pathname||"/",y=c;if(a!=="/"){let k=a.replace(/^\//,"").split("/");y="/"+c.replace(/^\//,"").split("/").slice(k.length).join("/")}let S=Wg(e,{pathname:y}),g=Cv(S&&S.map(k=>Object.assign({},k,{params:Object.assign({},l,k.params),pathname:yn([a,o.encodeLocation?o.encodeLocation(k.pathname).pathname:k.pathname]),pathnameBase:k.pathnameBase==="/"?a:yn([a,o.encodeLocation?o.encodeLocation(k.pathnameBase).pathname:k.pathnameBase])})),i,n,r);return t&&g?v.createElement(as.Provider,{value:{location:No({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:un.Pop}},g):g}function xv(){let e=Rv(),t=dv(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},t),n?v.createElement("pre",{style:o},n):null,null)}const Sv=v.createElement(xv,null);class Ev extends v.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?v.createElement(Dt.Provider,{value:this.props.routeContext},v.createElement(gp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function kv(e){let{routeContext:t,match:n,children:r}=e,o=v.useContext(Ha);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),v.createElement(Dt.Provider,{value:t},r)}function Cv(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let f=s.findIndex(d=>d.route.id&&(l==null?void 0:l[d.route.id])!==void 0);f>=0||xe(!1),s=s.slice(0,Math.min(s.length,f+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<s.length;f++){let d=s[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=f),d.route.id){let{loaderData:c,errors:y}=n,S=d.route.loader&&c[d.route.id]===void 0&&(!y||y[d.route.id]===void 0);if(d.route.lazy||S){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((f,d,c)=>{let y,S=!1,g=null,k=null;n&&(y=l&&d.route.id?l[d.route.id]:void 0,g=d.route.errorElement||Sv,a&&(u<0&&c===0?(S=!0,k=null):u===c&&(S=!0,k=d.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,c+1)),p=()=>{let m;return y?m=g:S?m=k:d.route.Component?m=v.createElement(d.route.Component,null):d.route.element?m=d.route.element:m=f,v.createElement(kv,{match:d,routeContext:{outlet:f,matches:h,isDataRoute:n!=null},children:m})};return n&&(d.route.ErrorBoundary||d.route.errorElement||c===0)?v.createElement(Ev,{location:n.location,revalidation:n.revalidation,component:g,error:y,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var xp=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(xp||{}),Hi=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Hi||{});function Pv(e){let t=v.useContext(Ha);return t||xe(!1),t}function _v(e){let t=v.useContext(pv);return t||xe(!1),t}function Nv(e){let t=v.useContext(Dt);return t||xe(!1),t}function Sp(e){let t=Nv(),n=t.matches[t.matches.length-1];return n.route.id||xe(!1),n.route.id}function Rv(){var e;let t=v.useContext(gp),n=_v(Hi.UseRouteError),r=Sp(Hi.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Tv(){let{router:e}=Pv(xp.UseNavigateStable),t=Sp(Hi.UseNavigateStable),n=v.useRef(!1);return vp(()=>{n.current=!0}),v.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,No({fromRouteId:t},i)))},[e,t])}function Ep(e){let{to:t,replace:n,state:r,relative:o}=e;zr()||xe(!1);let{future:i,static:s}=v.useContext(Cn),{matches:l}=v.useContext(Dt),{pathname:a}=Lr(),u=yp(),f=$a(t,Ba(l,i.v7_relativeSplatPath),a,o==="path"),d=JSON.stringify(f);return v.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:o}),[u,d,o,n,r]),null}function Z1(e){return vv(e.context)}function Z(e){xe(!1)}function Ov(e){let{basename:t="/",children:n=null,location:r,navigationType:o=un.Pop,navigator:i,static:s=!1,future:l}=e;zr()&&xe(!1);let a=t.replace(/^\/*/,"/"),u=v.useMemo(()=>({basename:a,navigator:i,static:s,future:No({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=jr(r));let{pathname:f="/",search:d="",hash:c="",state:y=null,key:S="default"}=r,g=v.useMemo(()=>{let k=Va(f,a);return k==null?null:{location:{pathname:k,search:d,hash:c,state:y,key:S},navigationType:o}},[a,f,d,c,y,S,o]);return g==null?null:v.createElement(Cn.Provider,{value:u},v.createElement(as.Provider,{children:n,value:g}))}function Iv(e){let{children:t,location:n}=e;return yv(Fl(t),n)}new Promise(()=>{});function Fl(e,t){t===void 0&&(t=[]);let n=[];return v.Children.forEach(e,(r,o)=>{if(!v.isValidElement(r))return;let i=[...t,o];if(r.type===v.Fragment){n.push.apply(n,Fl(r.props.children,i));return}r.type!==Z&&xe(!1),!r.props.index||!r.props.children||xe(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Fl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ul(){return Ul=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ul.apply(this,arguments)}function bv(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Dv(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function jv(e,t){return e.button===0&&(!t||t==="_self")&&!Dv(e)}const zv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Lv="6";try{window.__reactRouterVersion=Lv}catch{}const Mv="startTransition",wc=Rh[Mv];function Av(e){let{basename:t,children:n,future:r,window:o}=e,i=v.useRef();i.current==null&&(i.current=Bg({window:o,v5Compat:!0}));let s=i.current,[l,a]=v.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},f=v.useCallback(d=>{u&&wc?wc(()=>a(d)):a(d)},[a,u]);return v.useLayoutEffect(()=>s.listen(f),[s,f]),v.createElement(Ov,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}const Fv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Uv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ew=v.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:l,target:a,to:u,preventScrollReset:f,viewTransition:d}=t,c=bv(t,zv),{basename:y}=v.useContext(Cn),S,g=!1;if(typeof u=="string"&&Uv.test(u)&&(S=u,Fv))try{let m=new URL(window.location.href),x=u.startsWith("//")?new URL(m.protocol+u):new URL(u),P=Va(x.pathname,y);x.origin===m.origin&&P!=null?u=P+x.search+x.hash:g=!0}catch{}let k=hv(u,{relative:o}),h=Vv(u,{replace:s,state:l,target:a,preventScrollReset:f,relative:o,viewTransition:d});function p(m){r&&r(m),m.defaultPrevented||h(m)}return v.createElement("a",Ul({},c,{href:S||k,onClick:g||i?r:p,ref:n,target:a}))});var xc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(xc||(xc={}));var Sc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Sc||(Sc={}));function Vv(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:l}=t===void 0?{}:t,a=yp(),u=Lr(),f=wp(e,{relative:s});return v.useCallback(d=>{if(jv(d,n)){d.preventDefault();let c=r!==void 0?r:$i(u)===$i(f);a(e,{replace:c,state:o,preventScrollReset:i,relative:s,viewTransition:l})}},[u,a,f,r,o,n,e,i,s,l])}var us=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},cs=typeof window>"u"||"Deno"in globalThis;function gt(){}function Bv(e,t){return typeof e=="function"?e(t):e}function $v(e){return typeof e=="number"&&e>=0&&e!==1/0}function Hv(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ec(e,t){return typeof e=="function"?e(t):e}function Wv(e,t){return typeof e=="function"?e(t):e}function kc(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:l}=e;if(s){if(r){if(t.queryHash!==Wa(s,t.options))return!1}else if(!To(t.queryKey,s))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||i&&!i(t))}function Cc(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(Ro(t.options.mutationKey)!==Ro(i))return!1}else if(!To(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Wa(e,t){return((t==null?void 0:t.queryKeyHashFn)||Ro)(e)}function Ro(e){return JSON.stringify(e,(t,n)=>Vl(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function To(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!To(e[n],t[n])):!1}function kp(e,t){if(e===t)return e;const n=Pc(e)&&Pc(t);if(n||Vl(e)&&Vl(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,l=n?[]:{};let a=0;for(let u=0;u<s;u++){const f=n?u:i[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(l[f]=void 0,a++):(l[f]=kp(e[f],t[f]),l[f]===e[f]&&e[f]!==void 0&&a++)}return o===s&&a===o?e:l}return t}function Pc(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Vl(e){if(!_c(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!_c(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function _c(e){return Object.prototype.toString.call(e)==="[object Object]"}function Qv(e){return new Promise(t=>{setTimeout(t,e)})}function Gv(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?kp(e,t):t}function Kv(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function Yv(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Qa=Symbol();function Cp(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Qa?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var In,tn,mr,Bc,qv=(Bc=class extends us{constructor(){super();ee(this,In);ee(this,tn);ee(this,mr);G(this,mr,t=>{if(!cs&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){C(this,tn)||this.setEventListener(C(this,mr))}onUnsubscribe(){var t;this.hasListeners()||((t=C(this,tn))==null||t.call(this),G(this,tn,void 0))}setEventListener(t){var n;G(this,mr,t),(n=C(this,tn))==null||n.call(this),G(this,tn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){C(this,In)!==t&&(G(this,In,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof C(this,In)=="boolean"?C(this,In):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},In=new WeakMap,tn=new WeakMap,mr=new WeakMap,Bc),Pp=new qv,gr,nn,vr,$c,Xv=($c=class extends us{constructor(){super();ee(this,gr,!0);ee(this,nn);ee(this,vr);G(this,vr,t=>{if(!cs&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){C(this,nn)||this.setEventListener(C(this,vr))}onUnsubscribe(){var t;this.hasListeners()||((t=C(this,nn))==null||t.call(this),G(this,nn,void 0))}setEventListener(t){var n;G(this,vr,t),(n=C(this,nn))==null||n.call(this),G(this,nn,t(this.setOnline.bind(this)))}setOnline(t){C(this,gr)!==t&&(G(this,gr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return C(this,gr)}},gr=new WeakMap,nn=new WeakMap,vr=new WeakMap,$c),Wi=new Xv;function Jv(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function Zv(e){return Math.min(1e3*2**e,3e4)}function _p(e){return(e??"online")==="online"?Wi.isOnline():!0}var Np=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Bs(e){return e instanceof Np}function Rp(e){let t=!1,n=0,r=!1,o;const i=Jv(),s=g=>{var k;r||(c(new Np(g)),(k=e.abort)==null||k.call(e))},l=()=>{t=!0},a=()=>{t=!1},u=()=>Pp.isFocused()&&(e.networkMode==="always"||Wi.isOnline())&&e.canRun(),f=()=>_p(e.networkMode)&&e.canRun(),d=g=>{var k;r||(r=!0,(k=e.onSuccess)==null||k.call(e,g),o==null||o(),i.resolve(g))},c=g=>{var k;r||(r=!0,(k=e.onError)==null||k.call(e,g),o==null||o(),i.reject(g))},y=()=>new Promise(g=>{var k;o=h=>{(r||u())&&g(h)},(k=e.onPause)==null||k.call(e)}).then(()=>{var g;o=void 0,r||(g=e.onContinue)==null||g.call(e)}),S=()=>{if(r)return;let g;const k=n===0?e.initialPromise:void 0;try{g=k??e.fn()}catch(h){g=Promise.reject(h)}Promise.resolve(g).then(d).catch(h=>{var O;if(r)return;const p=e.retry??(cs?0:3),m=e.retryDelay??Zv,x=typeof m=="function"?m(n,h):m,P=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,h);if(t||!P){c(h);return}n++,(O=e.onFail)==null||O.call(e,n,h),Qv(x).then(()=>u()?void 0:y()).then(()=>{t?c(h):S()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:l,continueRetry:a,canStart:f,start:()=>(f()?S():y().then(S),i)}}function ey(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=l=>setTimeout(l,0);const i=l=>{t?e.push(l):o(()=>{n(l)})},s=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||s()}return a},batchCalls:l=>(...a)=>{i(()=>{l(...a)})},schedule:i,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var Ve=ey(),bn,Hc,Tp=(Hc=class{constructor(){ee(this,bn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),$v(this.gcTime)&&G(this,bn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(cs?1/0:5*60*1e3))}clearGcTimeout(){C(this,bn)&&(clearTimeout(C(this,bn)),G(this,bn,void 0))}},bn=new WeakMap,Hc),yr,wr,lt,De,Oo,Dn,vt,zt,Wc,ty=(Wc=class extends Tp{constructor(t){super();ee(this,vt);ee(this,yr);ee(this,wr);ee(this,lt);ee(this,De);ee(this,Oo);ee(this,Dn);G(this,Dn,!1),G(this,Oo,t.defaultOptions),this.setOptions(t.options),this.observers=[],G(this,lt,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,G(this,yr,ry(this.options)),this.state=t.state??C(this,yr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=C(this,De))==null?void 0:t.promise}setOptions(t){this.options={...C(this,Oo),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&C(this,lt).remove(this)}setData(t,n){const r=Gv(this.state.data,t,this.options);return Oe(this,vt,zt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Oe(this,vt,zt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=C(this,De))==null?void 0:r.promise;return(o=C(this,De))==null||o.cancel(t),n?n.then(gt).catch(gt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(C(this,yr))}isActive(){return this.observers.some(t=>Wv(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Qa||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Hv(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=C(this,De))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=C(this,De))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),C(this,lt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(C(this,De)&&(C(this,Dn)?C(this,De).cancel({revert:!0}):C(this,De).cancelRetry()),this.scheduleGc()),C(this,lt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Oe(this,vt,zt).call(this,{type:"invalidate"})}fetch(t,n){var a,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(C(this,De))return C(this,De).continueRetry(),C(this,De).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(c=>c.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,o=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(G(this,Dn,!0),r.signal)})},i=()=>{const d=Cp(this.options,n),c={queryKey:this.queryKey,meta:this.meta};return o(c),G(this,Dn,!1),this.options.persister?this.options.persister(d,c,this):d(c)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(a=this.options.behavior)==null||a.onFetch(s,this),G(this,wr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=s.fetchOptions)==null?void 0:u.meta))&&Oe(this,vt,zt).call(this,{type:"fetch",meta:(f=s.fetchOptions)==null?void 0:f.meta});const l=d=>{var c,y,S,g;Bs(d)&&d.silent||Oe(this,vt,zt).call(this,{type:"error",error:d}),Bs(d)||((y=(c=C(this,lt).config).onError)==null||y.call(c,d,this),(g=(S=C(this,lt).config).onSettled)==null||g.call(S,this.state.data,d,this)),this.scheduleGc()};return G(this,De,Rp({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var c,y,S,g;if(d===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(k){l(k);return}(y=(c=C(this,lt).config).onSuccess)==null||y.call(c,d,this),(g=(S=C(this,lt).config).onSettled)==null||g.call(S,d,this.state.error,this),this.scheduleGc()},onError:l,onFail:(d,c)=>{Oe(this,vt,zt).call(this,{type:"failed",failureCount:d,error:c})},onPause:()=>{Oe(this,vt,zt).call(this,{type:"pause"})},onContinue:()=>{Oe(this,vt,zt).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),C(this,De).start()}},yr=new WeakMap,wr=new WeakMap,lt=new WeakMap,De=new WeakMap,Oo=new WeakMap,Dn=new WeakMap,vt=new WeakSet,zt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ny(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Bs(o)&&o.revert&&C(this,wr)?{...C(this,wr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ve.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),C(this,lt).notify({query:this,type:"updated",action:t})})},Wc);function ny(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:_p(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ry(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Nt,Qc,oy=(Qc=class extends us{constructor(t={}){super();ee(this,Nt);this.config=t,G(this,Nt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??Wa(o,n);let s=this.get(i);return s||(s=new ty({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){C(this,Nt).has(t.queryHash)||(C(this,Nt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=C(this,Nt).get(t.queryHash);n&&(t.destroy(),n===t&&C(this,Nt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ve.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return C(this,Nt).get(t)}getAll(){return[...C(this,Nt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>kc(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>kc(t,r)):n}notify(t){Ve.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ve.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ve.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Nt=new WeakMap,Qc),Rt,Fe,jn,Tt,qt,Gc,iy=(Gc=class extends Tp{constructor(t){super();ee(this,Tt);ee(this,Rt);ee(this,Fe);ee(this,jn);this.mutationId=t.mutationId,G(this,Fe,t.mutationCache),G(this,Rt,[]),this.state=t.state||sy(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){C(this,Rt).includes(t)||(C(this,Rt).push(t),this.clearGcTimeout(),C(this,Fe).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){G(this,Rt,C(this,Rt).filter(n=>n!==t)),this.scheduleGc(),C(this,Fe).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){C(this,Rt).length||(this.state.status==="pending"?this.scheduleGc():C(this,Fe).remove(this))}continue(){var t;return((t=C(this,jn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,l,a,u,f,d,c,y,S,g,k,h,p,m,x,P,O,z;G(this,jn,Rp({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(E,I)=>{Oe(this,Tt,qt).call(this,{type:"failed",failureCount:E,error:I})},onPause:()=>{Oe(this,Tt,qt).call(this,{type:"pause"})},onContinue:()=>{Oe(this,Tt,qt).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>C(this,Fe).canRun(this)}));const n=this.state.status==="pending",r=!C(this,jn).canStart();try{if(!n){Oe(this,Tt,qt).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=C(this,Fe).config).onMutate)==null?void 0:i.call(o,t,this));const I=await((l=(s=this.options).onMutate)==null?void 0:l.call(s,t));I!==this.state.context&&Oe(this,Tt,qt).call(this,{type:"pending",context:I,variables:t,isPaused:r})}const E=await C(this,jn).start();return await((u=(a=C(this,Fe).config).onSuccess)==null?void 0:u.call(a,E,t,this.state.context,this)),await((d=(f=this.options).onSuccess)==null?void 0:d.call(f,E,t,this.state.context)),await((y=(c=C(this,Fe).config).onSettled)==null?void 0:y.call(c,E,null,this.state.variables,this.state.context,this)),await((g=(S=this.options).onSettled)==null?void 0:g.call(S,E,null,t,this.state.context)),Oe(this,Tt,qt).call(this,{type:"success",data:E}),E}catch(E){try{throw await((h=(k=C(this,Fe).config).onError)==null?void 0:h.call(k,E,t,this.state.context,this)),await((m=(p=this.options).onError)==null?void 0:m.call(p,E,t,this.state.context)),await((P=(x=C(this,Fe).config).onSettled)==null?void 0:P.call(x,void 0,E,this.state.variables,this.state.context,this)),await((z=(O=this.options).onSettled)==null?void 0:z.call(O,void 0,E,t,this.state.context)),E}finally{Oe(this,Tt,qt).call(this,{type:"error",error:E})}}finally{C(this,Fe).runNext(this)}}},Rt=new WeakMap,Fe=new WeakMap,jn=new WeakMap,Tt=new WeakSet,qt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ve.batch(()=>{C(this,Rt).forEach(r=>{r.onMutationUpdate(t)}),C(this,Fe).notify({mutation:this,type:"updated",action:t})})},Gc);function sy(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Je,Io,Kc,ly=(Kc=class extends us{constructor(t={}){super();ee(this,Je);ee(this,Io);this.config=t,G(this,Je,new Map),G(this,Io,Date.now())}build(t,n,r){const o=new iy({mutationCache:this,mutationId:++Uo(this,Io)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=oi(t),r=C(this,Je).get(n)??[];r.push(t),C(this,Je).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=oi(t);if(C(this,Je).has(n)){const o=(r=C(this,Je).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?C(this,Je).delete(n):C(this,Je).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=C(this,Je).get(oi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=C(this,Je).get(oi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Ve.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...C(this,Je).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Cc(n,r))}findAll(t={}){return this.getAll().filter(n=>Cc(t,n))}notify(t){Ve.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ve.batch(()=>Promise.all(t.map(n=>n.continue().catch(gt))))}},Je=new WeakMap,Io=new WeakMap,Kc);function oi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Nc(e){return{onFetch:(t,n)=>{var f,d,c,y,S;const r=t.options,o=(c=(d=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:d.fetchMore)==null?void 0:c.direction,i=((y=t.state.data)==null?void 0:y.pages)||[],s=((S=t.state.data)==null?void 0:S.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const u=async()=>{let g=!1;const k=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(t.signal.aborted?g=!0:t.signal.addEventListener("abort",()=>{g=!0}),t.signal)})},h=Cp(t.options,t.fetchOptions),p=async(m,x,P)=>{if(g)return Promise.reject();if(x==null&&m.pages.length)return Promise.resolve(m);const O={queryKey:t.queryKey,pageParam:x,direction:P?"backward":"forward",meta:t.options.meta};k(O);const z=await h(O),{maxPages:E}=t.options,I=P?Yv:Kv;return{pages:I(m.pages,z,E),pageParams:I(m.pageParams,x,E)}};if(o&&i.length){const m=o==="backward",x=m?ay:Rc,P={pages:i,pageParams:s},O=x(r,P);l=await p(P,O,m)}else{const m=e??i.length;do{const x=a===0?s[0]??r.initialPageParam:Rc(r,l);if(a>0&&x==null)break;l=await p(l,x),a++}while(a<m)}return l};t.options.persister?t.fetchFn=()=>{var g,k;return(k=(g=t.options).persister)==null?void 0:k.call(g,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Rc(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function ay(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ve,rn,on,xr,Sr,sn,Er,kr,Yc,uy=(Yc=class{constructor(e={}){ee(this,ve);ee(this,rn);ee(this,on);ee(this,xr);ee(this,Sr);ee(this,sn);ee(this,Er);ee(this,kr);G(this,ve,e.queryCache||new oy),G(this,rn,e.mutationCache||new ly),G(this,on,e.defaultOptions||{}),G(this,xr,new Map),G(this,Sr,new Map),G(this,sn,0)}mount(){Uo(this,sn)._++,C(this,sn)===1&&(G(this,Er,Pp.subscribe(async e=>{e&&(await this.resumePausedMutations(),C(this,ve).onFocus())})),G(this,kr,Wi.subscribe(async e=>{e&&(await this.resumePausedMutations(),C(this,ve).onOnline())})))}unmount(){var e,t;Uo(this,sn)._--,C(this,sn)===0&&((e=C(this,Er))==null||e.call(this),G(this,Er,void 0),(t=C(this,kr))==null||t.call(this),G(this,kr,void 0))}isFetching(e){return C(this,ve).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return C(this,rn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=C(this,ve).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=C(this,ve).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Ec(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return C(this,ve).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=C(this,ve).get(r.queryHash),i=o==null?void 0:o.state.data,s=Bv(t,i);if(s!==void 0)return C(this,ve).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Ve.batch(()=>C(this,ve).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=C(this,ve).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=C(this,ve);Ve.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=C(this,ve),r={type:"active",...e};return Ve.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Ve.batch(()=>C(this,ve).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(gt).catch(gt)}invalidateQueries(e={},t={}){return Ve.batch(()=>{if(C(this,ve).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Ve.batch(()=>C(this,ve).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(gt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(gt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=C(this,ve).build(this,t);return n.isStaleByTime(Ec(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(gt).catch(gt)}fetchInfiniteQuery(e){return e.behavior=Nc(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(gt).catch(gt)}ensureInfiniteQueryData(e){return e.behavior=Nc(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Wi.isOnline()?C(this,rn).resumePausedMutations():Promise.resolve()}getQueryCache(){return C(this,ve)}getMutationCache(){return C(this,rn)}getDefaultOptions(){return C(this,on)}setDefaultOptions(e){G(this,on,e)}setQueryDefaults(e,t){C(this,xr).set(Ro(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...C(this,xr).values()];let n={};return t.forEach(r=>{To(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){C(this,Sr).set(Ro(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...C(this,Sr).values()];let n={};return t.forEach(r=>{To(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...C(this,on).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Wa(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===Qa&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...C(this,on).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){C(this,ve).clear(),C(this,rn).clear()}},ve=new WeakMap,rn=new WeakMap,on=new WeakMap,xr=new WeakMap,Sr=new WeakMap,sn=new WeakMap,Er=new WeakMap,kr=new WeakMap,Yc),cy=v.createContext(void 0),dy=({client:e,children:t})=>(v.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),w.jsx(cy.Provider,{value:e,children:t}));const Tc=[{id:"1",name:"Test Berater",email:"<EMAIL>",role:"berater",teamId:"team1",password:"test123"},{id:"2",name:"Test Mentor",email:"<EMAIL>",role:"mentor",teamId:"team1",assignedBeraterIds:["1","5"],password:"test123"},{id:"3",name:"Test Teamleiter",email:"<EMAIL>",role:"teamleiter",teamId:"team1",password:"test123"},{id:"4",name:"Test Gebietsmanager",email:"<EMAIL>",role:"gebietsmanager",password:"test123"},{id:"5",name:"Test Admin",email:"<EMAIL>",role:"admin",password:"test123"},{id:"6",name:"Zweiter Berater",email:"<EMAIL>",role:"berater",teamId:"team1",mentorId:"2",password:"test123"},{id:"7",name:"Refik Emre Ak",email:"<EMAIL>",role:"admin",roles:["admin","berater"],teamId:"team1",password:"admin123",isMultiRole:!0}],_e=[];for(let e=0;e<256;++e)_e.push((e+256).toString(16).slice(1));function fy(e,t=0){return(_e[e[t+0]]+_e[e[t+1]]+_e[e[t+2]]+_e[e[t+3]]+"-"+_e[e[t+4]]+_e[e[t+5]]+"-"+_e[e[t+6]]+_e[e[t+7]]+"-"+_e[e[t+8]]+_e[e[t+9]]+"-"+_e[e[t+10]]+_e[e[t+11]]+_e[e[t+12]]+_e[e[t+13]]+_e[e[t+14]]+_e[e[t+15]]).toLowerCase()}let $s;const py=new Uint8Array(16);function hy(){if(!$s){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");$s=crypto.getRandomValues.bind(crypto)}return $s(py)}const my=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Oc={randomUUID:my};function Be(e,t,n){var o;if(Oc.randomUUID&&!t&&!e)return Oc.randomUUID();e=e||{};const r=e.random??((o=e.rng)==null?void 0:o.call(e))??hy();if(r.length<16)throw new Error("Random bytes length must be >= 16");return r[6]=r[6]&15|64,r[8]=r[8]&63|128,fy(r)}var gy=e=>{switch(e){case"success":return wy;case"info":return Sy;case"warning":return xy;case"error":return Ey;default:return null}},vy=Array(12).fill(0),yy=({visible:e})=>j.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},j.createElement("div",{className:"sonner-spinner"},vy.map((t,n)=>j.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),wy=j.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},j.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),xy=j.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},j.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Sy=j.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},j.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Ey=j.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},j.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ky=()=>{let[e,t]=j.useState(document.hidden);return j.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Bl=1,Cy=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Bl++,i=this.toasts.find(l=>l.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(l=>l.id===o?(this.publish({...l,...e,id:o,title:n}),{...l,...e,id:o,dismissible:s,title:n}):l):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(_y(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:l})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:l})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:l})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Bl++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Ze=new Cy,Py=(e,t)=>{let n=(t==null?void 0:t.id)||Bl++;return Ze.addToast({title:e,...t,id:n}),n},_y=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Ny=Py,Ry=()=>Ze.toasts,Ae=Object.assign(Ny,{success:Ze.success,info:Ze.info,warning:Ze.warning,error:Ze.error,custom:Ze.custom,message:Ze.message,promise:Ze.promise,dismiss:Ze.dismiss,loading:Ze.loading},{getHistory:Ry});function Ty(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Ty(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function ii(e){return e.label!==void 0}var Oy=3,Iy="32px",by=4e3,Dy=356,jy=14,zy=20,Ly=200;function My(...e){return e.filter(Boolean).join(" ")}var Ay=e=>{var t,n,r,o,i,s,l,a,u,f;let{invert:d,toast:c,unstyled:y,interacting:S,setHeights:g,visibleToasts:k,heights:h,index:p,toasts:m,expanded:x,removeToast:P,defaultRichColors:O,closeButton:z,style:E,cancelButtonStyle:I,actionButtonStyle:R,className:U="",descriptionClassName:L="",duration:H,position:ne,gap:de,loadingIcon:pe,expandByDefault:Le,classNames:_,icons:M,closeButtonAriaLabel:B="Close toast",pauseWhenPageIsHidden:F,cn:$}=e,[T,b]=j.useState(!1),[D,V]=j.useState(!1),[K,Y]=j.useState(!1),[q,Me]=j.useState(!1),[Mo,Ar]=j.useState(0),[eh,qa]=j.useState(0),Xa=j.useRef(null),Wn=j.useRef(null),th=p===0,nh=p+1<=k,st=c.type,Qn=c.dismissible!==!1,rh=c.className||"",oh=c.descriptionClassName||"",Ao=j.useMemo(()=>h.findIndex(W=>W.toastId===c.id)||0,[h,c.id]),ih=j.useMemo(()=>{var W;return(W=c.closeButton)!=null?W:z},[c.closeButton,z]),Ja=j.useMemo(()=>c.duration||H||by,[c.duration,H]),ds=j.useRef(0),Gn=j.useRef(0),Za=j.useRef(0),Kn=j.useRef(null),[eu,sh]=ne.split("-"),tu=j.useMemo(()=>h.reduce((W,le,ie)=>ie>=Ao?W:W+le.height,0),[h,Ao]),nu=ky(),lh=c.invert||d,fs=st==="loading";Gn.current=j.useMemo(()=>Ao*de+tu,[Ao,tu]),j.useEffect(()=>{b(!0)},[]),j.useLayoutEffect(()=>{if(!T)return;let W=Wn.current,le=W.style.height;W.style.height="auto";let ie=W.getBoundingClientRect().height;W.style.height=le,qa(ie),g(kt=>kt.find(Ct=>Ct.toastId===c.id)?kt.map(Ct=>Ct.toastId===c.id?{...Ct,height:ie}:Ct):[{toastId:c.id,height:ie,position:c.position},...kt])},[T,c.title,c.description,g,c.id]);let Qt=j.useCallback(()=>{V(!0),Ar(Gn.current),g(W=>W.filter(le=>le.toastId!==c.id)),setTimeout(()=>{P(c)},Ly)},[c,P,g,Gn]);j.useEffect(()=>{if(c.promise&&st==="loading"||c.duration===1/0||c.type==="loading")return;let W,le=Ja;return x||S||F&&nu?(()=>{if(Za.current<ds.current){let ie=new Date().getTime()-ds.current;le=le-ie}Za.current=new Date().getTime()})():le!==1/0&&(ds.current=new Date().getTime(),W=setTimeout(()=>{var ie;(ie=c.onAutoClose)==null||ie.call(c,c),Qt()},le)),()=>clearTimeout(W)},[x,S,Le,c,Ja,Qt,c.promise,st,F,nu]),j.useEffect(()=>{let W=Wn.current;if(W){let le=W.getBoundingClientRect().height;return qa(le),g(ie=>[{toastId:c.id,height:le,position:c.position},...ie]),()=>g(ie=>ie.filter(kt=>kt.toastId!==c.id))}},[g,c.id]),j.useEffect(()=>{c.delete&&Qt()},[Qt,c.delete]);function ah(){return M!=null&&M.loading?j.createElement("div",{className:"sonner-loader","data-visible":st==="loading"},M.loading):pe?j.createElement("div",{className:"sonner-loader","data-visible":st==="loading"},pe):j.createElement(yy,{visible:st==="loading"})}return j.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:Wn,className:$(U,rh,_==null?void 0:_.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,_==null?void 0:_.default,_==null?void 0:_[st],(n=c==null?void 0:c.classNames)==null?void 0:n[st]),"data-sonner-toast":"","data-rich-colors":(r=c.richColors)!=null?r:O,"data-styled":!(c.jsx||c.unstyled||y),"data-mounted":T,"data-promise":!!c.promise,"data-removed":D,"data-visible":nh,"data-y-position":eu,"data-x-position":sh,"data-index":p,"data-front":th,"data-swiping":K,"data-dismissible":Qn,"data-type":st,"data-invert":lh,"data-swipe-out":q,"data-expanded":!!(x||Le&&T),style:{"--index":p,"--toasts-before":p,"--z-index":m.length-p,"--offset":`${D?Mo:Gn.current}px`,"--initial-height":Le?"auto":`${eh}px`,...E,...c.style},onPointerDown:W=>{fs||!Qn||(Xa.current=new Date,Ar(Gn.current),W.target.setPointerCapture(W.pointerId),W.target.tagName!=="BUTTON"&&(Y(!0),Kn.current={x:W.clientX,y:W.clientY}))},onPointerUp:()=>{var W,le,ie,kt;if(q||!Qn)return;Kn.current=null;let Ct=Number(((W=Wn.current)==null?void 0:W.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Fo=new Date().getTime()-((le=Xa.current)==null?void 0:le.getTime()),uh=Math.abs(Ct)/Fo;if(Math.abs(Ct)>=zy||uh>.11){Ar(Gn.current),(ie=c.onDismiss)==null||ie.call(c,c),Qt(),Me(!0);return}(kt=Wn.current)==null||kt.style.setProperty("--swipe-amount","0px"),Y(!1)},onPointerMove:W=>{var le;if(!Kn.current||!Qn)return;let ie=W.clientY-Kn.current.y,kt=W.clientX-Kn.current.x,Ct=(eu==="top"?Math.min:Math.max)(0,ie),Fo=W.pointerType==="touch"?10:2;Math.abs(Ct)>Fo?(le=Wn.current)==null||le.style.setProperty("--swipe-amount",`${ie}px`):Math.abs(kt)>Fo&&(Kn.current=null)}},ih&&!c.jsx?j.createElement("button",{"aria-label":B,"data-disabled":fs,"data-close-button":!0,onClick:fs||!Qn?()=>{}:()=>{var W;Qt(),(W=c.onDismiss)==null||W.call(c,c)},className:$(_==null?void 0:_.closeButton,(o=c==null?void 0:c.classNames)==null?void 0:o.closeButton)},j.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},j.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),j.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||j.isValidElement(c.title)?c.jsx||c.title:j.createElement(j.Fragment,null,st||c.icon||c.promise?j.createElement("div",{"data-icon":"",className:$(_==null?void 0:_.icon,(i=c==null?void 0:c.classNames)==null?void 0:i.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||ah():null,c.type!=="loading"?c.icon||(M==null?void 0:M[st])||gy(st):null):null,j.createElement("div",{"data-content":"",className:$(_==null?void 0:_.content,(s=c==null?void 0:c.classNames)==null?void 0:s.content)},j.createElement("div",{"data-title":"",className:$(_==null?void 0:_.title,(l=c==null?void 0:c.classNames)==null?void 0:l.title)},c.title),c.description?j.createElement("div",{"data-description":"",className:$(L,oh,_==null?void 0:_.description,(a=c==null?void 0:c.classNames)==null?void 0:a.description)},c.description):null),j.isValidElement(c.cancel)?c.cancel:c.cancel&&ii(c.cancel)?j.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||I,onClick:W=>{var le,ie;ii(c.cancel)&&Qn&&((ie=(le=c.cancel).onClick)==null||ie.call(le,W),Qt())},className:$(_==null?void 0:_.cancelButton,(u=c==null?void 0:c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,j.isValidElement(c.action)?c.action:c.action&&ii(c.action)?j.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||R,onClick:W=>{var le,ie;ii(c.action)&&(W.defaultPrevented||((ie=(le=c.action).onClick)==null||ie.call(le,W),Qt()))},className:$(_==null?void 0:_.actionButton,(f=c==null?void 0:c.classNames)==null?void 0:f.actionButton)},c.action.label):null))};function Ic(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Fy=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:l,theme:a="light",richColors:u,duration:f,style:d,visibleToasts:c=Oy,toastOptions:y,dir:S=Ic(),gap:g=jy,loadingIcon:k,icons:h,containerAriaLabel:p="Notifications",pauseWhenPageIsHidden:m,cn:x=My}=e,[P,O]=j.useState([]),z=j.useMemo(()=>Array.from(new Set([n].concat(P.filter(F=>F.position).map(F=>F.position)))),[P,n]),[E,I]=j.useState([]),[R,U]=j.useState(!1),[L,H]=j.useState(!1),[ne,de]=j.useState(a!=="system"?a:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),pe=j.useRef(null),Le=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),_=j.useRef(null),M=j.useRef(!1),B=j.useCallback(F=>{var $;($=P.find(T=>T.id===F.id))!=null&&$.delete||Ze.dismiss(F.id),O(T=>T.filter(({id:b})=>b!==F.id))},[P]);return j.useEffect(()=>Ze.subscribe(F=>{if(F.dismiss){O($=>$.map(T=>T.id===F.id?{...T,delete:!0}:T));return}setTimeout(()=>{Fg.flushSync(()=>{O($=>{let T=$.findIndex(b=>b.id===F.id);return T!==-1?[...$.slice(0,T),{...$[T],...F},...$.slice(T+1)]:[F,...$]})})})}),[]),j.useEffect(()=>{if(a!=="system"){de(a);return}a==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?de("dark"):de("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:F})=>{de(F?"dark":"light")})},[a]),j.useEffect(()=>{P.length<=1&&U(!1)},[P]),j.useEffect(()=>{let F=$=>{var T,b;r.every(D=>$[D]||$.code===D)&&(U(!0),(T=pe.current)==null||T.focus()),$.code==="Escape"&&(document.activeElement===pe.current||(b=pe.current)!=null&&b.contains(document.activeElement))&&U(!1)};return document.addEventListener("keydown",F),()=>document.removeEventListener("keydown",F)},[r]),j.useEffect(()=>{if(pe.current)return()=>{_.current&&(_.current.focus({preventScroll:!0}),_.current=null,M.current=!1)}},[pe.current]),P.length?j.createElement("section",{"aria-label":`${p} ${Le}`,tabIndex:-1},z.map((F,$)=>{var T;let[b,D]=F.split("-");return j.createElement("ol",{key:F,dir:S==="auto"?Ic():S,tabIndex:-1,ref:pe,className:s,"data-sonner-toaster":!0,"data-theme":ne,"data-y-position":b,"data-x-position":D,style:{"--front-toast-height":`${((T=E[0])==null?void 0:T.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||Iy,"--width":`${Dy}px`,"--gap":`${g}px`,...d},onBlur:V=>{M.current&&!V.currentTarget.contains(V.relatedTarget)&&(M.current=!1,_.current&&(_.current.focus({preventScroll:!0}),_.current=null))},onFocus:V=>{V.target instanceof HTMLElement&&V.target.dataset.dismissible==="false"||M.current||(M.current=!0,_.current=V.relatedTarget)},onMouseEnter:()=>U(!0),onMouseMove:()=>U(!0),onMouseLeave:()=>{L||U(!1)},onPointerDown:V=>{V.target instanceof HTMLElement&&V.target.dataset.dismissible==="false"||H(!0)},onPointerUp:()=>H(!1)},P.filter(V=>!V.position&&$===0||V.position===F).map((V,K)=>{var Y,q;return j.createElement(Ay,{key:V.id,icons:h,index:K,toast:V,defaultRichColors:u,duration:(Y=y==null?void 0:y.duration)!=null?Y:f,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:t,visibleToasts:c,closeButton:(q=y==null?void 0:y.closeButton)!=null?q:i,interacting:L,position:F,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:B,toasts:P.filter(Me=>Me.position==V.position),heights:E.filter(Me=>Me.position==V.position),setHeights:I,expandByDefault:o,gap:g,loadingIcon:k,expanded:R,pauseWhenPageIsHidden:m,cn:x})}))})):null};const Uy=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,bc=8,Vy=/[!@#$%^&*(),.?":{}|<>]/,By=/[A-Z]/,$y=/[a-z]/,Hy=/[0-9]/,Wy=["password","123456","123456789","qwerty","abc123","password123","admin","letmein","welcome","monkey","1234567890","password1"],Qy=["123","234","345","456","567","678","789","890","abc","bcd","cde","def","efg","fgh","ghi","hij","qwe","wer","ert","rty","tyu","yui","uio","iop"],Gy=e=>{const t=e.toLowerCase();return!!(Wy.some(n=>t.includes(n))||Qy.some(n=>t.includes(n))||/(.)\1{2,}/.test(e))},Ky=(e,t)=>!1,Yy=e=>{let t=0;return/[a-z]/.test(e)&&(t+=26),/[A-Z]/.test(e)&&(t+=26),/[0-9]/.test(e)&&(t+=10),/[^a-zA-Z0-9]/.test(e)&&(t+=32),e.length*Math.log2(t)},Op=e=>e.trim()?Uy.test(e)?null:{field:"email",message:"Bitte geben Sie eine gültige E-Mail-Adresse ein"}:{field:"email",message:"E-Mail-Adresse ist erforderlich"},Ip=e=>e.trim()?e.trim().length<2?{field:"name",message:"Name muss mindestens 2 Zeichen lang sein"}:e.trim().length>50?{field:"name",message:"Name darf maximal 50 Zeichen lang sein"}:null:{field:"name",message:"Name ist erforderlich"},bp=(e,t)=>{const n={minLength:e.length>=bc,hasUppercase:By.test(e),hasLowercase:$y.test(e),hasNumbers:Hy.test(e),hasSpecialChars:Vy.test(e),noCommonPatterns:!Gy(e),noPersonalInfo:!Ky()},r=Object.values(n).filter(Boolean).length,o=Yy(e);let i=r;o>50&&(i+=1),e.length>12&&(i+=1);let s;i<=3?s="weak":i<=4?s="fair":i<=5?s="good":i<=6?s="strong":s="excellent";const l=[];return n.minLength||l.push(`Mindestens ${bc} Zeichen verwenden`),n.hasUppercase||l.push("Großbuchstaben hinzufügen (A-Z)"),n.hasLowercase||l.push("Kleinbuchstaben hinzufügen (a-z)"),n.hasNumbers||l.push("Zahlen hinzufügen (0-9)"),n.hasSpecialChars||l.push("Sonderzeichen hinzufügen (!@#$%^&*)"),n.noCommonPatterns||l.push("Vermeiden Sie häufige Muster oder Wörter"),n.noPersonalInfo||l.push("Vermeiden Sie persönliche Informationen"),e.length<12&&l.push("Längere Passwörter sind sicherer (12+ Zeichen)"),{strength:s,score:Math.min(i,7),requirements:n,suggestions:l,isValid:r>=5&&n.minLength,entropy:o}},qy=(e,t)=>t.trim()?e!==t?{field:"confirmPassword",message:"Passwörter stimmen nicht überein"}:null:{field:"confirmPassword",message:"Passwort-Bestätigung ist erforderlich"},Xy=(e,t)=>{const n=[];return e.trim()||n.push({field:"emailOrName",message:"E-Mail oder Name ist erforderlich"}),t.trim()||n.push({field:"password",message:"Passwort ist erforderlich"}),n},Jy=(e,t,n,r)=>{const o=[],i=Ip(e);i&&o.push(i);const s=Op(t);s&&o.push(s),bp(n).strength==="weak"&&o.push({field:"password",message:"Passwort ist zu schwach. Bitte folgen Sie den Empfehlungen."});const a=qy(n,r);return a&&o.push(a),o},tw=()=>{const e={},t={},n=f=>{e[f]=!0},r=f=>{t[f.field]=f},o=f=>{t[f]=null},i=f=>e[f]&&t[f]||null,s=()=>Object.values(t).some(f=>f!==null);return{errors:t,touched:e,setError:r,clearError:o,setTouched:n,getFieldError:i,hasErrors:s,reset:()=>{Object.keys(e).forEach(f=>delete e[f]),Object.keys(t).forEach(f=>delete t[f])},validateField:(f,d)=>{let c=null;switch(f){case"email":c=Op(d);break;case"name":c=Ip(d);break;case"password":{bp(d).isValid||(c={field:"password",message:"Passwort erfüllt nicht die Sicherheitsanforderungen"});break}case"emailOrName":d.trim()||(c={field:"emailOrName",message:"E-Mail oder Name ist erforderlich"});break}return c?r(c):o(f),c},validateAll:()=>!s()}},Zy=()=>{const[e,t]=v.useState(null),[n,r]=v.useState(!0),[o,i]=v.useState(null),[s,l]=v.useState([]),[a,u]=v.useState(null),f=(E,I,R,U,L=!0)=>({type:E,message:I,field:R,code:U,timestamp:new Date,retryable:L}),d=v.useCallback(()=>{i(null)},[]),c=e!==null;v.useEffect(()=>{console.log("Initial load, checking for stored users and current user..."),l(Tc),localStorage.setItem("users",JSON.stringify(Tc));const E=localStorage.getItem("currentUser"),I=localStorage.getItem("rememberMe")==="true",R=localStorage.getItem("sessionExpiry");if(E)try{const U=JSON.parse(E);if(R){const L=new Date(R);new Date<L?(t(U),u(L)):(localStorage.removeItem("currentUser"),localStorage.removeItem("sessionExpiry"),localStorage.removeItem("rememberMe"))}else if(I){t(U);const L=new Date;L.setDate(L.getDate()+30),u(L),localStorage.setItem("sessionExpiry",L.toISOString())}}catch(U){console.error("Error parsing stored user:",U),localStorage.removeItem("currentUser"),localStorage.removeItem("sessionExpiry"),localStorage.removeItem("rememberMe")}r(!1)},[]);const y=async(E,I,R=!1)=>{r(!0),i(null);try{const U=Xy(E,I);if(U.length>0){const H=U[0],ne=f("validation",H.message,H.field);return i(ne),{success:!1,error:ne}}await new Promise(H=>setTimeout(H,150)),console.log("Attempting to log in with:",E,"password length:",I.length),console.log("Available users:",s.map(H=>({email:H.email,name:H.name})));const L=s.find(H=>(H.email===E||H.name===E)&&H.password===I);if(L){console.log("User found:",L.name,L.role);const{password:H,...ne}=L;t(ne);const de=new Date;return R?de.setDate(de.getDate()+30):de.setHours(de.getHours()+24),u(de),localStorage.setItem("currentUser",JSON.stringify(ne)),localStorage.setItem("rememberMe",R.toString()),localStorage.setItem("sessionExpiry",de.toISOString()),Ae.success(`Willkommen zurück, ${L.name}!`),{success:!0,user:ne}}else{console.log("User not found or password incorrect");const H=f("authentication","Ungültige Anmeldeinformationen",void 0,"INVALID_CREDENTIALS");return i(H),Ae.error("Anmeldung fehlgeschlagen. Ungültiger Benutzername oder Passwort."),{success:!1,error:H}}}catch(U){console.error("Login error:",U);const L=f("network","Anmeldefehler. Bitte versuchen Sie es erneut.",void 0,"NETWORK_ERROR");return i(L),Ae.error("Ein Fehler ist aufgetreten."),{success:!1,error:L}}finally{r(!1)}},S=async E=>{r(!0),i(null);try{const{name:I,email:R,password:U,confirmPassword:L}=E,H=Jy(I,R,U,L);if(H.length>0){const B=H[0],F=f("validation",B.message,B.field);return i(F),{success:!1,error:F}}if(await new Promise(B=>setTimeout(B,200)),s.find(B=>B.email===R)){const B=f("validation","Diese E-Mail-Adresse wird bereits verwendet","email","EMAIL_EXISTS");return i(B),Ae.error("Diese E-Mail-Adresse existiert bereits."),{success:!1,error:B}}const de={id:Be(),name:I.trim(),email:R.toLowerCase().trim(),role:"berater",teamId:"team1",password:U},pe=[...s,de];l(pe),localStorage.setItem("users",JSON.stringify(pe));const{password:Le,..._}=de;t(_);const M=new Date;return M.setHours(M.getHours()+24),u(M),localStorage.setItem("currentUser",JSON.stringify(_)),localStorage.setItem("rememberMe","false"),localStorage.setItem("sessionExpiry",M.toISOString()),Ae.success(`Willkommen, ${de.name}! Registrierung erfolgreich.`),{success:!0,user:_}}catch(I){console.error("Registration error:",I);const R=f("server","Registrierungsfehler. Bitte versuchen Sie es erneut.",void 0,"SERVER_ERROR");return i(R),Ae.error("Ein Fehler ist aufgetreten."),{success:!1,error:R}}finally{r(!1)}},g=async()=>(t(null),i(null),u(null),localStorage.removeItem("currentUser"),localStorage.removeItem("sessionExpiry"),localStorage.removeItem("rememberMe"),Ae.info("Abmeldung erfolgreich."),Promise.resolve()),k=async()=>{try{const E=localStorage.getItem("currentUser"),I=localStorage.getItem("sessionExpiry"),R=localStorage.getItem("rememberMe")==="true";if(!E||!I)return!1;const U=new Date(I),L=new Date;if(L>U)return await g(),!1;const H=new Date(L.getTime()+60*60*1e3);if(U<H){const ne=new Date;R?ne.setDate(ne.getDate()+30):ne.setHours(ne.getHours()+24),u(ne),localStorage.setItem("sessionExpiry",ne.toISOString())}return!0}catch(E){return console.error("Session refresh error:",E),!1}},h=async E=>{try{l(s.map(R=>R.id===E.id?E:R)),e&&e.id===E.id&&(t(E),localStorage.setItem("currentUser",JSON.stringify(E)));const I=s.map(R=>R.id===E.id?E:R);localStorage.setItem("users",JSON.stringify(I))}catch(I){throw console.error("Error updating user:",I),I}},p=async E=>{try{const I={...E,id:Be()},R=[...s,I];return l(R),localStorage.setItem("users",JSON.stringify(R)),I}catch(I){throw console.error("Error creating user:",I),I}},m=async(E,I,R)=>{try{if(I==="soft"){const U=s.map(L=>L.id===E?{...L,isActive:!1,deletedAt:new Date().toISOString(),deleteReason:R}:L);l(U),localStorage.setItem("users",JSON.stringify(U))}else{const U=s.filter(L=>L.id!==E);l(U),localStorage.setItem("users",JSON.stringify(U))}Ae.success(I==="soft"?"Benutzer wurde deaktiviert":"Benutzer wurde gelöscht")}catch(U){throw console.error("Error deleting user:",U),Ae.error("Fehler beim Löschen des Benutzers"),U}},x=async(E,I,R)=>{try{const U=R&&R!=="permanent"?new Date(Date.now()+O(R)).toISOString():null,L=s.map(H=>H.id===E?{...H,isBlocked:!0,blockReason:I,blockedAt:new Date().toISOString(),blockUntil:U}:H);l(L),localStorage.setItem("users",JSON.stringify(L)),Ae.success("Benutzer wurde blockiert")}catch(U){throw console.error("Error blocking user:",U),Ae.error("Fehler beim Blockieren des Benutzers"),U}},P=async(E,I)=>{try{const R=s.map(U=>U.id===E?{...U,isBlocked:!1,blockReason:null,blockedAt:null,blockUntil:null,unblockReason:I,unblockedAt:new Date().toISOString()}:U);l(R),localStorage.setItem("users",JSON.stringify(R)),Ae.success("Benutzer wurde entsperrt")}catch(R){throw console.error("Error unblocking user:",R),Ae.error("Fehler beim Entsperren des Benutzers"),R}},O=E=>{switch(E){case"temporary_1h":return 60*60*1e3;case"temporary_24h":return 24*60*60*1e3;case"temporary_7d":return 7*24*60*60*1e3;case"temporary_30d":return 30*24*60*60*1e3;default:return 0}};return{user:e,login:y,register:S,logout:g,isLoading:n,error:o,users:s,updateUser:h,createUser:p,deleteUser:m,blockUser:x,unblockUser:P,loginWithGoogle:async()=>{r(!0),i(null);try{await new Promise(R=>setTimeout(R,800));const E={id:Be(),name:"Google User",email:`user_${Math.floor(Math.random()*1e4)}@gmail.com`,role:"berater",teamId:"team1"};s.some(R=>R.email===E.email)||l(R=>[...R,E]);const I=new Date;return I.setHours(I.getHours()+24),u(I),t(E),localStorage.setItem("currentUser",JSON.stringify(E)),localStorage.setItem("sessionExpiry",I.toISOString()),localStorage.setItem("rememberMe","false"),Ae.success(`Willkommen, ${E.name}!`),{success:!0,user:E}}catch(E){console.error("Google login error:",E);const I=f("authentication","Google-Anmeldung fehlgeschlagen",void 0,"GOOGLE_AUTH_ERROR");return i(I),Ae.error("Ein Fehler ist bei der Google-Anmeldung aufgetreten."),{success:!1,error:I}}finally{r(!1)}},clearError:d,isAuthenticated:c,sessionExpiry:a,refreshSession:k}},Dp=v.createContext(void 0),e0=({children:e})=>{const t=Zy();return w.jsx(Dp.Provider,{value:t,children:e})},jp=()=>{const e=v.useContext(Dp);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},t0=[{id:"a1",zipCode:"80331",city:"München",street:"Marienplatz"},{id:"a2",zipCode:"10117",city:"Berlin",street:"Unter den Linden"}],n0=[{id:"h1",addressId:"a1",houseNumber:"1",type:"EFH",latitude:48.1351,longitude:11.582,createdAt:new Date().toISOString(),createdBy:"1"},{id:"h2",addressId:"a1",houseNumber:"2",type:"MFH",latitude:48.1355,longitude:11.5825,createdAt:new Date().toISOString(),createdBy:"1"}],Nn=e=>{const t=new Date(e),n=t.getDay(),r=t.getHours(),o=n===0||n===6,i=t.getMonth();let s;return i>=2&&i<=4?s="spring":i>=5&&i<=7?s="summer":i>=8&&i<=10?s="autumn":s="winter",{dayOfWeek:n,hourOfDay:r,isWeekend:o,seasonalContext:s}},zp=(e,t,n)=>{const r=n.filter(u=>u.addressId===e),o=t.filter(u=>r.some(f=>f.id===u.houseId));if(o.length<2)return[];const i=[],s=o.filter(u=>u.status==="N/A"),l=o.filter(u=>u.status!=="N/A");if(s.length>=2){const u=s.map(d=>Nn(d.timestamp).hourOfDay),f=l.map(d=>Nn(d.timestamp).hourOfDay);i.push({id:Be(),addressId:e,patternType:"time_of_day",patternData:{failedTimes:u,successfulTimes:f,confidence:Math.min(s.length/5,1),dataPoints:o.length},lastUpdated:new Date().toISOString(),createdAt:new Date().toISOString()})}if(s.length>=2){const u=s.map(d=>Nn(d.timestamp).dayOfWeek),f=l.map(d=>Nn(d.timestamp).dayOfWeek);i.push({id:Be(),addressId:e,patternType:"day_of_week",patternData:{failedDays:u,successfulDays:f,confidence:Math.min(s.length/4,1),dataPoints:o.length},lastUpdated:new Date().toISOString(),createdAt:new Date().toISOString()})}const a=s.filter(u=>{const f=Nn(u.timestamp).hourOfDay;return f>=9&&f<=17});return a.length>=3&&a.length/s.length>.7&&i.push({id:Be(),addressId:e,patternType:"shift_work",patternData:{failedTimes:a.map(u=>Nn(u.timestamp).hourOfDay),confidence:Math.min(a.length/5,1),dataPoints:o.length},lastUpdated:new Date().toISOString(),createdAt:new Date().toISOString()}),i},Lp=(e,t,n)=>{const r=[];return t.forEach(o=>{switch(o.patternType){case"time_of_day":if(o.patternData.failedTimes&&o.patternData.failedTimes.length>=2){const i=r0(o.patternData.failedTimes),s=o0(i);r.push({id:Be(),addressId:e,recommendationType:"optimal_time",recommendation:`Versuchen Sie es um ${s.join(" oder ")} Uhr (basierend auf ${o.patternData.failedTimes.length} fehlgeschlagenen Versuchen)`,confidence:o.patternData.confidence,priority:o.patternData.confidence>.7?"high":"medium",basedOnVisits:n,suggestedTimeSlots:s.map(l=>({day:-1,startHour:l,endHour:l+1,confidence:o.patternData.confidence})),createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()}),r.push({id:Be(),addressId:e,recommendationType:"avoid_time",recommendation:`Vermeiden Sie: ${i.join(", ")} Uhr (${o.patternData.failedTimes.length} fehlgeschlagene Versuche)`,confidence:o.patternData.confidence,priority:"medium",basedOnVisits:n,createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()})}break;case"day_of_week":if(o.patternData.failedDays&&o.patternData.failedDays.length>=2){const i=i0(o.patternData.failedDays),s=s0(i);r.push({id:Be(),addressId:e,recommendationType:"day_preference",recommendation:`Beste Erfolgschance: ${l0(s).join(" oder ")}`,confidence:o.patternData.confidence,priority:o.patternData.confidence>.6?"high":"medium",basedOnVisits:n,createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()})}break;case"shift_work":r.push({id:Be(),addressId:e,recommendationType:"general_advice",recommendation:"Vermeiden Sie: Werktags 9-17 Uhr (wahrscheinlich Arbeitszeiten)",confidence:o.patternData.confidence,priority:"high",basedOnVisits:n,suggestedTimeSlots:[{day:1,startHour:18,endHour:20,confidence:o.patternData.confidence},{day:2,startHour:18,endHour:20,confidence:o.patternData.confidence},{day:3,startHour:18,endHour:20,confidence:o.patternData.confidence},{day:4,startHour:18,endHour:20,confidence:o.patternData.confidence},{day:5,startHour:18,endHour:20,confidence:o.patternData.confidence},{day:6,startHour:10,endHour:18,confidence:o.patternData.confidence},{day:0,startHour:10,endHour:18,confidence:o.patternData.confidence}],createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()});break}}),r},r0=e=>{const t={};return e.forEach(n=>{t[n]=(t[n]||0)+1}),Object.entries(t).sort(([,n],[,r])=>r-n).slice(0,3).map(([n])=>parseInt(n))},o0=e=>{const n=Array.from({length:24},(o,i)=>i).filter(o=>!e.includes(o)),r=n.filter(o=>o>=18&&o<=20||o>=8&&o<=10);return r.length>0?r.slice(0,2):n.slice(0,2)},i0=e=>{const t={};return e.forEach(n=>{t[n]=(t[n]||0)+1}),Object.entries(t).sort(([,n],[,r])=>r-n).slice(0,2).map(([n])=>parseInt(n))},s0=e=>{const n=[0,1,2,3,4,5,6].filter(o=>!e.includes(o)),r=n.filter(o=>o===0||o===5||o===6);return r.length>0?r:n.slice(0,2)},l0=e=>{const t=["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"];return e.map(n=>t[n])},a0=(e,t,n)=>{const r=n.filter(u=>u.addressId===e),o=t.filter(u=>r.some(f=>f.id===u.houseId)),i=o.filter(u=>u.status==="N/A"),s=o.filter(u=>u.status!=="N/A"),l=zp(e,t,n),a=Lp(e,l,i.length);return{addressId:e,totalVisits:o.length,failedVisits:i.length,successfulVisits:s.length,lastVisitDate:o.length>0?o.sort((u,f)=>new Date(f.timestamp).getTime()-new Date(u.timestamp).getTime())[0].timestamp:"",patterns:l,recommendations:a}},u0=[],c0=[],d0=[],f0=[],p0=[],Qe={save:(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t))}catch(n){console.error("Error saving to localStorage",n)}},load:(e,t)=>{try{const n=localStorage.getItem(e);return n?JSON.parse(n):t}catch(n){return console.error("Error loading from localStorage",n),t}}},Mp=v.createContext(void 0),h0=({children:e})=>{const[t,n]=v.useState(()=>Qe.load("addresses",t0)),[r,o]=v.useState(()=>Qe.load("houses",n0)),[i,s]=v.useState(()=>Qe.load("visits",u0)),[l,a]=v.useState(()=>Qe.load("doors",c0)),[u,f]=v.useState(()=>Qe.load("products",d0)),[d,c]=v.useState(()=>Qe.load("visitPatterns",f0)),[y,S]=v.useState(()=>Qe.load("visitRecommendations",p0)),{user:g}=jp();v.useEffect(()=>{Qe.save("addresses",t)},[t]),v.useEffect(()=>{Qe.save("houses",r)},[r]),v.useEffect(()=>{Qe.save("visits",i)},[i]),v.useEffect(()=>{Qe.save("doors",l)},[l]),v.useEffect(()=>{Qe.save("products",u)},[u]),v.useEffect(()=>{Qe.save("visitPatterns",d)},[d]),v.useEffect(()=>{Qe.save("visitRecommendations",y)},[y]);const k=T=>{const b={id:Be(),...T};return n(D=>[...D,b]),b},h=T=>t.find(b=>b.id===T),p=T=>{const b={id:Be(),...T,createdAt:new Date().toISOString(),createdBy:(g==null?void 0:g.id)||"unknown"};return o(D=>[...D,b]),b},m=T=>r.find(b=>b.id===T),x=T=>r.filter(b=>b.addressId===T),P=T=>{const b=Nn(T.timestamp),D={id:Be(),...T,status:"N/A",userId:(g==null?void 0:g.id)||"unknown",...b};return s(V=>[...V,D]),D},O=T=>i.find(b=>b.id===T),z=T=>i.filter(b=>b.houseId===T),E=()=>{const T=new Date().toISOString().split("T")[0];return i.filter(D=>D.timestamp.startsWith(T)).map(D=>r.find(V=>V.id===D.houseId)).filter(Boolean)},I=()=>{const T=new Date().toISOString().split("T")[0];return i.filter(b=>b.timestamp.startsWith(T))},R=T=>{const b={id:Be(),...T};return a(D=>[...D,b]),b},U=T=>l.filter(b=>b.visitId===T),L=T=>{const b={id:Be(),...T,timestamp:new Date().toISOString(),userId:(g==null?void 0:g.id)||"unknown"};return f(D=>[...D,b]),b},H=T=>u.filter(b=>b.doorId===T),ne=(T,b,D,V,K)=>{s(Y=>Y.map(q=>{if(q.id===T){const Me={...q,status:b,appointmentDate:D,appointmentTime:V,comment:K};if(b==="N/A"){const Mo=r.find(Ar=>Ar.id===q.houseId);Mo&&setTimeout(()=>_(Mo.addressId),100)}return Me}return q}))},de=(T,b,D,V,K)=>{a(Y=>Y.map(q=>q.id===T?{...q,status:b,appointmentDate:D,appointmentTime:V,comment:K}:q))},pe=()=>{const T=new Date,b=[];return i.forEach(D=>{if(D.status==="Angetroffen → Termin"&&D.appointmentDate&&D.appointmentTime){const V=new Date(`${D.appointmentDate}T${D.appointmentTime}`);if(V>T){const K=r.find(q=>q.id===D.houseId),Y=K?t.find(q=>q.id===K.addressId):null;b.push({id:D.id,type:"visit",date:D.appointmentDate,time:D.appointmentTime,address:Y?`${Y.street} ${K==null?void 0:K.houseNumber}, ${Y.city}`:"Unbekannte Adresse",dateTime:V})}}}),l.forEach(D=>{if(D.status==="Angetroffen → Termin"&&D.appointmentDate&&D.appointmentTime){const V=new Date(`${D.appointmentDate}T${D.appointmentTime}`);if(V>T){const K=i.find(Me=>Me.id===D.visitId),Y=K?r.find(Me=>Me.id===K.houseId):null,q=Y?t.find(Me=>Me.id===Y.addressId):null;b.push({id:D.id,type:"door",date:D.appointmentDate,time:D.appointmentTime,address:q?`${q.street} ${Y==null?void 0:Y.houseNumber} - ${D.name}`:"Unbekannte Adresse",dateTime:V})}}}),b.sort((D,V)=>D.dateTime.getTime()-V.dateTime.getTime())},Le=T=>{const b=[];return i.forEach(D=>{if(D.status==="Angetroffen → Termin"&&D.appointmentDate===T){const V=r.find(Y=>Y.id===D.houseId),K=V?t.find(Y=>Y.id===V.addressId):null;b.push({id:D.id,type:"visit",time:D.appointmentTime||"00:00",address:K?`${K.street} ${V==null?void 0:V.houseNumber}`:"Unbekannte Adresse"})}}),l.forEach(D=>{if(D.status==="Angetroffen → Termin"&&D.appointmentDate===T){const V=i.find(q=>q.id===D.visitId),K=V?r.find(q=>q.id===V.houseId):null,Y=K?t.find(q=>q.id===K.addressId):null;b.push({id:D.id,type:"door",time:D.appointmentTime||"00:00",address:Y?`${Y.street} ${K==null?void 0:K.houseNumber} - ${D.name}`:"Unbekannte Adresse"})}}),b.sort((D,V)=>D.time.localeCompare(V.time))},_=T=>{const b=zp(T,i,r),D=i.filter(K=>{const Y=r.find(q=>q.id===K.houseId);return(Y==null?void 0:Y.addressId)===T&&K.status==="N/A"});c(K=>[...K.filter(q=>q.addressId!==T),...b]);const V=Lp(T,b,D.length);S(K=>[...K.filter(q=>q.addressId!==T),...V])},M=T=>a0(T,i,r),B=T=>y.filter(b=>b.addressId===T),F=()=>{const T=new Set;return i.forEach(b=>{if(b.status==="N/A"){const D=r.find(V=>V.id===b.houseId);D&&T.add(D.addressId)}}),Array.from(T).map(b=>{const D=t.find(Y=>Y.id===b),V=B(b),K=i.filter(Y=>{const q=r.find(Me=>Me.id===Y.houseId);return(q==null?void 0:q.addressId)===b&&Y.status==="N/A"}).length;return{address:D,recommendations:V,failedVisitCount:K}}).filter(b=>b.address&&b.failedVisitCount>0)},$=()=>y.filter(T=>T.priority==="high").sort((T,b)=>b.confidence-T.confidence);return w.jsx(Mp.Provider,{value:{addresses:t,houses:r,visits:i,doors:l,products:u,visitPatterns:d,visitRecommendations:y,addAddress:k,getAddressById:h,addHouse:p,getHouseById:m,getHousesByAddress:x,addVisit:P,getVisit:O,getVisitsByHouse:z,getTodaysHouses:E,getTodaysVisits:I,updateVisitStatus:ne,addDoor:R,getDoorsByVisit:U,updateDoorStatus:de,addProduct:L,getProductsByDoor:H,getUpcomingAppointments:pe,getAppointmentsByDate:Le,getAddressVisitHistory:M,getVisitRecommendations:B,getAddressesRequiringReturnVisits:F,updateVisitPatterns:_,getHighPriorityRecommendations:$},children:e})},nw=()=>{const e=v.useContext(Mp);if(!e)throw new Error("useData must be used within a DataProvider");return e},m0=v.createContext(void 0),g0=({children:e})=>{const t="light",n="light";v.useEffect(()=>{const o=window.document.documentElement;o.classList.remove("light","dark"),o.classList.add("light"),console.log("Light theme applied")},[]);const r=()=>{console.log("Theme setting disabled - application is locked to light theme")};return w.jsx(m0.Provider,{value:{theme:t,setTheme:r,resolvedTheme:n},children:e})},Hs={language:"de",timezone:"Europe/Berlin",defaultView:"list",mapboxToken:"",defaultZoom:"12",autoSave:!0,offlineMode:!1,emailNotifications:!0,pushNotifications:!0},Ap=v.createContext(void 0),v0=({children:e})=>{const[t,n]=v.useState(()=>{const i=localStorage.getItem("app-settings");return i?{...Hs,...JSON.parse(i)}:Hs});v.useEffect(()=>{localStorage.setItem("app-settings",JSON.stringify(t))},[t]);const r=i=>{n(s=>({...s,...i}))},o=()=>{n(Hs),localStorage.removeItem("app-settings")};return w.jsx(Ap.Provider,{value:{settings:t,updateSettings:r,resetSettings:o},children:e})},rw=()=>{const e=v.useContext(Ap);if(e===void 0)throw new Error("useSettings must be used within a SettingsProvider");return e},y0=({...e})=>w.jsx(Fy,{theme:"light",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e}),w0={berater:["user.read","team.read","area.read","data.export"],mentor:["user.read","user.write","team.read","area.read","data.export","audit.read"],teamleiter:["user.read","user.write","team.read","team.write","team.assign_members","area.read","data.export","audit.read"],gebietsmanager:["user.read","user.write","team.read","team.write","team.assign_members","area.read","area.write","area.assign_teams","data.export","data.import","audit.read"],admin:["user.read","user.write","user.delete","user.bulk_assign","user.block","user.unblock","team.read","team.write","team.delete","team.assign_members","area.read","area.write","area.delete","area.assign_teams","audit.read","audit.write","audit.export","system.config","system.backup","system.health","data.export","data.import","data.backup"]},Ga=e=>e.isMultiRole&&e.roles&&e.roles.length>0?e.roles:[e.role],$l=(e,t)=>Ga(e).includes(t),x0=(e,t)=>{const n=Ga(e);return t.some(r=>n.includes(r))},S0=e=>e.isMultiRole===!0&&e.roles&&e.roles.length>1,ow=e=>S0(e)?e.roles||[e.role]:[e.role],iw=e=>$l(e,"admin"),sw=e=>({berater:"Berater",mentor:"Mentor",teamleiter:"Teamleiter",gebietsmanager:"Gebietsmanager",admin:"Administrator"})[e]||e,E0=(e,t)=>t==="admin"?$l(e,"admin"):!!$l(e,"admin"),lw=e=>e.includes("admin")?!0:e.length>0,k0=e=>{const t=Ga(e),n=new Set;return t.forEach(r=>{var o;(o=w0[r])==null||o.forEach(i=>{n.add(i)})}),Array.from(n)},C0=(e,t)=>k0(e).includes(t),aw=(e,t)=>{const n=[];return C0(e,"user.bulk_assign")||n.push("Sie haben keine Berechtigung für Massenzuweisungen"),E0(e,t.targetRole)||n.push(`Sie können die Rolle "${t.targetRole}" nicht zuweisen`),(!t.userIds||t.userIds.length===0)&&n.push("Mindestens ein Benutzer muss ausgewählt werden"),t.expiresAt&&new Date(t.expiresAt)<=new Date&&n.push("Das Ablaufdatum muss in der Zukunft liegen"),{isValid:n.length===0,errors:n}},re=({children:e,allowedRoles:t})=>{const{user:n,isLoading:r}=jp(),o=Lr();return r?w.jsx("div",{className:"flex items-center justify-center h-screen",children:w.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"})}):n?t&&t.length>0&&!x0(n,t)?w.jsx("div",{className:"flex items-center justify-center h-screen",children:w.jsxs("div",{className:"text-center",children:[w.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Zugriff verweigert"}),w.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})]})}):w.jsx(w.Fragment,{children:e}):w.jsx(Ep,{to:"/login",state:{from:o},replace:!0})};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Fp=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var _0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=v.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...l},a)=>v.createElement("svg",{ref:a,..._0,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Fp("lucide",o),...l},[...s.map(([u,f])=>v.createElement(u,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ka=(e,t)=>{const n=v.forwardRef(({className:r,...o},i)=>v.createElement(N0,{ref:i,iconNode:t,className:Fp(`lucide-${P0(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R0=Ka("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T0=Ka("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=Ka("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function I0(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Up(...e){return t=>e.forEach(n=>I0(n,t))}function uw(...e){return v.useCallback(Up(...e),e)}var Vp=v.forwardRef((e,t)=>{const{children:n,...r}=e,o=v.Children.toArray(n),i=o.find(D0);if(i){const s=i.props.children,l=o.map(a=>a===i?v.Children.count(s)>1?v.Children.only(null):v.isValidElement(s)?s.props.children:null:a);return w.jsx(Hl,{...r,ref:t,children:v.isValidElement(s)?v.cloneElement(s,void 0,l):null})}return w.jsx(Hl,{...r,ref:t,children:n})});Vp.displayName="Slot";var Hl=v.forwardRef((e,t)=>{const{children:n,...r}=e;if(v.isValidElement(n)){const o=z0(n);return v.cloneElement(n,{...j0(r,n.props),ref:t?Up(t,o):o})}return v.Children.count(n)>1?v.Children.only(null):null});Hl.displayName="SlotClone";var b0=({children:e})=>w.jsx(w.Fragment,{children:e});function D0(e){return v.isValidElement(e)&&e.type===b0}function j0(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function z0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Bp(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Bp(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function $p(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Bp(e))&&(r&&(r+=" "),r+=t);return r}const Dc=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,jc=$p,L0=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return jc(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],d=i==null?void 0:i[u];if(f===null)return null;const c=Dc(f)||Dc(d);return o[u][c]}),l=n&&Object.entries(n).reduce((u,f)=>{let[d,c]=f;return c===void 0||(u[d]=c),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:d,className:c,...y}=f;return Object.entries(y).every(S=>{let[g,k]=S;return Array.isArray(k)?k.includes({...i,...l}[g]):{...i,...l}[g]===k})?[...u,d,c]:u},[]);return jc(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)},Ya="-",M0=e=>{const t=F0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Ya);return l[0]===""&&l.length!==1&&l.shift(),Hp(l,t)||A0(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},Hp=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Hp(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Ya);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},zc=/^\[(.+)\]$/,A0=e=>{if(zc.test(e)){const t=zc.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},F0=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return V0(Object.entries(e.classGroups),n).forEach(([i,s])=>{Wl(s,r,i,t)}),r},Wl=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Lc(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(U0(o)){Wl(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Wl(s,Lc(t,i),n,r)})})},Lc=(e,t)=>{let n=e;return t.split(Ya).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},U0=e=>e.isThemeGetter,V0=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,B0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},Wp="!",$0=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,f=0,d;for(let k=0;k<l.length;k++){let h=l[k];if(u===0){if(h===o&&(r||l.slice(k,k+i)===t)){a.push(l.slice(f,k)),f=k+i;continue}if(h==="/"){d=k;continue}}h==="["?u++:h==="]"&&u--}const c=a.length===0?l:l.substring(f),y=c.startsWith(Wp),S=y?c.substring(1):c,g=d&&d>f?d-f:void 0;return{modifiers:a,hasImportantModifier:y,baseClassName:S,maybePostfixModifierPosition:g}};return n?l=>n({className:l,parseClassName:s}):s},H0=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},W0=e=>({cache:B0(e.cacheSize),parseClassName:$0(e),...M0(e)}),Q0=/\s+/,G0=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(Q0);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:f,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:y}=n(u);let S=!!y,g=r(S?c.substring(0,y):c);if(!g){if(!S){l=u+(l.length>0?" "+l:l);continue}if(g=r(c),!g){l=u+(l.length>0?" "+l:l);continue}S=!1}const k=H0(f).join(":"),h=d?k+Wp:k,p=h+g;if(i.includes(p))continue;i.push(p);const m=o(g,S);for(let x=0;x<m.length;++x){const P=m[x];i.push(h+P)}l=u+(l.length>0?" "+l:l)}return l};function K0(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Qp(t))&&(r&&(r+=" "),r+=n);return r}const Qp=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Qp(e[r]))&&(n&&(n+=" "),n+=t);return n};function Y0(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((f,d)=>d(f),e());return n=W0(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const f=G0(a,n);return o(a,f),f}return function(){return i(K0.apply(null,arguments))}}const ae=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Gp=/^\[(?:([a-z-]+):)?(.+)\]$/i,q0=/^\d+\/\d+$/,X0=new Set(["px","full","screen"]),J0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Z0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,e1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,t1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,n1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,jt=e=>hr(e)||X0.has(e)||q0.test(e),Kt=e=>Mr(e,"length",c1),hr=e=>!!e&&!Number.isNaN(Number(e)),Ws=e=>Mr(e,"number",hr),Gr=e=>!!e&&Number.isInteger(Number(e)),r1=e=>e.endsWith("%")&&hr(e.slice(0,-1)),Q=e=>Gp.test(e),Yt=e=>J0.test(e),o1=new Set(["length","size","percentage"]),i1=e=>Mr(e,o1,Kp),s1=e=>Mr(e,"position",Kp),l1=new Set(["image","url"]),a1=e=>Mr(e,l1,f1),u1=e=>Mr(e,"",d1),Kr=()=>!0,Mr=(e,t,n)=>{const r=Gp.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},c1=e=>Z0.test(e)&&!e1.test(e),Kp=()=>!1,d1=e=>t1.test(e),f1=e=>n1.test(e),p1=()=>{const e=ae("colors"),t=ae("spacing"),n=ae("blur"),r=ae("brightness"),o=ae("borderColor"),i=ae("borderRadius"),s=ae("borderSpacing"),l=ae("borderWidth"),a=ae("contrast"),u=ae("grayscale"),f=ae("hueRotate"),d=ae("invert"),c=ae("gap"),y=ae("gradientColorStops"),S=ae("gradientColorStopPositions"),g=ae("inset"),k=ae("margin"),h=ae("opacity"),p=ae("padding"),m=ae("saturate"),x=ae("scale"),P=ae("sepia"),O=ae("skew"),z=ae("space"),E=ae("translate"),I=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],U=()=>["auto",Q,t],L=()=>[Q,t],H=()=>["",jt,Kt],ne=()=>["auto",hr,Q],de=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],pe=()=>["solid","dashed","dotted","double","none"],Le=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],_=()=>["start","end","center","between","around","evenly","stretch"],M=()=>["","0",Q],B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>[hr,Q];return{cacheSize:500,separator:":",theme:{colors:[Kr],spacing:[jt,Kt],blur:["none","",Yt,Q],brightness:F(),borderColor:[e],borderRadius:["none","","full",Yt,Q],borderSpacing:L(),borderWidth:H(),contrast:F(),grayscale:M(),hueRotate:F(),invert:M(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[r1,Kt],inset:U(),margin:U(),opacity:F(),padding:L(),saturate:F(),scale:F(),sepia:M(),skew:F(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",Q]}],container:["container"],columns:[{columns:[Yt]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...de(),Q]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:I()}],"overscroll-x":[{"overscroll-x":I()}],"overscroll-y":[{"overscroll-y":I()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Gr,Q]}],basis:[{basis:U()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Q]}],grow:[{grow:M()}],shrink:[{shrink:M()}],order:[{order:["first","last","none",Gr,Q]}],"grid-cols":[{"grid-cols":[Kr]}],"col-start-end":[{col:["auto",{span:["full",Gr,Q]},Q]}],"col-start":[{"col-start":ne()}],"col-end":[{"col-end":ne()}],"grid-rows":[{"grid-rows":[Kr]}],"row-start-end":[{row:["auto",{span:[Gr,Q]},Q]}],"row-start":[{"row-start":ne()}],"row-end":[{"row-end":ne()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Q]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",..._()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",..._(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[..._(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[k]}],mx:[{mx:[k]}],my:[{my:[k]}],ms:[{ms:[k]}],me:[{me:[k]}],mt:[{mt:[k]}],mr:[{mr:[k]}],mb:[{mb:[k]}],ml:[{ml:[k]}],"space-x":[{"space-x":[z]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[z]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Q,t]}],"min-w":[{"min-w":[Q,t,"min","max","fit"]}],"max-w":[{"max-w":[Q,t,"none","full","min","max","fit","prose",{screen:[Yt]},Yt]}],h:[{h:[Q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Yt,Kt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ws]}],"font-family":[{font:[Kr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Q]}],"line-clamp":[{"line-clamp":["none",hr,Ws]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",jt,Q]}],"list-image":[{"list-image":["none",Q]}],"list-style-type":[{list:["none","disc","decimal",Q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",jt,Kt]}],"underline-offset":[{"underline-offset":["auto",jt,Q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...de(),s1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",i1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},a1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[S]}],"gradient-via-pos":[{via:[S]}],"gradient-to-pos":[{to:[S]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...pe(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:pe()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...pe()]}],"outline-offset":[{"outline-offset":[jt,Q]}],"outline-w":[{outline:[jt,Kt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[jt,Kt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Yt,u1]}],"shadow-color":[{shadow:[Kr]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...Le(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Le()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Yt,Q]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[d]}],saturate:[{saturate:[m]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Q]}],duration:[{duration:F()}],ease:[{ease:["linear","in","out","in-out",Q]}],delay:[{delay:F()}],animate:[{animate:["none","spin","ping","pulse","bounce",Q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Gr,Q]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[O]}],"skew-y":[{"skew-y":[O]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[jt,Kt,Ws]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},h1=Y0(p1);function Hn(...e){return h1($p(e))}const m1=L0("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-feedback",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-12 px-4 py-2 min-h-[48px]",sm:"h-10 rounded-md px-3 min-h-[44px]",lg:"h-14 rounded-md px-8 min-h-[56px]",icon:"h-12 w-12 min-h-[48px] min-w-[48px]"}},defaultVariants:{variant:"default",size:"default"}}),Ql=v.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?Vp:"button";return w.jsx(s,{className:Hn(m1({variant:t,size:n,className:e})),ref:i,...o})});Ql.displayName="Button";const Yp=v.forwardRef(({className:e,...t},n)=>w.jsx("div",{ref:n,className:Hn("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Yp.displayName="Card";const qp=v.forwardRef(({className:e,...t},n)=>w.jsx("div",{ref:n,className:Hn("flex flex-col space-y-1.5 p-6",e),...t}));qp.displayName="CardHeader";const Xp=v.forwardRef(({className:e,...t},n)=>w.jsx("h3",{ref:n,className:Hn("text-2xl font-semibold leading-none tracking-tight",e),...t}));Xp.displayName="CardTitle";const Jp=v.forwardRef(({className:e,...t},n)=>w.jsx("p",{ref:n,className:Hn("text-sm text-muted-foreground",e),...t}));Jp.displayName="CardDescription";const Zp=v.forwardRef(({className:e,...t},n)=>w.jsx("div",{ref:n,className:Hn("p-6 pt-0",e),...t}));Zp.displayName="CardContent";const g1=v.forwardRef(({className:e,...t},n)=>w.jsx("div",{ref:n,className:Hn("flex items-center p-6 pt-0",e),...t}));g1.displayName="CardFooter";class Mc extends v.Component{constructor(n){super(n);ps(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});ps(this,"handleGoHome",()=>{window.location.href="/"});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),console.group("Error Details"),console.error("Error:",n.message),console.error("Stack:",n.stack),console.error("Component Stack:",r.componentStack),console.groupEnd(),this.props.onError&&this.props.onError(n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:w.jsx("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:w.jsxs(Yp,{className:"w-full max-w-md",children:[w.jsxs(qp,{className:"text-center",children:[w.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:w.jsx(O0,{className:"h-6 w-6 text-red-600"})}),w.jsx(Xp,{className:"text-xl font-semibold text-gray-900",children:"Etwas ist schiefgelaufen"}),w.jsx(Jp,{className:"text-gray-600",children:"Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut."})]}),w.jsxs(Zp,{className:"space-y-4",children:[!1,w.jsxs("div",{className:"flex flex-col gap-2",children:[w.jsxs(Ql,{onClick:this.handleReset,className:"w-full",variant:"default",children:[w.jsx(T0,{className:"mr-2 h-4 w-4"}),"Erneut versuchen"]}),w.jsxs(Ql,{onClick:this.handleGoHome,className:"w-full",variant:"outline",children:[w.jsx(R0,{className:"mr-2 h-4 w-4"}),"Zur Startseite"]})]})]})]})}):this.props.children}}const Ac={MINIMUM:44,COMFORTABLE:48,LARGE:56,EXTRA_LARGE:64},v1=e=>{const t=e.getBoundingClientRect();return t.width>=Ac.MINIMUM&&t.height>=Ac.MINIMUM},si=e=>{const n=(e||document.body).querySelectorAll('button, input, select, textarea, [role="button"], [role="tab"], a, [tabindex]:not([tabindex="-1"]), .touch-target'),r=[];return n.forEach(o=>{var s;const i=o;if(!y1(i)&&!v1(i)){const l=i.getBoundingClientRect(),a={element:i,currentSize:{width:l.width,height:l.height},tagName:i.tagName,className:i.className,id:i.id,textContent:((s=i.textContent)==null?void 0:s.slice(0,50))||"",selector:w1(i)};r.push(a),console.warn("Touch target too small:",a)}}),r.length>0&&(console.warn(`Found ${r.length} touch target violations`),console.table(r.map(o=>({selector:o.selector,width:o.currentSize.width,height:o.currentSize.height,text:o.textContent})))),r},y1=e=>{const t=["[data-radix-tabs-content]","[data-radix-popover-content]","[data-radix-dialog-content]","[data-radix-select-content]","[data-radix-tooltip-content]","[data-radix-hover-card-content]","[data-radix-menubar-content]","[data-radix-dropdown-menu-content]","[data-radix-context-menu-content]"];for(const i of t)if(e.matches(i))return!0;const n=e.getBoundingClientRect();return!!(n.width===0&&n.height===0||e.offsetParent===null&&e.style.position!=="fixed"||e.id&&e.id.includes("radix-")&&e.id.includes("-content-")||e.tagName==="DIV"&&e.className.includes("ring-offset-background")&&e.className.includes("focus-visible:outline-none")&&e.className.includes("focus-visible:ring-2")||["animate-fade-in"].some(i=>e.className.includes(i))&&e.tagName==="DIV"&&(e.className.includes("p-0")||e.className.includes("m-0")))},w1=e=>{if(e.id)return`#${e.id}`;let t=e.tagName.toLowerCase();if(e.className){const r=e.className.split(" ").filter(o=>o.trim());r.length>0&&(t+="."+r.join("."))}const n=e.parentElement;if(n&&n!==document.body){const r=n.tagName.toLowerCase();if(n.className){const o=n.className.split(" ").filter(i=>i.trim()).slice(0,2);if(o.length>0)return`${r}.${o.join(".")} > ${t}`}return`${r} > ${t}`}return t},Fc=(e,t=250)=>{let n;return()=>{clearTimeout(n),n=setTimeout(e,t)}},x1=()=>{document.addEventListener("DOMContentLoaded",()=>{si()}),new MutationObserver(t=>{t.forEach(n=>{n.type==="childList"&&n.addedNodes.forEach(r=>{r.nodeType===Node.ELEMENT_NODE&&si(r)})})}).observe(document.body,{childList:!0,subtree:!0}),window.addEventListener("orientationchange",Fc(()=>{setTimeout(()=>{si()},500)})),window.addEventListener("resize",Fc(()=>{si()}))},S1=v.lazy(()=>oe(()=>import("./Index-BGwUgqRu.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22]))),E1=v.lazy(()=>oe(()=>import("./Login-BUsjEoap.js"),__vite__mapDeps([23,3,2,4,6,24,25,26,27,28,29,30,31,32,19,7,33,34]))),k1=v.lazy(()=>oe(()=>import("./EFHPage-DMM_tLtI.js"),__vite__mapDeps([35,1,2,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22]))),C1=v.lazy(()=>oe(()=>import("./MFHPage-8AEXcdx5.js"),__vite__mapDeps([36,1,2,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22]))),P1=v.lazy(()=>oe(()=>import("./MapPage-B69IH5bG.js"),__vite__mapDeps([37,1,2,21,38,25,7,30,22,39]))),_1=v.lazy(()=>oe(()=>import("./DailyViewPage-DvHskOIl.js"),__vite__mapDeps([40,1,2,41,12,26,4,31,27,42,14]))),N1=v.lazy(()=>oe(()=>import("./StatisticsPage-8qINX5Rj.js"),__vite__mapDeps([43,1,2,42,3,4,16,17,27,44,45]))),R1=v.lazy(()=>oe(()=>import("./TeamsStatisticsPage-Cd6sXFHc.js"),__vite__mapDeps([46,1,2,45]))),T1=v.lazy(()=>oe(()=>import("./BeraterStatisticsPage-Das5Pbh1.js"),__vite__mapDeps([47,1,2,48,45]))),O1=v.lazy(()=>oe(()=>import("./ProfilePage-DyzMFiGF.js"),__vite__mapDeps([49,1,2,6,33,50,51,4,25,31,7,44,27,52,53,3,12,29,54,55,56,57,58,59,42,60]))),I1=v.lazy(()=>oe(()=>import("./SettingsPage-B4Djqc5y.js"),__vite__mapDeps([61,1,2,3,4,6,51,25,31,7,24,15,55,32,28,29]))),b1=v.lazy(()=>oe(()=>import("./CalendarPage-SQS4x_Yq.js"),__vite__mapDeps([62,1,2,12,14]))),D1=v.lazy(()=>oe(()=>import("./UserManagementPage-l3IFNP6h.js"),__vite__mapDeps([63,1,2,57,55,53,12,29,64,50,20,60,51,4,25,31,7,15,6,33,14,24,65,66,39]))),j1=v.lazy(()=>oe(()=>import("./TeamManagementPage-DuP4jJt0.js"),__vite__mapDeps([67,1,2,15,6,68,51,4,25,31,7,53,12,24,57,55,65,64]))),z1=v.lazy(()=>oe(()=>import("./AreaManagementPage-DSL8ruvD.js"),__vite__mapDeps([69,1,2,15,6,68,53,57,55,65,64]))),L1=v.lazy(()=>oe(()=>import("./AdminDashboardPage-Bmw0DA-1.js"),__vite__mapDeps([70,1,2,12,3,4,15,51,25,31,7,38,6,68,71,14,58,27,19,72,24,65,64,42,59,60,57,55]))),M1=v.lazy(()=>oe(()=>import("./AdminTestPage-PYJHbxwC.js"),__vite__mapDeps([73,1,2,12,57,55,59,10,27,72]))),A1=v.lazy(()=>oe(()=>import("./TeamOverviewPage-BrHek2KD.js"),__vite__mapDeps([74,1,2,52,53,3,4,12,29,27]))),F1=v.lazy(()=>oe(()=>import("./TeamsOverviewPage-CcQZrY1e.js"),__vite__mapDeps([75,1,2,54,3,4,55,53,12,27,29]))),U1=v.lazy(()=>oe(()=>import("./AreaOverviewPage-cmvYm83d.js"),__vite__mapDeps([76,1,2,56,53]))),V1=v.lazy(()=>oe(()=>import("./VisitStatusPage-DUhk-biP.js"),__vite__mapDeps([77,1,2,8,6,9,10,11,12,13,14,15,16,17,18]))),Uc=v.lazy(()=>oe(()=>import("./ProductSelectionPage-CWUbhSM6.js"),__vite__mapDeps([78,11,6,2,38,25,7,3,4,51,1,31,65,9,10,34,72,27]))),B1=v.lazy(()=>oe(()=>import("./PatternAnalysisDemoPage-BdB8Yspw.js"),__vite__mapDeps([79,1,2,12,71,18,19,14,22,44,17,6,27,66,48,41,26,4,31,42,60,3]))),$1=v.lazy(()=>oe(()=>import("./NotFound-Bj2vnPg6.js"),[])),Vc=v.lazy(()=>oe(()=>import("./MFHManagerPage-BF13aXK8.js"),__vite__mapDeps([80,1,2,6,51,4,25,31,7,65]))),H1=v.lazy(()=>oe(()=>import("./ButtonTest-DM7XAVUz.js"),__vite__mapDeps([81,9,10,27,13]))),W1=v.lazy(()=>oe(()=>import("./TouchTargetValidatorPage-Ahn2en4Z.js"),__vite__mapDeps([82,1,2,12,27,28,29]))),Q1=()=>w.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:w.jsxs("div",{className:"text-center",children:[w.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),w.jsx("p",{className:"text-neutral-600 font-medium",children:"Lädt..."})]})}),G1=new uy({defaultOptions:{queries:{staleTime:1e3*60*5,refetchOnWindowFocus:!1}}});function K1(){return v.useEffect(()=>{x1()},[]),w.jsx(Mc,{children:w.jsx(dy,{client:G1,children:w.jsx(g0,{children:w.jsx(v0,{children:w.jsx(e0,{children:w.jsx(h0,{children:w.jsx(Av,{children:w.jsxs(Mc,{children:[w.jsx("div",{className:"min-h-screen bg-background",children:w.jsx(v.Suspense,{fallback:w.jsx(Q1,{}),children:w.jsxs(Iv,{children:[w.jsx(Z,{path:"/login",element:w.jsx(E1,{})}),w.jsx(Z,{path:"/",element:w.jsx(re,{children:w.jsx(S1,{})})}),w.jsx(Z,{path:"/efh",element:w.jsx(re,{children:w.jsx(k1,{})})}),w.jsx(Z,{path:"/mfh",element:w.jsx(re,{children:w.jsx(C1,{})})}),w.jsx(Z,{path:"/mfh/:id",element:w.jsx(re,{children:w.jsx(Vc,{})})}),w.jsx(Z,{path:"/mfh-manager",element:w.jsx(re,{children:w.jsx(Vc,{})})}),w.jsx(Z,{path:"/map",element:w.jsx(re,{children:w.jsx(P1,{})})}),w.jsx(Z,{path:"/daily-view",element:w.jsx(re,{children:w.jsx(_1,{})})}),w.jsx(Z,{path:"/statistics",element:w.jsx(re,{children:w.jsx(N1,{})})}),w.jsx(Z,{path:"/teams-statistics",element:w.jsx(re,{children:w.jsx(R1,{})})}),w.jsx(Z,{path:"/berater-statistics",element:w.jsx(re,{children:w.jsx(T1,{})})}),w.jsx(Z,{path:"/profile",element:w.jsx(re,{children:w.jsx(O1,{})})}),w.jsx(Z,{path:"/settings",element:w.jsx(re,{children:w.jsx(I1,{})})}),w.jsx(Z,{path:"/calendar",element:w.jsx(re,{children:w.jsx(b1,{})})}),w.jsx(Z,{path:"/admin-dashboard",element:w.jsx(re,{allowedRoles:["admin"],children:w.jsx(L1,{})})}),w.jsx(Z,{path:"/admin-test",element:w.jsx(re,{allowedRoles:["admin"],children:w.jsx(M1,{})})}),w.jsx(Z,{path:"/user-management",element:w.jsx(re,{children:w.jsx(D1,{})})}),w.jsx(Z,{path:"/team-management",element:w.jsx(re,{children:w.jsx(j1,{})})}),w.jsx(Z,{path:"/area-management",element:w.jsx(re,{children:w.jsx(z1,{})})}),w.jsx(Z,{path:"/team-overview",element:w.jsx(re,{children:w.jsx(A1,{})})}),w.jsx(Z,{path:"/teams-overview",element:w.jsx(re,{children:w.jsx(F1,{})})}),w.jsx(Z,{path:"/area-overview",element:w.jsx(re,{children:w.jsx(U1,{})})}),w.jsx(Z,{path:"/visit-status",element:w.jsx(re,{children:w.jsx(V1,{})})}),w.jsx(Z,{path:"/products/:visitId",element:w.jsx(re,{children:w.jsx(Uc,{})})}),w.jsx(Z,{path:"/product-selection",element:w.jsx(re,{children:w.jsx(Uc,{})})}),w.jsx(Z,{path:"/pattern-analysis-demo",element:w.jsx(re,{children:w.jsx(B1,{})})}),w.jsx(Z,{path:"/button-test",element:w.jsx(re,{children:w.jsx(H1,{})})}),w.jsx(Z,{path:"/touch-target-validator",element:w.jsx(re,{children:w.jsx(W1,{})})}),w.jsx(Z,{path:"/404",element:w.jsx($1,{})}),w.jsx(Z,{path:"*",element:w.jsx(Ep,{to:"/404",replace:!0})})]})})}),w.jsx(y0,{})]})})})})})})})})}dp(document.getElementById("root")).render(w.jsx(K1,{}));export{Z1 as $,Ga as A,Ql as B,qp as C,E0 as D,lw as E,uw as F,m1 as G,R0 as H,iw as I,Ae as J,C0 as K,aw as L,X1 as M,J1 as N,Mc as O,Vp as P,L0 as Q,T0 as R,b0 as S,O0 as T,Lr as U,cp as V,si as W,Ac as X,Fg as Y,ew as Z,oe as _,Hn as a,Rh as a0,jp as b,Ka as c,tw as d,bp as e,qy as f,Op as g,Ip as h,Xp as i,w as j,Yp as k,Zp as l,nw as m,g1 as n,q1 as o,qc as p,$p as q,v as r,j as s,Jp as t,yp as u,Xy as v,ow as w,S0 as x,sw as y,rw as z};
