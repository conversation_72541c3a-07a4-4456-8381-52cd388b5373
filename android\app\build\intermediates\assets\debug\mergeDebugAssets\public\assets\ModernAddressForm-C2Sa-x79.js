import{c as je,j as e,r as a,B as G,a as ae,b as ye,m as ne,J as c,u as Ce,k as ke,C as ze,H as Ae,i as Ee,l as Be,n as He}from"./index-Cmt5neWh.js";import{I as ie}from"./input-BK13BBqa.js";import{L as le}from"./label-NwAA2N0T.js";import{C as oe}from"./check-abM7k-xd.js";import{a as ce,E as Le}from"./EFHVisitTracker-BC_kU7m6.js";import{B as Pe}from"./Button-ETlvKXsU.js";import{t as X}from"./useSwipeGestures-49dHBwDT.js";import{Z as de}from"./zap-BNKcOEAu.js";import{U as Fe}from"./user-x-DrICOSZg.js";import{G as Ke}from"./geocodingService-r9QfKNdP.js";import{B as te,a as T}from"./MainLayout-wyzz138D.js";import{L as Ie}from"./loader-circle-Brkx1kW_.js";import{N as Re}from"./navigation-B6ya6VuX.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=je("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),ue={70173:"Stuttgart",70174:"Stuttgart",70175:"Stuttgart",70176:"Stuttgart",70177:"Stuttgart",70178:"Stuttgart",70179:"Stuttgart",70180:"Stuttgart",70181:"Stuttgart",70182:"Stuttgart",70183:"Stuttgart",70184:"Stuttgart",70185:"Stuttgart",70186:"Stuttgart",70187:"Stuttgart",70188:"Stuttgart",70189:"Stuttgart",70190:"Stuttgart",70191:"Stuttgart",70192:"Stuttgart",70193:"Stuttgart",70194:"Stuttgart",70195:"Stuttgart",70196:"Stuttgart",70197:"Stuttgart",70198:"Stuttgart",70199:"Stuttgart",76131:"Karlsruhe",76133:"Karlsruhe",76135:"Karlsruhe",76137:"Karlsruhe",76139:"Karlsruhe",76227:"Karlsruhe",76228:"Karlsruhe",76229:"Karlsruhe",68159:"Mannheim",68161:"Mannheim",68163:"Mannheim",68165:"Mannheim",68167:"Mannheim",68169:"Mannheim",68199:"Mannheim",68219:"Mannheim",68229:"Mannheim",68239:"Mannheim",68259:"Mannheim",68305:"Mannheim",68307:"Mannheim",68309:"Mannheim",79098:"Freiburg",79100:"Freiburg",79102:"Freiburg",79104:"Freiburg",79106:"Freiburg",79108:"Freiburg",79110:"Freiburg",79112:"Freiburg",79114:"Freiburg",79115:"Freiburg",79117:"Freiburg",69115:"Heidelberg",69116:"Heidelberg",69117:"Heidelberg",69118:"Heidelberg",69120:"Heidelberg",69121:"Heidelberg",69123:"Heidelberg",69124:"Heidelberg",69126:"Heidelberg",74072:"Heilbronn",74074:"Heilbronn",74076:"Heilbronn",74078:"Heilbronn",74080:"Heilbronn",74081:"Heilbronn",89073:"Ulm",89075:"Ulm",89077:"Ulm",89079:"Ulm",89081:"Ulm",89082:"Ulm",89083:"Ulm",89085:"Ulm",89087:"Ulm",89090:"Ulm",89134:"Blaustein",89143:"Blaubeuren",89150:"Laichingen",89160:"Dornstadt",89168:"Niederstotzingen",89171:"Illerkirchberg",89173:"Lonsee",89180:"Berghülen",89188:"Merklingen",89191:"Nellingen",89194:"Schnürpflingen",89197:"Weidenstetten",89198:"Westerstetten",89275:"Elchingen",89278:"Nersingen",89284:"Pfaffenhofen an der Roth",89287:"Bellenberg",88299:"Leutkirch",75172:"Pforzheim",75173:"Pforzheim",75175:"Pforzheim",75177:"Pforzheim",75179:"Pforzheim",75180:"Pforzheim",75181:"Pforzheim",80331:"München",80333:"München",80335:"München",80336:"München",80337:"München",80339:"München",80469:"München",80538:"München",80539:"München",80634:"München",80636:"München",80637:"München",80638:"München",80639:"München",80686:"München",80687:"München",80689:"München",80796:"München",80797:"München",80798:"München",80799:"München",80801:"München",80802:"München",80803:"München",80804:"München",80805:"München",80807:"München",80809:"München",80933:"München",80935:"München",80937:"München",80939:"München",80992:"München",80993:"München",80995:"München",80997:"München",80999:"München",81241:"München",81243:"München",81245:"München",81247:"München",81249:"München",81369:"München",81371:"München",81373:"München",81375:"München",81377:"München",81379:"München",76297:"Stutensee",76275:"Ettlingen",76287:"Rheinstetten",79539:"Lörrach",79540:"Lörrach",79541:"Lörrach",79618:"Rheinfelden",88045:"Friedrichshafen",88046:"Friedrichshafen",88048:"Friedrichshafen",88212:"Ravensburg",88213:"Ravensburg",88214:"Ravensburg",71634:"Ludwigsburg",71636:"Ludwigsburg",71638:"Ludwigsburg",71640:"Ludwigsburg",72070:"Tübingen",72072:"Tübingen",72074:"Tübingen",72076:"Tübingen",78462:"Konstanz",78464:"Konstanz",78467:"Konstanz",72760:"Reutlingen",72762:"Reutlingen",72764:"Reutlingen",72766:"Reutlingen",72768:"Reutlingen",72770:"Reutlingen",73728:"Esslingen",73730:"Esslingen",73732:"Esslingen",73734:"Esslingen",73736:"Esslingen",73738:"Esslingen",73740:"Esslingen",73742:"Esslingen",73744:"Esslingen",73746:"Esslingen",73033:"Göppingen",73035:"Göppingen",73037:"Göppingen",73614:"Schorndorf",73525:"Schwäbisch Gmünd",74321:"Bietigheim-Bissingen",74172:"Neckarsulm",78048:"Villingen-Schwenningen",78050:"Villingen-Schwenningen",78052:"Villingen-Schwenningen",78054:"Villingen-Schwenningen"},Ve=t=>ue[t]||null,Te=t=>!t||t.length<2?[]:Object.keys(ue).filter(d=>d.startsWith(t)).slice(0,15),De={Stuttgart:["Königstraße","Schlossplatz","Marktplatz","Wilhelmsplatz","Eberhardstraße","Bolzstraße","Calwer Straße","Rotebühlplatz","Holzstraße","Tübinger Straße","Sophienstraße","Lautenschlagerstraße","Schulstraße","Urbanstraße","Paulinenstraße","Rotebühlstraße","Silberburgstraße","Gymnasiumstraße","Heusteigstraße","Werastraße","Böblinger Straße","Olgastraße","Liststraße","Weimarstraße","Seidenstraße","Alexanderstraße","Kriegsbergstraße","Kronenstraße","Hirschstraße","Hohenheimer Straße"],Karlsruhe:["Kaiserstraße","Zirkel","Waldstraße","Sophienstraße","Akademiestraße","Kronenstraße","Kriegsstraße","Rüppurrer Straße","Ettlinger Straße","Adlerstraße","Baumeisterstraße","Kaiserallee","Reinhold-Frank-Straße","Durlacher Allee","Badener Straße","Herrenstraße","Amalienstraße","Lessingstraße","Goethestraße","Schillerstraße","Hirschstraße","Schlossplatz","Erbprinzenstraße","Douglasstraße"],Mannheim:["Planken","Augustaanlage","Friedrichsplatz","Kunststraße","Fressgasse","Quadratestraße","Paradeplatz","Wasserturmplatz","Schlossplatz","Breite Straße","Schwetzinger Straße","Neckarauer Straße","Bismarckstraße","Waldstraße","Käfertaler Straße","Seckenheimer Straße","Rheingoldstraße","Rheinau-Straße","Neutharder Straße","Hafenstraße"],Freiburg:["Kaiser-Joseph-Straße","Münsterplatz","Rathausplatz","Schusterstraße","Salzstraße","Konviktstraße","Bertoldstraße","Herrenstraße","Basler Straße","Schillerstraße","Friedrichstraße","Talstraße","Günterstalstraße","Lorettostraße","Schwarzwaldstraße","Kartäuserstraße","Stefan-Meier-Straße","Eschholzstraße","Breisacher Straße","Habsburgerstraße"],Heidelberg:["Hauptstraße","Philosophenweg","Neuenheimer Landstraße","Plöck","Rohrbacher Straße","Berliner Straße","Kurfürstenanlage","Bergheimer Straße","Sofienstraße","Theaterstraße","Neckarstaden","Schlossberg","Ziegelhäuser Landstraße","Steubenstraße","Handschuhsheimer Landstraße"]},Ue={Heilbronn:["Allee","Kaiserstraße","Moltkestraße","Sülmerstraße","Gymnasiumstraße","Lohtorstraße","Weinsberger Straße","Bahnhofstraße","Karlstraße","Wollhausstraße","Oststraße","Südstraße","Weststraße","Nordstraße"],Ulm:["Münsterplatz","Hirschstraße","Neue Straße","Frauenstraße","Hafengasse","Donaustraße","Marktplatz","Schuhhausgasse","Platzgasse","Kronengasse","Sattlergasse","Dreiköniggasse","Zeitblomstraße","Olgastraße","Neutorstraße","Bahnhofstraße","Adlerstraße","Münchner Straße","Blaubeurer Straße","Karlstraße","Frauengraben","Herdbruckerstraße","Kreuzgasse","Lautenberg","Münsterplatz","Neue Mitte","Pfauengasse","Rosengasse","Salzstadelgasse","Schwörhausgasse","Weinhof"],München:["Marienplatz","Kaufingerstraße","Neuhauser Straße","Tal","Maximilianstraße","Theatinerstraße","Ludwigstraße","Leopoldstraße","Brienner Straße","Prannerstraße","Kardinal-Faulhaber-Straße","Salvatorstraße","Dienerstraße","Sendlinger Straße","Rindermarkt","Rosental","Viktualienmarkt","Frauenstraße","Petersplatz","Oberanger","Herzog-Wilhelm-Straße","Sonnenstraße","Karlsplatz","Lenbachplatz","Promenadeplatz","Odeonsplatz","Maximilianplatz","Karolinenplatz","Königsplatz","Stiglmaierplatz","Nymphenburger Straße","Schwanthalerstraße","Bayerstraße","Arnulfstraße","Schleißheimer Straße","Dachauer Straße"]},We={Blaustein:["Hauptstraße","Ulmer Straße","Bahnhofstraße","Bergstraße","Lindenstraße","Gartenstraße","Kirchweg","Schulstraße"],Blaubeuren:["Karlstraße","Kirchplatz","Marktplatz","Konbergstraße","Ulmer Straße","Blautopfstraße","Bahnhofstraße"],Leutkirch:["Marktplatz","Hauptstraße","Bahnhofstraße","Kornhausstraße","Kirchgasse","Reichenhofer Straße","Kemptener Straße","Memminger Straße","Ravensburger Straße","Wangener Straße","Ulmer Straße","Schulgasse","Ratsgasse","Bachgasse","Bergstraße","Gartenstraße","Lindenstraße","Eichendorffstraße","Goethestraße","Schillerstraße","Mozartstraße","Beethovenstraße","Brahmsstraße","Hirschstraße","Rosenstraße","Tulpenstraße","Lilienstraße","Nelkenstraße","Veilchenstraße","Sonnenstraße","Mondstraße","Waldstraße","Feldstraße","Wiesenweg","Buchenweg","Birkenweg","Tannenweg","Fichtenweg","Ahornweg","Erlenweg"]},$e=["Hauptstraße","Bahnhofstraße","Kirchstraße","Schulstraße","Gartenstraße","Bergstraße","Waldstraße","Dorfstraße","Lindenstraße","Ringstraße","Bachstraße","Talstraße","Wiesenweg","Birkenweg","Buchenweg","Eichenweg","Tannenweg","Ahornweg","Erlenweg","Kastanienweg","Feldweg","Am Bach","Am Berg","Mühlenweg","Rosenstraße","Tulpenstraße","Nelkenweg","Lilienweg","Sonnenstraße","Mondstraße"],Oe={...De,...Ue,...We},Ze=t=>Oe[t]||$e,_e=(t,d)=>{const m=Ze(t);return d?m.filter(j=>j.toLowerCase().includes(d.toLowerCase())).slice(0,15):m.slice(0,15)},re=({id:t,label:d,value:m,onChange:j,placeholder:C,required:u=!1,className:r,disabled:f=!1,icon:w,isValid:N=!1,type:h="text",maxLength:b})=>e.jsxs("div",{className:"mobile-form-group",children:[e.jsxs(le,{htmlFor:t,className:"mobile-label flex items-center gap-2",children:[w&&e.jsx(w,{className:"h-4 w-4 text-red-500"}),d,u&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(ie,{id:t,type:h,value:m,onChange:g=>j(g.target.value),placeholder:C,required:u,className:`mobile-input bg-white/90 backdrop-blur-sm border-2 rounded-xl transition-all duration-200 touch-feedback ${N?"border-green-400 focus:border-green-500 focus:ring-green-500":"border-gray-200 focus:border-red-500 focus:ring-red-500"} ${r}`,disabled:f,maxLength:b,style:{fontSize:"16px"}}),N&&e.jsx("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2",children:e.jsx(oe,{className:"h-5 w-5 text-green-500"})})]})]}),se=({id:t,label:d,value:m,onChange:j,suggestions:C,placeholder:u,required:r=!1,className:f,disabled:w=!1,maxSuggestions:N=10,icon:h,isValid:b=!1,debounceMs:g=150})=>{const[x,z]=a.useState(!1),[p,v]=a.useState(-1),[y,V]=a.useState(m),H=a.useRef(null),K=a.useRef(null),L=a.useRef(null);a.useEffect(()=>{const n=setTimeout(()=>{V(m)},g);return()=>clearTimeout(n)},[m,g]);const M=C.filter(n=>n.toLowerCase().includes(y.toLowerCase())).slice(0,N).sort((n,i)=>{const s=n.toLowerCase().startsWith(y.toLowerCase()),F=i.toLowerCase().startsWith(y.toLowerCase());return s&&!F?-1:!s&&F?1:0});a.useEffect(()=>{const n=i=>{H.current&&!H.current.contains(i.target)&&z(!1)};return document.addEventListener("mousedown",n),()=>{document.removeEventListener("mousedown",n)}},[]),a.useEffect(()=>{const n=i=>{if(x)switch(i.key){case"ArrowDown":i.preventDefault(),v(s=>s<M.length-1?s+1:s);break;case"ArrowUp":i.preventDefault(),v(s=>s>0?s-1:0);break;case"Enter":p>=0&&p<M.length&&(i.preventDefault(),A(M[p]));break;case"Escape":z(!1),v(-1);break;case"Tab":p>=0&&p<M.length&&(i.preventDefault(),A(M[p]));break}};return x&&document.addEventListener("keydown",n),()=>{document.removeEventListener("keydown",n)}},[x,p,M]),a.useEffect(()=>{if(x&&p>=0&&L.current){const n=L.current.children[p];n&&n.scrollIntoView({block:"nearest",behavior:"smooth"})}},[p,x]);const P=n=>{const i=n.target.value;j(i);const s=C.some(F=>F.toLowerCase()===i.toLowerCase());z(!s&&i.length>0),v(-1)},A=n=>{var i;j(n),z(!1),v(-1),(i=K.current)==null||i.focus()},U=(n,i)=>{n.preventDefault(),A(i)};return e.jsxs("div",{className:"mobile-form-group",ref:H,children:[e.jsxs(le,{htmlFor:t,className:"mobile-label flex items-center gap-2",children:[h&&e.jsx(h,{className:"h-4 w-4 text-red-500"}),d,r&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(ie,{id:t,ref:K,value:m,onChange:P,onFocus:()=>{!C.some(i=>i.toLowerCase()===m.toLowerCase())&&m.length>0&&z(!0)},placeholder:u,required:r,className:`mobile-input bg-white/90 backdrop-blur-sm border-2 rounded-xl transition-all duration-200 touch-feedback ${b?"border-green-400 focus:border-green-500 focus:ring-green-500":"border-gray-200 focus:border-red-500 focus:ring-red-500"} ${f}`,disabled:w,style:{fontSize:"16px"}}),b&&e.jsx("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2",children:e.jsx(oe,{className:"h-5 w-5 text-green-500"})}),x&&M.length>0&&e.jsx("div",{ref:L,className:"absolute z-50 w-full mt-2 bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-2xl max-h-80 overflow-auto animate-fade-in",children:M.map((n,i)=>e.jsx(G,{type:"button",variant:"ghost",className:`mobile-list-item w-full justify-start text-left rounded-none first:rounded-t-xl last:rounded-b-xl touch-feedback ${i===p?"bg-red-50 text-red-600 font-semibold":"hover:bg-red-50 hover:text-red-600"}`,onMouseDown:s=>U(s,n),onMouseEnter:()=>v(i),style:{minHeight:"48px"},children:e.jsx("span",{className:"truncate text-base",children:n})},i))}),x&&m&&M.length===0&&e.jsx("div",{className:"absolute z-50 w-full mt-2 bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-lg p-4 text-center text-gray-500 animate-fade-in",children:e.jsx("p",{className:"text-lg",children:"Keine Vorschläge gefunden"})})]})]})},Qe=({addressData:t,onSuccess:d,className:m})=>{const{user:j}=ye(),{addAddress:C,addHouse:u,addVisit:r,addDoor:f}=ne(),[w,N]=a.useState(!1),h=async()=>{if(!j){c.error("Benutzer nicht angemeldet");return}N(!0),X("medium");try{const b=C({zipCode:t.zipCode,city:t.city,street:t.street}),g=u({addressId:b.id,houseNumber:t.houseNumber,type:t.houseType,latitude:t.latitude,longitude:t.longitude}),x=r({houseId:g.id,timestamp:new Date().toISOString(),status:"N/A"});t.houseType==="EFH"&&f({visitId:x.id,name:"Haupteingang",status:"N/A"}),c.success("⚡ N/A gespeichert - Nächstes Haus eingeben"),X("light"),d==null||d()}catch(b){console.error("Error in Quick N/A:",b),c.error("Fehler beim Speichern"),X("heavy")}finally{N(!1)}};return e.jsx(Pe,{onClick:h,loading:w,disabled:w,variant:"outline",size:"lg",fullWidth:!0,leftIcon:e.jsx(Fe,{className:"h-5 w-5"}),rightIcon:e.jsx(de,{className:"h-4 w-4"}),className:ae("h-14 border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400","transition-all duration-250 active:scale-95",ce.classes.hoverScale,m),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-semibold",children:"Quick N/A"}),e.jsx("span",{className:"text-sm opacity-75",children:"Nicht angetroffen"})]})})},Je=({addressData:t,onSuccess:d,isVisible:m})=>m?e.jsxs("div",{className:ae("mt-4 p-4 bg-red-50 border-2 border-red-200 rounded-xl",ce.classes.slideAndFade),children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:e.jsx(de,{className:"h-5 w-5 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-red-800",children:"Schnell-Tracking"}),e.jsx("p",{className:"text-sm text-red-600",children:'Für häufige "Nicht angetroffen" Fälle'})]})]}),e.jsx(Qe,{addressData:t,onSuccess:d,className:"mt-3"}),e.jsx("p",{className:"text-xs text-red-500 mt-2 text-center",children:"🏃‍♂️ Speichert N/A und bereitet nächstes Haus vor"})]}):null,R="visit-flow-last-address",Xe=24*60*60*1e3,qe=()=>{const[t,d]=a.useState(null);return a.useEffect(()=>{try{const u=localStorage.getItem(R);if(u){const r=JSON.parse(u);Date.now()-r.lastUsed<Xe?d(r):localStorage.removeItem(R)}}catch(u){console.error("Fehler beim Laden der gespeicherten Adresse:",u),localStorage.removeItem(R)}},[]),{persistedAddress:t,saveAddress:(u,r,f)=>{if(!u||!r||!f)return;const w={zipCode:u,city:r,street:f,lastUsed:Date.now()};try{localStorage.setItem(R,JSON.stringify(w)),d(w)}catch(N){console.error("Fehler beim Speichern der Adresse:",N)}},clearAddress:()=>{try{localStorage.removeItem(R),d(null)}catch(u){console.error("Fehler beim Löschen der gespeicherten Adresse:",u)}},isCurrentAddress:(u,r,f)=>t?t.zipCode===u&&t.city.toLowerCase()===r.toLowerCase()&&t.street.toLowerCase()===f.toLowerCase():!1,hasPersisted:!!t}};var Ye={};const mt=({houseType:t})=>{const d=Ce(),{addAddress:m,addHouse:j,addVisit:C,addresses:u}=ne(),[r,f]=a.useState(""),[w,N]=a.useState([]),[h,b]=a.useState(""),[g,x]=a.useState(""),[z,p]=a.useState([]),[v,y]=a.useState(""),[V,H]=a.useState(!1),[K,L]=a.useState(null),[D,M]=a.useState(!1),[P,A]=a.useState([]),[U,n]=a.useState(""),i=P.length>0,{persistedAddress:s,saveAddress:F,clearAddress:he,isCurrentAddress:ge,hasPersisted:q}=qe(),me=(o,S)=>{S!==U?(A([o]),n(S)):A(E=>[...E,o])},fe=()=>{A([]),n("")},I=r.length===5&&/^\d{5}$/.test(r),W=h.length>2,$=g.length>2,O=v.length>0,Y=I&&W&&$&&O;a.useEffect(()=>{if(r.length===5){const o=Ve(r);o&&b(o)}},[r]),a.useEffect(()=>{h&&p(_e(h,g))},[h,g]),a.useEffect(()=>{r.length>=2?N(Te(r)):N([])},[r]),a.useEffect(()=>{s&&!r&&!h&&!g&&(f(s.zipCode),b(s.city),x(s.street))},[s,r,h,g]);const be=o=>{const S=o.replace(/\D/g,"").slice(0,5);f(S),S.length!==5&&b("")},xe=o=>{const S=o.replace(/[^0-9a-zA-Z\-/\s]/g,"").slice(0,10);y(S)},Se=()=>I?W?$?O?!0:(c.error("Bitte geben Sie eine Hausnummer ein"),!1):(c.error("Bitte geben Sie eine gültige Straße ein"),!1):(c.error("Bitte geben Sie eine gültige Stadt ein"),!1):(c.error("Bitte geben Sie eine gültige 5-stellige Postleitzahl ein"),!1),pe=async o=>{if(o.preventDefault(),!!Se()){H(!0);try{let S;const E=u.find(l=>l.zipCode===r&&l.city.toLowerCase()===h.toLowerCase()&&l.street.toLowerCase()===g.toLowerCase());E?(S=E.id,c.success("Adresse bereits vorhanden - weiter zur Besuchserfassung")):(S=m({zipCode:r,city:h,street:g}).id,c.success("Neue Adresse erfolgreich hinzugefügt"));const Z=j({addressId:S,houseNumber:v,type:t,latitude:48.1351+Math.random()*.01,longitude:11.582+Math.random()*.01});if(F(r,h,g),t==="EFH"){const l=C({houseId:Z.id,timestamp:new Date().toISOString(),status:"N/A"});L(l.id),c.success("EFH-Besuch erstellt - Status erfassen")}else d(`/mfh/${Z.id}`)}catch(S){c.error("Fehler beim Speichern der Adresse"),console.error(S)}finally{H(!1)}}},we=()=>{L(null),f(""),b(""),x(""),y(""),fe(),c.success("Bereit für neue Adresseingabe")},Ne=async()=>{M(!0);try{const o=await new Promise((B,k)=>{if(!navigator.geolocation){k(new Error("Geolocation wird von diesem Browser nicht unterstützt"));return}navigator.geolocation.getCurrentPosition(B,k,{enableHighAccuracy:!0,timeout:15e3,maximumAge:0})}),{latitude:S,longitude:E}=o.coords,l=await new Ke(Ye.VITE_MAPBOX_ACCESS_TOKEN||"demo-token").reverseGeocodeDetailed(E,S);if(!l)throw new Error("Keine Adresse für diese Position gefunden");if(l.zipCode&&l.city&&l.street)f(l.zipCode),b(l.city),x(l.street),l.houseNumber?(y(l.houseNumber),c.success(`📍 GPS-Adresse erfolgreich erfasst: ${l.street} ${l.houseNumber}, ${l.zipCode} ${l.city}`)):(y(""),c.success(`📍 GPS-Adresse teilweise erfasst: ${l.street}, ${l.zipCode} ${l.city}. Bitte Hausnummer eingeben.`),setTimeout(()=>{const B=document.getElementById("houseNumber");B&&(B.focus(),B.scrollIntoView({behavior:"smooth",block:"center"}))},500));else{const B=Me=>{const _=Me.split(",").map(ee=>ee.trim());if(_.length>=2){const Q=_[0].match(/^(.+?)\s+(\d+[a-zA-Z]?)$/),J=_[1].match(/^(\d{5})\s+(.+)$/);if(Q&&J)return{street:Q[1].trim(),houseNumber:Q[2].trim(),zipCode:J[1].trim(),city:J[2].trim()}}return null},k=l.fullAddress?B(l.fullAddress):null;k?(f(k.zipCode),b(k.city),x(k.street),y(k.houseNumber),c.success(`📍 GPS-Adresse erfolgreich erfasst: ${k.street} ${k.houseNumber}`)):(c.warning("GPS-Position erfasst, aber Adresse konnte nicht automatisch erkannt werden. Bitte manuell eingeben."),console.log("GPS Koordinaten:",{latitude:S,longitude:E,addressData:l}))}}catch(o){if(console.error("GPS-Fehler:",o),o instanceof GeolocationPositionError)switch(o.code){case o.PERMISSION_DENIED:c.error("GPS-Berechtigung verweigert. Bitte in den Browser-Einstellungen aktivieren.");break;case o.POSITION_UNAVAILABLE:c.error("GPS-Position nicht verfügbar. Bitte Standortdienste aktivieren.");break;case o.TIMEOUT:c.error("GPS-Timeout. Bitte erneut versuchen.");break;default:c.error("GPS-Fehler aufgetreten.")}else c.error("Fehler beim Abrufen der GPS-Adresse.")}finally{M(!1)}},ve=()=>{me(v,g),y(""),setTimeout(()=>{const o=document.getElementById("houseNumber");o&&(o.focus(),o.scrollIntoView({behavior:"smooth",block:"center"}))},100)};return K&&t==="EFH"?e.jsxs("div",{className:"space-y-0 w-full",children:[e.jsx(Le,{visitId:K}),e.jsxs("div",{className:"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-t border-blue-100",children:[e.jsxs("button",{onClick:we,className:"w-full h-16 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-3",children:[e.jsx("span",{className:"text-2xl",children:"🏠"}),e.jsx("span",{children:"Neue Adresse eingeben"}),e.jsx("span",{className:"text-xl",children:"→"})]}),e.jsx("p",{className:"text-center text-sm text-blue-600 mt-2 font-medium",children:"Bereit für das nächste Haus"})]})]}):e.jsxs(e.Fragment,{children:[i&&e.jsx("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300",children:e.jsx("div",{className:"bg-green-500 text-white px-3 py-2 rounded-full shadow-lg flex items-center gap-2",children:e.jsx("span",{className:"text-sm font-semibold",children:P.length})})}),e.jsxs(ke,{className:"w-full bg-white/95 backdrop-blur-sm border-0 shadow-none rounded-none overflow-hidden animate-fade-in m-0",children:[e.jsxs(ze,{className:"text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent m-0",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:t==="EFH"?e.jsx(Ae,{className:"h-8 w-8 text-red-600"}):e.jsx(te,{className:"h-8 w-8 text-red-600"})}),e.jsx(Ee,{className:"text-2xl font-bold text-gray-800",children:t==="EFH"?"Einfamilienhaus":"Mehrfamilienhaus"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Adresse für den Besuch eingeben"})]}),e.jsxs(Be,{className:"px-8 pt-6",children:[i&&e.jsx("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-xl",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:e.jsx(T,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"font-semibold text-green-800",children:"Sequenzielles Tracking"}),e.jsxs("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded text-xs",children:[P.length," Häuser erfasst"]})]}),e.jsxs("p",{className:"text-sm text-green-600",children:["📍 ",g,", ",h]}),e.jsxs("div",{className:"flex items-center gap-4 mt-2",children:[e.jsxs("span",{className:"text-xs text-green-600",children:["✅ Erfasst: ",P.join(", ")]}),e.jsx("span",{className:"text-xs text-blue-600",children:"⏰ Nächstes Haus bereit"})]})]})]})}),e.jsxs("form",{onSubmit:pe,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(G,{type:"button",onClick:Ne,disabled:D,className:"w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",children:D?e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"w-5 h-5 animate-spin mr-3"}),"GPS-Position wird ermittelt..."]}):e.jsxs("div",{className:"flex items-center",children:[e.jsx(Re,{className:"w-5 h-5 mr-3"}),"📍 GPS-Adresse automatisch erfassen"]})}),q&&!ge(r,h,g)&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(G,{type:"button",onClick:()=>{s&&(f(s.zipCode),b(s.city),x(s.street),y(""),c.success(`Letzte Adresse geladen: ${s.street}, ${s.city}`))},variant:"outline",className:"flex-1 h-10 text-sm font-medium border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400",children:[e.jsx(T,{className:"w-4 h-4 mr-2"}),"Letzte Adresse: ",s==null?void 0:s.street]}),e.jsx(G,{type:"button",onClick:()=>{he(),c.success("Gespeicherte Adresse gelöscht")},variant:"outline",size:"sm",className:"min-h-[44px] min-w-[44px] px-3 text-gray-500 hover:text-red-600 hover:border-red-300",children:"✕"})]}),e.jsx("p",{className:"text-xs text-gray-500 text-center",children:q?"GPS für neue Adresse oder letzte Adresse wiederverwenden":"Nutzt hochgenaue GPS-Positionierung für automatische Adresseingabe"})]}),e.jsxs("div",{className:"mobile-form-group space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(se,{id:"zipCode",label:"Postleitzahl",value:r,onChange:be,suggestions:w,placeholder:"12345",required:!0,icon:T,isValid:I,maxSuggestions:8}),e.jsx(re,{id:"city",label:"Stadt",value:h,onChange:b,placeholder:"Stadtname",required:!0,icon:te,isValid:W,disabled:!I,className:I?"":"bg-gray-50"})]}),e.jsx(se,{id:"street",label:"Straße",value:g,onChange:x,suggestions:z,placeholder:"Straßenname",required:!0,icon:T,isValid:$,maxSuggestions:12}),e.jsx(re,{id:"houseNumber",label:"Hausnummer",value:v,onChange:xe,placeholder:"123a",required:!0,icon:Ge,isValid:O,maxLength:10})]}),t==="EFH"&&Y&&e.jsx(Je,{addressData:{zipCode:r,city:h,street:g,houseNumber:v,houseType:t,latitude:48.1351+Math.random()*.01,longitude:11.582+Math.random()*.01},onSuccess:ve,isVisible:!0}),e.jsx(He,{className:"px-0 pt-6",children:e.jsx(G,{type:"submit",className:"mobile-action-button w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold rounded-xl transition-all duration-200 active:scale-[0.98] shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed touch-feedback",disabled:V||!Y,"data-size":"large",children:V?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"}),"Speichern..."]}):`${t==="EFH"?"Besuch erstellen":"Wohnungen verwalten"}`})})]})]})]})]})};export{mt as M};
