import{s as n}from"./client-DbI4l5kI.js";const m=[{id:"area-1",name:"Hamburg Nord",description:"Nördliche Stadtteile von Hamburg",postal_codes:["20095","20099","20144","20146"],created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"area-2",name:"München Süd",description:"Südliche Stadtteile von München",postal_codes:["80331","80333","80335","80337"],created_at:new Date().toISOString(),updated_at:new Date().toISOString()}],i=async()=>{try{const{data:{session:e}}=await n.auth.getSession();return e?!0:(console.log("No Supabase session found, using mock data"),!1)}catch(e){return console.log("Supabase not available, using mock data:",e),!1}},w=async e=>{if(!await i()){const t={id:`area-${Date.now()}`,name:e.name,description:e.description,postal_codes:e.postal_codes,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},r=[...JSON.parse(localStorage.getItem("mock_areas")||"[]"),t];return localStorage.setItem("mock_areas",JSON.stringify(r)),t}try{const{data:t,error:a}=await n.from("areas").insert([e]).select().single();if(a)throw a;return t}catch(t){console.error("Supabase error, falling back to mock:",t);const a={id:`area-${Date.now()}`,name:e.name,description:e.description,postal_codes:e.postal_codes,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},o=[...JSON.parse(localStorage.getItem("mock_areas")||"[]"),a];return localStorage.setItem("mock_areas",JSON.stringify(o)),a}},f=async()=>{if(!await i()){const s=JSON.parse(localStorage.getItem("mock_areas")||"[]");return[...m,...s].sort((a,r)=>a.name.localeCompare(r.name))}try{const{data:s,error:t}=await n.from("areas").select("*").order("name");if(t)throw t;return s}catch(s){console.error("Supabase error, falling back to mock:",s);const t=JSON.parse(localStorage.getItem("mock_areas")||"[]");return[...m,...t].sort((r,o)=>r.name.localeCompare(o.name))}},I=async(e,s)=>{if(!await i()){const a=JSON.parse(localStorage.getItem("mock_areas")||"[]"),r=[...m,...a],o=r.findIndex(d=>d.id===e);if(o===-1)throw new Error("Area not found");const c={...r[o],...s,updated_at:new Date().toISOString()},l=a.findIndex(d=>d.id===e);return l!==-1&&(a[l]=c,localStorage.setItem("mock_areas",JSON.stringify(a))),c}try{const{data:a,error:r}=await n.from("areas").update(s).eq("id",e).select().single();if(r)throw r;return a}catch(a){console.error("Supabase error, falling back to mock:",a);const r=JSON.parse(localStorage.getItem("mock_areas")||"[]"),o=[...m,...r],c=o.findIndex(_=>_.id===e);if(c===-1)throw new Error("Area not found");const l={...o[c],...s,updated_at:new Date().toISOString()},d=r.findIndex(_=>_.id===e);return d!==-1&&(r[d]=l,localStorage.setItem("mock_areas",JSON.stringify(r))),l}},A=async e=>{if(!await i()){const a=JSON.parse(localStorage.getItem("mock_areas")||"[]").filter(r=>r.id!==e);localStorage.setItem("mock_areas",JSON.stringify(a));return}try{const{error:t}=await n.from("areas").delete().eq("id",e);if(t)throw t}catch(t){console.error("Supabase error, falling back to mock:",t);const r=JSON.parse(localStorage.getItem("mock_areas")||"[]").filter(o=>o.id!==e);localStorage.setItem("mock_areas",JSON.stringify(r))}},g=[{id:"team-1",name:"Nord-Team",description:"Verantwortlich für nördliche Gebiete",area_id:"area-1",team_leader_id:"leader-1",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"team-2",name:"Süd-Team",description:"Verantwortlich für südliche Gebiete",area_id:"area-2",team_leader_id:"leader-2",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}],O=async e=>{if(!await i()){const t={id:`team-${Date.now()}`,name:e.name,description:e.description,area_id:e.area_id,team_leader_id:e.team_leader_id,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},r=[...JSON.parse(localStorage.getItem("mock_teams")||"[]"),t];return localStorage.setItem("mock_teams",JSON.stringify(r)),t}try{const{data:t,error:a}=await n.from("teams").insert([e]).select().single();if(a)throw a;return t}catch(t){console.error("Supabase error, falling back to mock:",t);const a={id:`team-${Date.now()}`,name:e.name,description:e.description,area_id:e.area_id,team_leader_id:e.team_leader_id,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},o=[...JSON.parse(localStorage.getItem("mock_teams")||"[]"),a];return localStorage.setItem("mock_teams",JSON.stringify(o)),a}},k=async()=>{if(!await i()){const s=JSON.parse(localStorage.getItem("mock_teams")||"[]");return[...g,...s].sort((a,r)=>a.name.localeCompare(r.name))}try{const{data:s,error:t}=await n.from("teams").select(`
        *,
        areas (
          id,
          name
        )
      `).order("name");if(t)throw t;return s}catch(s){console.error("Supabase error, falling back to mock:",s);const t=JSON.parse(localStorage.getItem("mock_teams")||"[]");return[...g,...t].sort((r,o)=>r.name.localeCompare(o.name))}},b=async(e,s)=>{const{data:t,error:a}=await n.from("teams").update(s).eq("id",e).select().single();if(a)throw a;return t},y=async e=>{const{error:s}=await n.from("teams").delete().eq("id",e);if(s)throw s},S=[{id:"profile-1",user_id:"admin-1",full_name:"Admin User",email:"<EMAIL>",role:"admin",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),teams:{id:"team-1",name:"Admin Team"}},{id:"profile-2",user_id:"berater-1",full_name:"Test Berater",email:"<EMAIL>",role:"berater",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),teams:{id:"team-1",name:"Nord-Team"}}],h=async()=>{if(!await i()){const s=JSON.parse(localStorage.getItem("mock_user_profiles")||"[]");return[...S,...s].sort((a,r)=>new Date(r.created_at).getTime()-new Date(a.created_at).getTime())}try{const{data:s,error:t}=await n.from("user_profiles").select(`
        *,
        teams (
          id,
          name
        )
      `).order("created_at",{ascending:!1});if(t)throw t;return s}catch(s){console.error("Supabase error, falling back to mock:",s);const t=JSON.parse(localStorage.getItem("mock_user_profiles")||"[]");return[...S,...t].sort((r,o)=>new Date(o.created_at).getTime()-new Date(r.created_at).getTime())}},N=async(e,s)=>{const{data:t,error:a}=await n.from("user_profiles").update(s).eq("id",e).select().single();if(a)throw a;return t},T=async(e,s)=>{const{data:t,error:a}=await n.rpc("reset_user_statistics",{p_user_id:e,p_admin_id:s});if(a)throw a;return t},u=[{id:"audit-1",admin_user_id:"admin-1",action_type:"role_assignment",target_type:"user",target_id:"user-1",description:"Bulk role assignment: mentor to 3 users",created_at:new Date(Date.now()-864e5).toISOString(),ip_address:"***********",user_agent:"Mozilla/5.0..."},{id:"audit-2",admin_user_id:"admin-1",action_type:"team_assignment",target_type:"team",target_id:"team-1",description:"Team member assignment",created_at:new Date(Date.now()-1728e5).toISOString(),ip_address:"***********",user_agent:"Mozilla/5.0..."}],v=async(e=50)=>{if(!await i()){const t=JSON.parse(localStorage.getItem("mock_audit_logs")||"[]");return[...u,...t].sort((r,o)=>new Date(o.created_at).getTime()-new Date(r.created_at).getTime()).slice(0,e)}try{const{data:t,error:a}=await n.from("audit_logs").select("*").order("created_at",{ascending:!1}).limit(e);if(a)throw a;return t}catch(t){console.error("Supabase error, falling back to mock:",t);const a=JSON.parse(localStorage.getItem("mock_audit_logs")||"[]");return[...u,...a].sort((o,c)=>new Date(c.created_at).getTime()-new Date(o.created_at).getTime()).slice(0,e)}},J=async e=>{if(!await i()){const t={id:`audit-${Date.now()}`,admin_user_id:e.admin_user_id,action_type:e.action_type,target_type:e.target_type,target_id:e.target_id,old_data:e.old_data,new_data:e.new_data,description:e.description,created_at:new Date().toISOString(),ip_address:"127.0.0.1",user_agent:navigator.userAgent},a=JSON.parse(localStorage.getItem("mock_audit_logs")||"[]"),r=[t,...a];return localStorage.setItem("mock_audit_logs",JSON.stringify(r)),t}try{const{data:t,error:a}=await n.rpc("log_admin_action",{p_admin_user_id:e.admin_user_id,p_action_type:e.action_type,p_target_type:e.target_type,p_target_id:e.target_id,p_old_data:e.old_data,p_new_data:e.new_data,p_description:e.description});if(a)throw a;return t}catch(t){console.error("Supabase error, falling back to mock:",t);const a={id:`audit-${Date.now()}`,admin_user_id:e.admin_user_id,action_type:e.action_type,target_type:e.target_type,target_id:e.target_id,old_data:e.old_data,new_data:e.new_data,description:e.description,created_at:new Date().toISOString(),ip_address:"127.0.0.1",user_agent:navigator.userAgent},r=JSON.parse(localStorage.getItem("mock_audit_logs")||"[]"),o=[a,...r];return localStorage.setItem("mock_audit_logs",JSON.stringify(o)),a}};export{k as a,v as b,h as c,O as d,b as e,y as f,f as g,w as h,I as i,A as j,J as l,T as r,N as u};
