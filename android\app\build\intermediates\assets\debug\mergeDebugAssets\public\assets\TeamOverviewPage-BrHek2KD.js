import{b as i,j as e}from"./index-Cmt5neWh.js";import{M as r}from"./MainLayout-wyzz138D.js";import{M as o}from"./MentorDashboard-CXkcQl70.js";import"./input-BK13BBqa.js";import"./table-Bp6KGmPn.js";import"./tabs-BJh52NhZ.js";import"./index-C8JwcrUT.js";import"./badge-XkNoLG2o.js";import"./eye-DwLHs0eg.js";import"./circle-check-big-DK6RP7UF.js";const x=()=>{const{user:t}=i();return!t||t.role!=="mentor"?e.jsx(r,{title:"Team Übersicht",children:e.jsx("div",{className:"p-4 text-center",children:"<PERSON>e haben keine Berecht<PERSON>, diese Seite anzuzeigen."})}):e.jsx(r,{title:"Team Übersicht",children:e.jsx(o,{mentorId:t.id})})};export{x as default};
