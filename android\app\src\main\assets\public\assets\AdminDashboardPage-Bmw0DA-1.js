import{c as Se,b as pe,r as o,J as d,K as $,j as e,T as E,B as g,k as S,l as k,C as A,i as R,y as Ne,L as De,t as ee,z as Ae,m as Re,I as Ie}from"./index-Cmt5neWh.js";import{b as q,U as V,T as ze,e as oe,S as de,B as Te,a as Be,M as ve}from"./MainLayout-wyzz138D.js";import{B as z}from"./badge-XkNoLG2o.js";import{T as Ee,a as Pe,b as J,c as G}from"./tabs-BJh52NhZ.js";import{D as te,b as ae,c as re,d as le,e as ne}from"./dialog-BFTaoFLK.js";import{S as me,a as xe,b as he,c as ue,d as _}from"./select-IVIdgARa.js";import{C as ye}from"./checkbox-ClMhnSPl.js";import{L as w}from"./label-NwAA2N0T.js";import{T as ie}from"./textarea-BM1-JMTm.js";import{I as Q}from"./input-BK13BBqa.js";import{P as X}from"./progress-DeyBaPEi.js";import{C as Z}from"./clock-DhYcPjhn.js";import{U as je,A as ce}from"./upload-CWJxBjPA.js";import{C as se}from"./circle-check-big-DK6RP7UF.js";import{Z as fe}from"./zap-BNKcOEAu.js";import{C as Fe}from"./circle-x-IVLZ_bbf.js";import{S as Y}from"./switch-DOMi03D3.js";import{P as Ue}from"./plus-C74OdLeW.js";import{S as Oe,T as Le}from"./trash-2-BcpLsksG.js";import{D as ke}from"./download-BHlV_KY3.js";import{D as ge}from"./database-DPAjLTxQ.js";import{R as be}from"./rotate-ccw-YT9XFzuk.js";import{g as $e,a as Me,b as _e,c as Ve,l as Ze}from"./functions-DcGEt8N_.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./chevron-down-I8tOk39n.js";import"./check-abM7k-xd.js";import"./client-DbI4l5kI.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=Se("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=Se("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),Ge=({isOpen:t,onClose:u,users:b,onAssignRoles:D})=>{const{user:C}=pe(),v=q(),[i,p]=o.useState([]),[h,j]=o.useState(""),[n,m]=o.useState(!1),[P,O]=o.useState(""),[I,x]=o.useState(""),[r,N]=o.useState(!1),[s,a]=o.useState(0),[f,y]=o.useState(""),[K,H]=o.useState(!1),l=["berater","mentor","teamleiter","gebietsmanager","admin"],T=o.useCallback((c,U)=>{p(B=>U?[...B,c]:B.filter(L=>L!==c))},[]),F=o.useCallback(c=>{p(c?b.map(U=>U.id):[])},[b]),M=o.useCallback(()=>{try{const U=f.trim().split(`
`).map(L=>L.trim().toLowerCase()).filter(Boolean),B=b.filter(L=>U.includes(L.email.toLowerCase()));p(B.map(L=>L.id)),y(""),H(!1),d.success(`${B.length} Benutzer aus CSV importiert`)}catch{d.error("Fehler beim Importieren der CSV-Daten")}},[f,b]),Ce=async()=>{if(!C||!h)return;const c={userIds:i,targetRole:h,removeExistingRoles:n,reason:P.trim()||void 0,expiresAt:I||void 0},U=De(C,c);if(!U.isValid){U.errors.forEach(B=>d.error(B));return}N(!0),a(0);try{const B=setInterval(()=>{a(L=>Math.min(L+10,90))},100);await D(c),clearInterval(B),a(100),d.success(`Rollen erfolgreich für ${i.length} Benutzer zugewiesen`),p([]),j(""),m(!1),O(""),x(""),setTimeout(()=>{u(),a(0)},1e3)}catch(B){d.error("Fehler bei der Rollenzuweisung"),console.error("Bulk role assignment error:",B)}finally{N(!1)}},W=b.filter(c=>i.includes(c.id));return C&&$(C,"user.bulk_assign")?e.jsx(te,{open:t,onOpenChange:u,children:e.jsxs(ae,{className:`${v?"w-[95vw] h-[90vh]":"max-w-4xl max-h-[90vh]"} overflow-y-auto`,children:[e.jsx(re,{children:e.jsxs(le,{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5 text-blue-600"}),"Massenzuweisung von Rollen"]})}),r&&e.jsx(S,{className:"border-blue-200 bg-blue-50",children:e.jsxs(k,{className:"pt-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Z,{className:"h-4 w-4 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium",children:"Verarbeitung läuft..."})]}),e.jsx(X,{value:s,className:"w-full"}),e.jsx("p",{className:"text-xs text-gray-600 mt-2",children:s<100?"Rollen werden zugewiesen...":"Abgeschlossen!"})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(S,{children:[e.jsx(A,{children:e.jsxs(R,{className:"text-lg flex items-center justify-between",children:["Benutzer auswählen",e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(g,{variant:"outline",size:"sm",onClick:()=>H(!K),children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"CSV Import"]}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>F(i.length!==b.length),children:i.length===b.length?"Alle abwählen":"Alle auswählen"})]})]})}),e.jsxs(k,{children:[K&&e.jsxs("div",{className:"mb-4 p-4 border rounded-lg bg-gray-50",children:[e.jsx(w,{htmlFor:"csv-input",className:"text-sm font-medium",children:"E-Mail-Adressen (eine pro Zeile)"}),e.jsx(ie,{id:"csv-input",value:f,onChange:c=>y(c.target.value),placeholder:`<EMAIL>
<EMAIL>
<EMAIL>`,className:"mt-2",rows:4}),e.jsxs("div",{className:"flex gap-2 mt-2",children:[e.jsx(g,{onClick:M,className:"min-h-[44px]",children:"Importieren"}),e.jsx(g,{variant:"outline",onClick:()=>H(!1),className:"min-h-[44px]",children:"Abbrechen"})]})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:b.map(c=>e.jsxs("div",{className:"flex items-center space-x-3 p-2 rounded hover:bg-gray-50",children:[e.jsx(ye,{checked:i.includes(c.id),onCheckedChange:U=>T(c.id,U)}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium truncate",children:c.name}),e.jsx("p",{className:"text-xs text-gray-500 truncate",children:c.email})]}),e.jsx(z,{variant:"outline",className:"text-xs",children:Ne(c.role)})]},c.id))}),W.length>0&&e.jsxs("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:[e.jsxs("p",{className:"text-sm font-medium text-blue-800",children:[W.length," Benutzer ausgewählt"]}),e.jsxs("div",{className:"flex flex-wrap gap-1 mt-2",children:[W.slice(0,5).map(c=>e.jsx(z,{variant:"secondary",className:"text-xs",children:c.name},c.id)),W.length>5&&e.jsxs(z,{variant:"secondary",className:"text-xs",children:["+",W.length-5," weitere"]})]})]})]})]}),e.jsxs(S,{children:[e.jsx(A,{children:e.jsx(R,{className:"text-lg",children:"Rollenkonfiguration"})}),e.jsxs(k,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(w,{htmlFor:"target-role",children:"Zielrolle"}),e.jsxs(me,{value:h,onValueChange:c=>j(c),children:[e.jsx(xe,{className:"mt-1",children:e.jsx(he,{placeholder:"Rolle auswählen"})}),e.jsx(ue,{children:l.map(c=>e.jsx(_,{value:c,children:Ne(c)},c))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ye,{id:"remove-existing",checked:n,onCheckedChange:m}),e.jsx(w,{htmlFor:"remove-existing",className:"text-sm",children:"Bestehende Rollen entfernen (nur neue Rolle behalten)"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"expiration",children:"Ablaufdatum (optional)"}),e.jsx(Q,{id:"expiration",type:"datetime-local",value:I,onChange:c=>x(c.target.value),className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"reason",children:"Grund für die Zuweisung (optional)"}),e.jsx(ie,{id:"reason",value:P,onChange:c=>O(c.target.value),placeholder:"Beschreiben Sie den Grund für diese Rollenzuweisung...",className:"mt-1",rows:3})]})]})]})]}),e.jsxs(ne,{className:"flex gap-2",children:[e.jsx(g,{variant:"outline",onClick:u,disabled:r,children:"Abbrechen"}),e.jsx(g,{onClick:Ce,disabled:!h||i.length===0||r,className:"min-w-[120px]",children:r?e.jsxs(e.Fragment,{children:[e.jsx(Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Verarbeitung..."]}):e.jsxs(e.Fragment,{children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),"Rollen zuweisen"]})})]})]})}):e.jsx(te,{open:t,onOpenChange:u,children:e.jsxs(ae,{className:`${v?"w-[95vw]":"max-w-2xl"}`,children:[e.jsx(re,{children:e.jsxs(le,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-5 w-5 text-red-500"}),"Zugriff verweigert"]})}),e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung für Massenzuweisungen von Rollen."})}),e.jsx(ne,{children:e.jsx(g,{onClick:u,variant:"outline",children:"Schließen"})})]})})},we=({health:t,detailed:u=!1})=>{const b=q();if(!t)return e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-5 w-5 text-gray-400"}),"System Health"]})}),e.jsx(k,{children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin"}),e.jsx("p",{className:"text-gray-500",children:"Lade Systemstatus..."})]})})]});const D=i=>{switch(i){case"healthy":return e.jsx(se,{className:"h-5 w-5 text-green-600"});case"warning":return e.jsx(E,{className:"h-5 w-5 text-yellow-600"});case"critical":return e.jsx(Fe,{className:"h-5 w-5 text-red-600"});default:return e.jsx(ce,{className:"h-5 w-5 text-gray-400"})}},C=i=>{switch(i){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"critical":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},v=[{label:"Verfügbarkeit",value:t.uptime,unit:"%",icon:ze,description:"System-Verfügbarkeit der letzten 30 Tage"},{label:"Speichernutzung",value:t.memoryUsage,unit:"%",icon:ce,description:"Aktuelle RAM-Auslastung"},{label:"Aktive Benutzer",value:t.activeUsers,unit:"",icon:V,description:"Derzeit angemeldete Benutzer"},{label:"Antwortzeit",value:t.responseTime,unit:"ms",icon:fe,description:"Durchschnittliche API-Antwortzeit"}];return e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(A,{children:[e.jsxs(R,{className:"flex items-center gap-2",children:[D(t.status),"System Health Monitor"]}),e.jsxs(ee,{children:["Letzte Aktualisierung: ",new Date(t.lastChecked).toLocaleString("de-DE")]})]}),e.jsxs(k,{className:"space-y-6",children:[e.jsx("div",{className:`p-4 rounded-lg border ${C(t.status)}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Systemstatus"}),e.jsxs("p",{className:"text-sm opacity-80",children:[t.status==="healthy"&&"Alle Systeme funktionieren normal",t.status==="warning"&&"Einige Systeme benötigen Aufmerksamkeit",t.status==="critical"&&"Kritische Probleme erkannt"]})]}),e.jsx(z,{variant:t.status==="healthy"?"default":"destructive",className:"capitalize",children:t.status==="healthy"?"Gesund":t.status==="warning"?"Warnung":"Kritisch"})]})}),e.jsx("div",{className:`grid ${u?"grid-cols-1 lg:grid-cols-2":"grid-cols-2"} gap-4`,children:v.map((i,p)=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(i.icon,{className:"h-4 w-4 text-gray-600"}),e.jsx("span",{className:`font-medium ${b?"text-sm":"text-base"}`,children:i.label})]}),e.jsxs("span",{className:`font-bold ${b?"text-sm":"text-base"}`,children:[i.value,i.unit]})]}),(i.label==="Verfügbarkeit"||i.label==="Speichernutzung")&&e.jsxs("div",{className:"space-y-1",children:[e.jsx(X,{value:i.value,className:"h-2"}),u&&e.jsx("p",{className:"text-xs text-gray-500",children:i.description})]}),i.label==="Antwortzeit"&&u&&e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-xs text-gray-500",children:[e.jsx("span",{children:"Ziel: <250ms"}),e.jsx("span",{className:i.value<=250?"text-green-600":"text-red-600",children:i.value<=250?"✓ Gut":"⚠ Langsam"})]}),e.jsx(X,{value:Math.min(i.value/500*100,100),className:"h-2"})]})]},p))}),u&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4 text-gray-600"}),e.jsx("span",{className:"font-medium",children:"Fehlerrate"})]}),e.jsxs("span",{className:"font-bold",children:[t.errorRate,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(X,{value:Math.min(t.errorRate*20,100),className:"h-2"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500",children:[e.jsx("span",{children:"Ziel: <1%"}),e.jsx("span",{className:t.errorRate<=1?"text-green-600":"text-red-600",children:t.errorRate<=1?"✓ Niedrig":"⚠ Hoch"})]})]})]}),u&&e.jsxs("div",{className:"pt-4 border-t",children:[e.jsx("h4",{className:"font-medium mb-3",children:"Schnellaktionen"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("button",{className:"p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback",children:"System neustarten"}),e.jsx("button",{className:"p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback",children:"Cache leeren"}),e.jsx("button",{className:"p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback",children:"Logs exportieren"}),e.jsx("button",{className:"p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback",children:"Backup erstellen"})]})]}),u&&e.jsxs("div",{className:"pt-4 border-t",children:[e.jsx("h4",{className:"font-medium mb-3",children:"Status-Verlauf (24h)"}),e.jsx("div",{className:"flex gap-1",children:Array.from({length:24},(i,p)=>{const h=Math.random()>.1;return e.jsx("div",{className:`h-8 flex-1 rounded-sm ${h?"bg-green-200":"bg-red-200"}`,title:`${23-p} Stunden ago: ${h?"Gesund":"Problem"}`},p)})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"24h ago"}),e.jsx("span",{children:"Jetzt"})]})]})]})]})},qe=({toggles:t,onUpdate:u,canManage:b})=>{const D=q(),[C,v]=o.useState(!1),[i,p]=o.useState(!1),[h,j]=o.useState(null),[n,m]=o.useState({name:"",description:"",enabled:!1,rolloutPercentage:100,targetRoles:[],targetUsers:[]}),P=()=>{m({name:"",description:"",enabled:!1,rolloutPercentage:100,targetRoles:[],targetUsers:[]})},O=()=>{if(!n.name.trim()){d.error("Name ist erforderlich");return}const s={id:Date.now().toString(),name:n.name.trim(),description:n.description.trim(),enabled:n.enabled,rolloutPercentage:n.rolloutPercentage,targetRoles:n.targetRoles.length>0?n.targetRoles:void 0,targetUsers:n.targetUsers.length>0?n.targetUsers:void 0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};u([...t,s]),d.success(`Feature Toggle "${n.name}" wurde erstellt`),P(),v(!1)},I=()=>{if(!h||!n.name.trim()){d.error("Name ist erforderlich");return}const s={...h,name:n.name.trim(),description:n.description.trim(),enabled:n.enabled,rolloutPercentage:n.rolloutPercentage,targetRoles:n.targetRoles.length>0?n.targetRoles:void 0,targetUsers:n.targetUsers.length>0?n.targetUsers:void 0,updatedAt:new Date().toISOString()},a=t.map(f=>f.id===h.id?s:f);u(a),d.success(`Feature Toggle "${n.name}" wurde aktualisiert`),P(),j(null),p(!1)},x=s=>{if(!confirm(`Möchten Sie das Feature Toggle "${s.name}" wirklich löschen?`))return;const a=t.filter(f=>f.id!==s.id);u(a),d.success(`Feature Toggle "${s.name}" wurde gelöscht`)},r=s=>{const a={...s,enabled:!s.enabled,updatedAt:new Date().toISOString()},f=t.map(y=>y.id===s.id?a:y);u(f),d.success(`Feature Toggle "${s.name}" wurde ${a.enabled?"aktiviert":"deaktiviert"}`)},N=s=>{j(s),m({name:s.name,description:s.description,enabled:s.enabled,rolloutPercentage:s.rolloutPercentage||100,targetRoles:s.targetRoles||[],targetUsers:s.targetUsers||[]}),p(!0)};return b?e.jsxs("div",{className:"space-y-4",children:[e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsx(A,{children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(oe,{className:"h-5 w-5 text-blue-600"}),"Feature Toggle Manager"]}),e.jsx(ee,{children:"Verwalten Sie Feature-Flags und Rollout-Strategien"})]}),e.jsxs(g,{onClick:()=>v(!0),className:"min-h-[44px]",children:[e.jsx(Ue,{className:"h-4 w-4 mr-2"}),"Neues Feature"]})]})}),e.jsx(k,{children:t.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(oe,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Noch keine Feature Toggles konfiguriert"})]}):e.jsx("div",{className:"space-y-4",children:t.map(s=>e.jsx("div",{className:"border rounded-lg p-4 space-y-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"font-semibold truncate",children:s.name}),e.jsx(Y,{checked:s.enabled,onCheckedChange:()=>r(s)}),e.jsx(z,{variant:s.enabled?"default":"secondary",children:s.enabled?"Aktiv":"Inaktiv"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:s.description}),e.jsxs("div",{className:"flex flex-wrap gap-2 text-xs",children:[s.rolloutPercentage!==void 0&&s.rolloutPercentage<100&&e.jsxs(z,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(Ke,{className:"h-3 w-3"}),s.rolloutPercentage,"% Rollout"]}),s.targetRoles&&s.targetRoles.length>0&&e.jsxs(z,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3"}),s.targetRoles.length," Rollen"]}),s.targetUsers&&s.targetUsers.length>0&&e.jsxs(z,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3"}),s.targetUsers.length," Benutzer"]}),e.jsxs(z,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(Z,{className:"h-3 w-3"}),new Date(s.updatedAt).toLocaleDateString("de-DE")]})]})]}),e.jsxs("div",{className:"flex gap-2 ml-4",children:[e.jsx(g,{variant:"outline",size:"sm",onClick:()=>N(s),className:"min-h-[44px] min-w-[44px]",children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>x(s),className:"min-h-[44px] min-w-[44px] text-red-600 hover:text-red-700",children:e.jsx(Le,{className:"h-4 w-4"})})]})]})},s.id))})})]}),e.jsx(te,{open:C,onOpenChange:v,children:e.jsxs(ae,{className:`${D?"w-[95vw]":"max-w-2xl"}`,children:[e.jsx(re,{children:e.jsx(le,{children:"Neues Feature Toggle erstellen"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(w,{htmlFor:"name",children:"Name"}),e.jsx(Q,{id:"name",value:n.name,onChange:s=>m(a=>({...a,name:s.target.value})),placeholder:"feature_name",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"description",children:"Beschreibung"}),e.jsx(ie,{id:"description",value:n.description,onChange:s=>m(a=>({...a,description:s.target.value})),placeholder:"Beschreibung des Features...",className:"mt-1",rows:3})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Y,{checked:n.enabled,onCheckedChange:s=>m(a=>({...a,enabled:s}))}),e.jsx(w,{children:"Feature aktiviert"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"rollout",children:"Rollout-Prozentsatz"}),e.jsx(Q,{id:"rollout",type:"number",min:"0",max:"100",value:n.rolloutPercentage,onChange:s=>m(a=>({...a,rolloutPercentage:parseInt(s.target.value)||0})),className:"mt-1"})]})]}),e.jsxs(ne,{children:[e.jsx(g,{variant:"outline",onClick:()=>v(!1),children:"Abbrechen"}),e.jsx(g,{onClick:O,children:"Erstellen"})]})]})}),e.jsx(te,{open:i,onOpenChange:p,children:e.jsxs(ae,{className:`${D?"w-[95vw]":"max-w-2xl"}`,children:[e.jsx(re,{children:e.jsx(le,{children:"Feature Toggle bearbeiten"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(w,{htmlFor:"edit-name",children:"Name"}),e.jsx(Q,{id:"edit-name",value:n.name,onChange:s=>m(a=>({...a,name:s.target.value})),className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"edit-description",children:"Beschreibung"}),e.jsx(ie,{id:"edit-description",value:n.description,onChange:s=>m(a=>({...a,description:s.target.value})),className:"mt-1",rows:3})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Y,{checked:n.enabled,onCheckedChange:s=>m(a=>({...a,enabled:s}))}),e.jsx(w,{children:"Feature aktiviert"})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"edit-rollout",children:"Rollout-Prozentsatz"}),e.jsx(Q,{id:"edit-rollout",type:"number",min:"0",max:"100",value:n.rolloutPercentage,onChange:s=>m(a=>({...a,rolloutPercentage:parseInt(s.target.value)||0})),className:"mt-1"})]})]}),e.jsxs(ne,{children:[e.jsx(g,{variant:"outline",onClick:()=>p(!1),children:"Abbrechen"}),e.jsx(g,{onClick:I,children:"Speichern"})]})]})})]}):e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-5 w-5 text-red-500"}),"Zugriff verweigert"]})}),e.jsx(k,{children:e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung für die Feature-Verwaltung."})})})]})},He=({user:t,onRefresh:u})=>{const b=q(),[D,C]=o.useState(!1),[v,i]=o.useState(!1),[p,h]=o.useState(!1),[j,n]=o.useState(0),x=[{title:"Daten exportieren",description:"Vollständiger Export aller Systemdaten",icon:ke,action:async()=>{if(!$(t,"data.export")){d.error("Keine Berechtigung für Datenexport");return}C(!0),n(0);try{const r=setInterval(()=>{n(y=>y>=90?(clearInterval(r),y):y+10)},200);await new Promise(y=>setTimeout(y,2e3)),clearInterval(r),n(100);const N={timestamp:new Date().toISOString(),users:[],teams:[],areas:[],visits:[],metadata:{version:"1.0",exportedBy:t.id,totalRecords:1e3}},s=new Blob([JSON.stringify(N,null,2)],{type:"application/json"}),a=URL.createObjectURL(s),f=document.createElement("a");f.href=a,f.download=`admin-export-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(f),f.click(),document.body.removeChild(f),URL.revokeObjectURL(a),d.success("Datenexport erfolgreich abgeschlossen"),setTimeout(()=>{n(0)},2e3)}catch(r){d.error("Fehler beim Datenexport"),console.error("Export error:",r)}finally{C(!1)}},permission:"data.export",loading:D,color:"text-blue-600",bgColor:"bg-blue-50 hover:bg-blue-100"},{title:"Daten importieren",description:"Import von Daten aus JSON-Datei",icon:je,action:async()=>{if(!$(t,"data.import")){d.error("Keine Berechtigung für Datenimport");return}const r=document.createElement("input");r.type="file",r.accept=".json",r.onchange=async N=>{var a;const s=(a=N.target.files)==null?void 0:a[0];if(s){i(!0);try{const f=await s.text(),y=JSON.parse(f);if(!y.metadata||!y.timestamp)throw new Error("Ungültiges Datenformat");await new Promise(K=>setTimeout(K,1500)),d.success(`Datenimport erfolgreich: ${y.metadata.totalRecords||0} Datensätze`),u()}catch(f){d.error("Fehler beim Datenimport: Ungültiges Dateiformat"),console.error("Import error:",f)}finally{i(!1)}}},r.click()},permission:"data.import",loading:v,color:"text-green-600",bgColor:"bg-green-50 hover:bg-green-100"},{title:"System-Backup",description:"Vollständiges System-Backup erstellen",icon:ge,action:async()=>{if(!$(t,"data.backup")){d.error("Keine Berechtigung für System-Backup");return}h(!0);try{await new Promise(r=>setTimeout(r,3e3)),d.success("System-Backup erfolgreich erstellt")}catch(r){d.error("Fehler beim System-Backup"),console.error("Backup error:",r)}finally{h(!1)}},permission:"data.backup",loading:p,color:"text-purple-600",bgColor:"bg-purple-50 hover:bg-purple-100"},{title:"Cache leeren",description:"Browser-Cache und temporäre Daten löschen",icon:be,action:async()=>{try{const r=["currentUser","sessionExpiry","rememberMe"];Object.keys(localStorage).forEach(s=>{r.includes(s)||localStorage.removeItem(s)}),sessionStorage.clear(),d.success("Cache erfolgreich geleert"),u()}catch(r){d.error("Fehler beim Leeren des Cache"),console.error("Cache clear error:",r)}},permission:"system.config",loading:!1,color:"text-orange-600",bgColor:"bg-orange-50 hover:bg-orange-100"}];return e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(A,{children:[e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-5 w-5 text-blue-600"}),"Schnellaktionen"]}),e.jsx(ee,{children:"Häufig verwendete Admin-Funktionen"})]}),e.jsxs(k,{className:"space-y-4",children:[D&&e.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(Z,{className:"h-4 w-4 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Export läuft..."})]}),e.jsx(X,{value:j,className:"w-full"}),e.jsx("p",{className:"text-xs text-blue-600 mt-1",children:j<100?"Daten werden exportiert...":"Export abgeschlossen!"})]}),e.jsx("div",{className:`grid ${b?"grid-cols-1":"grid-cols-2"} gap-3`,children:x.map((r,N)=>{const s=$(t,r.permission);return e.jsxs("button",{onClick:r.action,disabled:!s||r.loading,className:`
                  p-4 rounded-lg border transition-all duration-200 text-left touch-feedback
                  ${s&&!r.loading?`${r.bgColor} border-gray-200 hover:border-gray-300 hover:shadow-md`:"bg-gray-50 border-gray-200 cursor-not-allowed opacity-50"}
                  min-h-[80px] min-w-[120px] flex flex-col justify-between
                `,children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsx(r.icon,{className:`h-6 w-6 ${s?r.color:"text-gray-400"}`}),r.loading&&e.jsx(Z,{className:"h-5 w-5 text-gray-500 animate-spin"}),!s&&e.jsx(de,{className:"h-5 w-5 text-gray-400"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-medium text-sm ${s?"text-gray-900":"text-gray-500"}`,children:r.title}),e.jsx("p",{className:`text-xs mt-1 ${s?"text-gray-600":"text-gray-400"}`,children:r.description})]}),!s&&e.jsx(z,{variant:"secondary",className:"text-xs mt-2 self-start",children:"Keine Berechtigung"})]},N)})}),e.jsxs("div",{className:"pt-4 border-t",children:[e.jsx("h4",{className:"font-medium text-sm mb-3 text-gray-700",children:"System-Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-6 w-6 text-green-500 mx-auto mb-1"}),e.jsx("p",{className:"text-xs text-gray-600",children:"API"}),e.jsx("p",{className:"text-xs font-medium text-green-600",children:"Online"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-6 w-6 text-green-500 mx-auto mb-1"}),e.jsx("p",{className:"text-xs text-gray-600",children:"Datenbank"}),e.jsx("p",{className:"text-xs font-medium text-green-600",children:"Verbunden"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(E,{className:"h-6 w-6 text-yellow-500 mx-auto mb-1"}),e.jsx("p",{className:"text-xs text-gray-600",children:"Cache"}),e.jsx("p",{className:"text-xs font-medium text-yellow-600",children:"Voll"})]})]})]}),e.jsxs("div",{className:"pt-4 border-t",children:[e.jsx("h4",{className:"font-medium text-sm mb-3 text-gray-700",children:"Letzte Aktionen"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600",children:"Datenexport"}),e.jsx("span",{className:"text-gray-500",children:"vor 2 Stunden"})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600",children:"Cache geleert"}),e.jsx("span",{className:"text-gray-500",children:"vor 1 Tag"})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600",children:"System-Backup"}),e.jsx("span",{className:"text-gray-500",children:"vor 3 Tagen"})]})]})]})]})]})},We=()=>{const{settings:t,updateSettings:u,resetSettings:b}=Ae(),D=q(),[C,v]=o.useState(!1),[i,p]=o.useState(!1),[h,j]=o.useState(!1),n=async()=>{j(!0);try{await new Promise(a=>setTimeout(a,1500)),d.success("Systemdaten erfolgreich exportiert");const x={timestamp:new Date().toISOString(),settings:JSON.parse(localStorage.getItem("app_settings")||"{}"),users:JSON.parse(localStorage.getItem("users")||"[]").map(a=>({id:a.id,name:a.name,email:a.email,role:a.role,teamId:a.teamId})),version:"1.0.0"},r=new Blob([JSON.stringify(x,null,2)],{type:"application/json"}),N=URL.createObjectURL(r),s=document.createElement("a");s.href=N,s.download=`system-export-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(N)}catch{d.error("Fehler beim Exportieren der Systemdaten")}finally{j(!1)}},m=async x=>{var N;const r=(N=x.target.files)==null?void 0:N[0];if(r){j(!0);try{const s=await r.text(),a=JSON.parse(s);if(!a.settings||!a.users)throw new Error("Ungültiges Datenformat");await new Promise(f=>setTimeout(f,1e3)),d.success("Systemdaten erfolgreich importiert")}catch{d.error("Fehler beim Importieren der Systemdaten")}finally{j(!1),x.target.value=""}}},P=async()=>{j(!0);try{await new Promise(x=>setTimeout(x,1e3)),b(),d.success("Alle Einstellungen wurden zurückgesetzt"),v(!1)}catch{d.error("Fehler beim Zurücksetzen der Einstellungen")}finally{j(!1)}},O=async()=>{j(!0);try{await new Promise(x=>setTimeout(x,2e3)),localStorage.removeItem("users"),localStorage.removeItem("app_settings"),localStorage.removeItem("mock_areas"),localStorage.removeItem("mock_teams"),localStorage.removeItem("mock_user_profiles"),d.success("Alle Systemdaten wurden zurückgesetzt"),p(!1),setTimeout(()=>{window.location.reload()},1e3)}catch{d.error("Fehler beim Zurücksetzen der Systemdaten")}finally{j(!1)}},I=(x,r)=>{u({[x]:r}),d.success("System-Einstellung aktualisiert")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(oe,{className:"h-5 w-5 text-blue-600"}),"System-Konfiguration"]})}),e.jsxs(k,{className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{children:"Standard-Zeitzone"}),e.jsxs(me,{value:t.timezone,onValueChange:x=>I("timezone",x),children:[e.jsx(xe,{className:`min-h-[44px] ${D?"text-base":""}`,children:e.jsx(he,{})}),e.jsxs(ue,{children:[e.jsx(_,{value:"Europe/Berlin",children:"Europa/Berlin"}),e.jsx(_,{value:"Europe/Vienna",children:"Europa/Wien"}),e.jsx(_,{value:"Europe/Zurich",children:"Europa/Zürich"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{children:"Standard-Sprache"}),e.jsxs(me,{value:t.language,onValueChange:x=>I("language",x),children:[e.jsx(xe,{className:`min-h-[44px] ${D?"text-base":""}`,children:e.jsx(he,{})}),e.jsxs(ue,{children:[e.jsx(_,{value:"de",children:"Deutsch"}),e.jsx(_,{value:"en",children:"English"}),e.jsx(_,{value:"fr",children:"Français"})]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h4",{className:"font-medium text-gray-800 flex items-center gap-2",children:[e.jsx(Je,{className:"h-4 w-4"}),"System-Features"]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx(w,{className:"font-medium",children:"Offline-Modus"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Ermöglicht Arbeiten ohne Internetverbindung"})]}),e.jsx(Y,{checked:t.offlineMode,onCheckedChange:x=>I("offlineMode",x)})]}),e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx(w,{className:"font-medium",children:"Auto-Speichern"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Automatisches Speichern von Änderungen"})]}),e.jsx(Y,{checked:t.autoSave,onCheckedChange:x=>I("autoSave",x)})]})]})]})]})]}),e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-5 w-5 text-green-600"}),"Daten-Management"]})}),e.jsx(k,{className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(g,{onClick:n,disabled:h,className:"min-h-[44px] flex items-center gap-2",variant:"outline",children:[e.jsx(ke,{className:"h-4 w-4"}),h?"Exportiere...":"Systemdaten exportieren"]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"file",accept:".json",onChange:m,className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer",disabled:h}),e.jsxs(g,{disabled:h,className:"min-h-[44px] w-full flex items-center gap-2",variant:"outline",children:[e.jsx(je,{className:"h-4 w-4"}),h?"Importiere...":"Systemdaten importieren"]})]})]})})]}),e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-red-50/90 backdrop-blur-sm border-red-200",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2 text-red-700",children:[e.jsx(E,{className:"h-5 w-5"}),"Gefahrenbereich"]})}),e.jsx(k,{className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(g,{onClick:()=>v(!0),disabled:h,className:"min-h-[44px] flex items-center gap-2",variant:"destructive",children:[e.jsx(be,{className:"h-4 w-4"}),"Einstellungen zurücksetzen"]}),e.jsxs(g,{onClick:()=>p(!0),disabled:h,className:"min-h-[44px] flex items-center gap-2",variant:"destructive",children:[e.jsx(ge,{className:"h-4 w-4"}),"Alle Daten zurücksetzen"]})]})})]}),C&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs(S,{className:"w-full max-w-md",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2 text-red-700",children:[e.jsx(E,{className:"h-5 w-5"}),"Einstellungen zurücksetzen"]})}),e.jsxs(k,{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Sind Sie sicher, dass Sie alle Einstellungen auf die Standardwerte zurücksetzen möchten?"}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(g,{variant:"outline",onClick:()=>v(!1),className:"min-h-[44px]",children:"Abbrechen"}),e.jsx(g,{variant:"destructive",onClick:P,disabled:h,className:"min-h-[44px]",children:h?"Zurücksetzen...":"Zurücksetzen"})]})]})]})}),i&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs(S,{className:"w-full max-w-md",children:[e.jsx(A,{children:e.jsxs(R,{className:"flex items-center gap-2 text-red-700",children:[e.jsx(E,{className:"h-5 w-5"}),"Alle Daten zurücksetzen"]})}),e.jsxs(k,{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:[e.jsx("strong",{children:"WARNUNG:"})," Diese Aktion löscht alle Benutzerdaten, Einstellungen und gespeicherten Informationen unwiderruflich."]}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(g,{variant:"outline",onClick:()=>p(!1),className:"min-h-[44px]",children:"Abbrechen"}),e.jsx(g,{variant:"destructive",onClick:O,disabled:h,className:"min-h-[44px]",children:h?"Lösche...":"Alle Daten löschen"})]})]})]})})]})},Qe=()=>{const{user:t,users:u,updateUser:b}=pe();Re(),q();const[D,C]=o.useState([]),[v,i]=o.useState([]),[p,h]=o.useState([]),[j,n]=o.useState([]),[m,P]=o.useState(null),[O,I]=o.useState([]),[x,r]=o.useState(!0),[N,s]=o.useState(!1);o.useEffect(()=>{a(),f(),y()},[]);const a=async()=>{try{const[l,T,F,M]=await Promise.all([$e(),Me(),_e(50),Ve()]);C(l||[]),i(T||[]),h(F||[]),n(M||[])}catch(l){console.error("Error loading admin data:",l),d.error("Fehler beim Laden der Admin-Daten")}finally{r(!1)}},f=async()=>{const l={status:"healthy",uptime:99.9,memoryUsage:65,activeUsers:u.filter(T=>T.isActive!==!1).length,errorRate:.1,responseTime:180,lastChecked:new Date().toISOString()};P(l)},y=async()=>{const l=[{id:"1",name:"advanced_analytics",description:"Erweiterte Analytik-Features",enabled:!0,rolloutPercentage:100,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",name:"mobile_offline_sync",description:"Mobile Offline-Synchronisation",enabled:!1,rolloutPercentage:0,targetRoles:["admin","teamleiter"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}];I(l)},K=async l=>{if(t)try{await Ze({admin_user_id:t.id,action_type:"role_assignment",target_type:"user",description:`Bulk role assignment: ${l.targetRole} to ${l.userIds.length} users`,new_data:l});for(const T of l.userIds){const F=u.find(M=>M.id===T);if(F){const M={...F,role:l.targetRole,roles:l.removeExistingRoles?[l.targetRole]:[...F.roles||[F.role],l.targetRole],isMultiRole:!l.removeExistingRoles&&F.roles&&F.roles.length>0,updatedAt:new Date().toISOString()};await b(M)}}await a()}catch(T){throw console.error("Error in bulk role assignment:",T),T}},H=[{title:"Aktive Benutzer",value:j.filter(l=>l.is_active!==!1).length,total:j.length,icon:V,color:"from-blue-500 to-blue-600",textColor:"text-blue-600",change:"+5%"},{title:"Teams",value:v.length,icon:Te,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"},{title:"Gebiete",value:D.length,icon:Be,color:"from-green-500 to-green-600",textColor:"text-green-600"},{title:"System Status",value:(m==null?void 0:m.status)==="healthy"?"Gesund":"Warnung",icon:(m==null?void 0:m.status)==="healthy"?se:E,color:(m==null?void 0:m.status)==="healthy"?"from-green-500 to-green-600":"from-yellow-500 to-yellow-600",textColor:(m==null?void 0:m.status)==="healthy"?"text-green-600":"text-yellow-600"}];return!t||!$(t,"system.config")?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(E,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Zugriff verweigert"}),e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung für das Admin-Dashboard."})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Zentrale Verwaltung und Systemüberwachung"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(g,{onClick:()=>s(!0),disabled:!$(t,"user.bulk_assign"),className:"min-h-[44px]",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),"Massenzuweisung"]}),e.jsxs(g,{variant:"outline",onClick:a,className:"min-h-[44px]",children:[e.jsx(be,{className:"h-4 w-4 mr-2"}),"Aktualisieren"]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:H.map((l,T)=>e.jsx(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:e.jsx(k,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:l.title}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:l.value}),l.change&&e.jsx(z,{variant:"secondary",className:"text-xs",children:l.change})]}),l.total&&e.jsxs("p",{className:"text-xs text-gray-500",children:["von ",l.total," gesamt"]})]}),e.jsx("div",{className:`p-3 rounded-full bg-gradient-to-r ${l.color}`,children:e.jsx(l.icon,{className:"h-6 w-6 text-white"})})]})})},T))}),e.jsxs(Ee,{defaultValue:"overview",className:"space-y-4",children:[e.jsxs(Pe,{className:"grid w-full grid-cols-2 lg:grid-cols-6",children:[e.jsx(J,{value:"overview",children:"Übersicht"}),e.jsx(J,{value:"users",children:"Benutzer"}),e.jsx(J,{value:"system",children:"System"}),e.jsx(J,{value:"settings",children:"Einstellungen"}),e.jsx(J,{value:"features",children:"Features"}),e.jsx(J,{value:"audit",children:"Audit"})]}),e.jsx(G,{value:"overview",className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(He,{user:t,onRefresh:a}),e.jsx(we,{health:m})]})}),e.jsx(G,{value:"users",className:"space-y-4",children:e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(A,{children:[e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5 text-blue-600"}),"Benutzerverwaltung"]}),e.jsx(ee,{children:"Verwalten Sie Benutzerrollen und -berechtigungen"})]}),e.jsxs(k,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:[j.length," Benutzer registriert"]}),e.jsxs(g,{onClick:()=>s(!0),disabled:!$(t,"user.bulk_assign"),size:"sm",children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),"Massenzuweisung"]})]}),e.jsx("div",{className:"space-y-2",children:j.slice(0,5).map(l=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:l.full_name||l.email}),e.jsx("p",{className:"text-sm text-gray-500",children:l.email})]}),e.jsx(z,{variant:l.is_active?"default":"secondary",children:l.is_active?"Aktiv":"Inaktiv"})]},l.id))}),j.length>5&&e.jsxs("p",{className:"text-sm text-gray-500 mt-4 text-center",children:["und ",j.length-5," weitere..."]})]})]})}),e.jsx(G,{value:"system",className:"space-y-4",children:e.jsx(we,{health:m,detailed:!0})}),e.jsx(G,{value:"settings",className:"space-y-4",children:e.jsx(We,{})}),e.jsx(G,{value:"features",className:"space-y-4",children:e.jsx(qe,{toggles:O,onUpdate:I,canManage:$(t,"system.config")})}),e.jsx(G,{value:"audit",className:"space-y-4",children:e.jsxs(S,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(A,{children:[e.jsxs(R,{className:"flex items-center gap-2",children:[e.jsx(de,{className:"h-5 w-5 text-orange-600"}),"Audit-Protokoll"]}),e.jsxs(ee,{children:["Letzte ",p.length," Admin-Aktionen im System"]})]}),e.jsx(k,{children:x?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin"}),e.jsx("p",{className:"text-gray-500",children:"Lade Audit-Logs..."})]}):p.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(de,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Noch keine Admin-Aktionen protokolliert"})]}):e.jsx("div",{className:"space-y-2",children:p.slice(0,10).map(l=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:l.action_type}),e.jsx("p",{className:"text-xs text-gray-500",children:l.description})]}),e.jsxs("div",{className:"text-right",children:[e.jsx(z,{variant:"outline",className:"text-xs",children:l.target_type}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:new Date(l.created_at).toLocaleDateString("de-DE")})]})]},l.id))})})]})})]}),e.jsx(Ge,{isOpen:N,onClose:()=>s(!1),users:u,onAssignRoles:K})]})},Ds=()=>{const{user:t}=pe();return!t||!Ie(t)?e.jsx(ve,{title:"Admin Dashboard",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(E,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Zugriff verweigert"}),e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung für das Admin-Dashboard."})]})})}):e.jsx(ve,{title:"Admin Dashboard",children:e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsx(Qe,{})})})};export{Ds as default};
