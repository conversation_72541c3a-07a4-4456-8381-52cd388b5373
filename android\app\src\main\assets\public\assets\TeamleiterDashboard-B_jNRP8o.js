import{m as Y,b as Z,r as v,j as e,k as D,l as S,C as E,i as V,t as H}from"./index-Cmt5neWh.js";import{T as _,a as ee,b as A,c as B}from"./tabs-BJh52NhZ.js";import{s as se}from"./client-DbI4l5kI.js";import{b as $,B as L,U as te,T as ae,C as re,f as z,d as G,a as le}from"./MainLayout-wyzz138D.js";import{T as K,a as O,b as y,c as h,d as P,e as c}from"./table-Bp6KGmPn.js";import{B as w}from"./badge-XkNoLG2o.js";import{C as R}from"./circle-check-big-DK6RP7UF.js";import{E as ne}from"./eye-DwLHs0eg.js";const ie=r=>{const{visits:t,doors:s,products:a,houses:l,addresses:b,getHouseById:T}=Y(),{users:d}=Z(),[f,C]=v.useState([]),[W,q]=v.useState(!0);v.useEffect(()=>{(async()=>{try{const{data:i,error:j}=await se.from("teams").select("id, name");if(j){console.error("Error fetching teams:",j);return}i&&C(i)}catch(i){console.error("Error fetching teams:",i)}finally{q(!1)}})()},[]);const I=n=>d.filter(i=>i.teamId===n),F=()=>f.map(n=>{const i=I(n.id),j=t.filter(o=>i.find(u=>u.id===o.userId)!==void 0),N=a.filter(o=>i.find(u=>u.id===o.userId)!==void 0),m=new Date().toISOString().split("T")[0],x=j.filter(o=>o.timestamp.startsWith(m));return{id:n.id,name:n.name,memberCount:i.length,visitCount:j.length,salesCount:N.length,todayVisits:x.length}}),J=n=>{const i=I(n);return s.filter(N=>{const m=t.find(o=>o.id===N.visitId);return m?i.find(o=>o.id===m.userId)!==void 0&&N.status==="Angetroffen → Sale":!1}).map(N=>{var k;const m=t.find(p=>p.id===N.visitId);if(!m)return null;const x=d.find(p=>p.id===m.userId),o=x!=null&&x.mentorId&&((k=d.find(p=>p.id===x.mentorId))==null?void 0:k.name)||"Nicht zugewiesen",g=T(m.houseId),u=g?b.find(p=>p.id===g.addressId):null;return{visitDate:m.timestamp?new Date(m.timestamp).toLocaleDateString("de-DE"):"Unbekannt",address:u?`${u.street} ${(g==null?void 0:g.houseNumber)||""}, ${u.zipCode} ${u.city}`:"Unbekannte Adresse",beraterName:(x==null?void 0:x.name)||"Unbekannt",mentorName:o}}).filter(Boolean)},U=F(),M=f.find(n=>n.id===r),Q=U.find(n=>n.id===r),X=M?J(M.id):[];return{loading:W,teams:f,teamStats:U,selectedTeam:M,selectedTeamStats:Q,completedVisits:X}},oe=({teamStats:r})=>{const t=$(),s=[{title:"Teams",value:r.length,icon:L,color:"from-blue-500 to-blue-600",textColor:"text-blue-600"},{title:"Gesamt Besuche",value:r.reduce((a,l)=>a+l.visitCount,0),icon:te,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"},{title:"Gesamt Verkäufe",value:r.reduce((a,l)=>a+l.salesCount,0),icon:ae,color:"from-green-500 to-green-600",textColor:"text-green-600"},{title:"Heute",value:r.reduce((a,l)=>a+l.todayVisits,0),icon:re,color:"from-orange-500 to-orange-600",textColor:"text-orange-600"}];return e.jsx("div",{className:`grid gap-4 md:gap-6 ${t?"grid-cols-2":"grid-cols-2 md:grid-cols-4"}`,children:s.map((a,l)=>e.jsx(D,{className:`glass-card hover-lift ${t?"p-3":"p-4"} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`,style:{animationDelay:`${l*.1}s`},children:e.jsx(S,{className:"p-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:`font-medium text-gray-600 ${t?"text-xs":"text-sm"}`,children:a.title}),e.jsx("p",{className:`font-bold ${a.textColor} ${t?"text-xl":"text-3xl"}`,children:a.value})]}),e.jsx("div",{className:`rounded-2xl bg-gradient-to-br ${a.color} p-2 shadow-lg`,children:e.jsx(a.icon,{className:`${t?"h-5 w-5":"h-6 w-6"} text-white`})})]})})},a.title))})},ce=({teamStats:r})=>{const t=$();return e.jsxs(D,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(E,{className:`${t?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(V,{className:`flex items-center gap-3 ${t?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(L,{className:`${t?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Teams-Übersicht"]}),e.jsx(H,{className:`${t?"text-xs":"text-sm"} text-gray-600`,children:z(new Date,"'Stand:' d. MMMM yyyy",{locale:G})})]}),e.jsx(S,{className:`${t?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{className:"overflow-x-auto scrollbar-none",children:e.jsxs(K,{children:[e.jsx(O,{children:e.jsxs(y,{className:"border-gray-200",children:[e.jsx(h,{className:`font-semibold text-gray-700 ${t?"text-xs":"text-sm"}`,children:"Name"}),e.jsx(h,{className:`font-semibold text-gray-700 ${t?"text-xs":"text-sm"}`,children:"Mit."}),e.jsx(h,{className:`font-semibold text-gray-700 ${t?"text-xs":"text-sm"}`,children:"Bes."}),e.jsx(h,{className:`font-semibold text-gray-700 ${t?"text-xs":"text-sm"}`,children:"Verk."})]})}),e.jsxs(P,{children:[r.map((s,a)=>e.jsxs(y,{className:"hover:bg-blue-50/50 transition-colors border-gray-100",style:{animationDelay:`${a*.05}s`},children:[e.jsx(c,{className:`font-medium text-gray-800 ${t?"text-sm":"text-base"}`,children:s.name}),e.jsx(c,{className:`${t?"text-sm":"text-base"}`,children:e.jsx(w,{variant:"secondary",className:"bg-blue-100 text-blue-800 hover:bg-blue-200",children:s.memberCount})}),e.jsx(c,{className:`${t?"text-sm":"text-base"}`,children:e.jsx(w,{variant:"secondary",className:"bg-purple-100 text-purple-800 hover:bg-purple-200",children:s.visitCount})}),e.jsx(c,{className:`${t?"text-sm":"text-base"}`,children:e.jsx(w,{variant:"secondary",className:"bg-green-100 text-green-800 hover:bg-green-200",children:s.salesCount})})]},s.id)),r.length===0&&e.jsx(y,{children:e.jsx(c,{colSpan:4,className:`text-center py-8 text-gray-500 ${t?"text-sm":"text-base"}`,children:"Keine Teams gefunden"})})]})]})})})]})},de=({completedVisits:r,teamName:t="Teams"})=>{const s=$();return e.jsxs(D,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(E,{className:`${s?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(V,{className:`flex items-center gap-3 ${s?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(le,{className:`${s?"h-5 w-5":"h-6 w-6"} text-green-600`}),"Abgeschlossene Adressen"]}),e.jsxs(H,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:["Erfolgreich bearbeitete Adressen von ",t," (",r.length," Einträge)"]})]}),e.jsx(S,{className:`${s?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{className:"overflow-x-auto scrollbar-none",children:e.jsxs(K,{children:[e.jsx(O,{children:e.jsxs(y,{className:"border-gray-200",children:[e.jsx(h,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Datum"}),e.jsx(h,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Adresse"}),!s&&e.jsx(h,{className:"font-semibold text-gray-700 text-sm",children:"Berater"}),!s&&e.jsx(h,{className:"font-semibold text-gray-700 text-sm",children:"Mentor"}),e.jsx(h,{className:`font-semibold text-gray-700 ${s?"text-xs":"text-sm"}`,children:"Status"})]})}),e.jsxs(P,{children:[r.map((a,l)=>e.jsxs(y,{className:"hover:bg-green-50/50 transition-colors border-gray-100",style:{animationDelay:`${l*.05}s`},children:[e.jsx(c,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:a.visitDate}),e.jsx(c,{className:`font-medium text-gray-800 ${s?"text-xs max-w-[120px]":"text-sm max-w-[200px]"} truncate`,children:a.address}),!s&&e.jsx(c,{className:"text-sm text-gray-700",children:a.beraterName}),!s&&e.jsx(c,{className:"text-sm text-gray-700",children:a.mentorName}),e.jsx(c,{children:e.jsxs(w,{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-md",children:[e.jsx(R,{className:"h-3 w-3 mr-1"}),s?"✓":"Abgeschlossen"]})})]},l)),r.length===0&&e.jsx(y,{children:e.jsx(c,{colSpan:s?3:5,className:`text-center py-8 text-gray-500 ${s?"text-sm":"text-base"}`,children:"Keine abgeschlossenen Adressen vorhanden"})})]})]})})})]})},je=({teamId:r})=>{var f;const[t,s]=v.useState("overview"),{loading:a,teamStats:l,selectedTeam:b,completedVisits:T}=ie(r),d=$();return a?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Lade Teams..."})]})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${d?"text-2xl":"text-4xl"}`,children:"Teams Übersicht"}),b&&e.jsxs("p",{className:`text-gray-600 ${d?"text-sm":"text-lg"}`,children:[b.name," | ",((f=l.find(C=>C.id===r))==null?void 0:f.memberCount)||0," Mitglieder"]}),e.jsx("p",{className:`text-gray-500 ${d?"text-xs":"text-sm"}`,children:z(new Date,"'Stand:' d. MMMM yyyy",{locale:G})})]}),e.jsxs(_,{value:t,onValueChange:s,className:"w-full",children:[e.jsxs(ee,{className:`grid w-full grid-cols-2 ${d?"h-12":"h-14"} bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg rounded-2xl`,children:[e.jsxs(A,{value:"overview",className:`${d?"text-sm py-2":"text-base py-3"} data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white rounded-xl transition-all duration-300`,children:[e.jsx(ne,{className:"h-4 w-4 mr-2"}),"Teams Übersicht"]}),e.jsxs(A,{value:"completed",className:`${d?"text-sm py-2":"text-base py-3"} data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300`,children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Abgeschlossen"]})]}),e.jsxs(B,{value:"overview",className:"space-y-6 animate-fade-in",children:[e.jsx(oe,{teamStats:l}),e.jsx(ce,{teamStats:l})]}),e.jsx(B,{value:"completed",className:"space-y-6 animate-fade-in",children:e.jsx(de,{completedVisits:T,teamName:b==null?void 0:b.name})})]})]})})};export{je as T};
