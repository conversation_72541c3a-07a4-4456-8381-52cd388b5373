import{c as C,j as e,a as N,B as K,s as $,Q as D,T as _,N as ee,u as te,m as se,r as T,O as ae,J as y}from"./index-Cmt5neWh.js";import{C as A,b as F,c as re,a as R,d as ne}from"./Card-CELKqcN7.js";import{L as V}from"./label-NwAA2N0T.js";import{C as le}from"./checkbox-ClMhnSPl.js";import{T as ie,a as oe,b as M,c as ce}from"./tabs-BJh52NhZ.js";import{I as de}from"./input-BK13BBqa.js";import{S as me,a as ue,b as he,c as xe,d as ge}from"./select-IVIdgARa.js";import{P as be}from"./plus-C74OdLeW.js";import{B as w}from"./Button-ETlvKXsU.js";import{A as fe}from"./arrow-left-CPLDG87G.js";import{C as L}from"./circle-x-IVLZ_bbf.js";import{C as pe}from"./circle-check-big-DK6RP7UF.js";import{L as ye}from"./loader-circle-Brkx1kW_.js";import{M as je}from"./MainLayout-wyzz138D.js";import"./index-CEhV84jC.js";import"./check-abM7k-xd.js";import"./index-C8JwcrUT.js";import"./chevron-down-I8tOk39n.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=C("CircleMinus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=C("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=C("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=C("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=C("Tv",[["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",ry:"2",key:"10ag99"}],["polyline",{points:"17 2 12 7 7 2",key:"11pgbg"}]]),Ne=({address:t,house:s})=>e.jsxs("div",{className:"text-center pb-4 pt-4",children:[e.jsx("h1",{className:"text-xl md:text-2xl font-bold text-gray-800 mb-3",children:"Produkte erfassen"}),e.jsxs("div",{className:"p-3 md:p-4 glass-card rounded-xl",children:[e.jsxs("p",{className:"text-base md:text-lg font-semibold text-gray-800",children:[t.street," ",s.houseNumber]}),e.jsxs("p",{className:"text-gray-600 text-sm md:text-base",children:[t.zipCode," ",t.city]})]})]}),we=({isMultiDoor:t,salesDoors:s,selectedDoors:a,canSave:r,isSubmitting:n})=>e.jsxs(e.Fragment,{children:[s.length===0&&e.jsx("div",{className:"mb-4 p-3 bg-red-50/80 rounded-xl border border-red-200/50",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(I,{className:"h-4 w-4 text-red-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-red-800 mb-1",children:"Keine Sales-Türen verfügbar"}),e.jsx("p",{className:"text-xs text-red-700",children:'Es wurden keine Türen mit Status "Angetroffen → Sale" gefunden.'})]})]})}),t&&s.length>1&&e.jsx("div",{className:"mb-4 p-3 bg-blue-50/80 rounded-xl border border-blue-200/50",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(I,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-800 mb-1",children:"Mehrere Türen verfügbar"}),e.jsx("p",{className:"text-xs text-blue-700",children:"Wählen Sie eine oder mehrere Türen aus und fügen Sie Produkte hinzu."})]})]})}),!r&&a.length===0&&!n&&e.jsx("div",{className:"mb-3 p-2 bg-orange-50/80 rounded-lg border border-orange-200/50",children:e.jsx("p",{className:"text-xs text-orange-700",children:"Wählen Sie mindestens eine Tür aus um zu speichern."})})]}),Ce=({salesDoors:t,selectedDoors:s,onDoorToggle:a})=>t.length>1?e.jsxs(A,{variant:"default",className:"mb-6",children:[e.jsx(F,{children:e.jsxs(re,{className:"text-lg",children:["Türen auswählen (",s.length," von ",t.length," ausgewählt)"]})}),e.jsx(R,{children:e.jsx("div",{className:"space-y-3",children:t.map(n=>e.jsxs("div",{className:N("flex items-center space-x-4 p-4 rounded-xl border-2 transition-all duration-250 cursor-pointer","hover:bg-blue-50 hover:border-blue-200",s.includes(n.id)?"bg-blue-50 border-blue-300":"bg-white border-neutral-200"),onClick:()=>a(n.id),children:[e.jsx(le,{id:`door-${n.id}`,checked:s.includes(n.id),onCheckedChange:()=>a(n.id),className:"h-5 w-5"}),e.jsxs(V,{htmlFor:`door-${n.id}`,className:"text-base font-medium text-neutral-800 cursor-pointer flex-1",children:[n.name," ",n.floor?`(${n.floor})`:""]})]},n.id))})})]}):null,ke=[{category:"KIP",type:"KIP Standard",label:"KIP Standard"},{category:"TV",type:"TV Connect Standard",label:"TV Connect Standard"},{category:"TV",type:"TV Connect",label:"TV Connect"},{category:"TV",type:"TV Connect Start",label:"TV Connect Start"},{category:"TV",type:"Giga TV / Cable Net",label:"Giga TV / Cable Net"},{category:"TV",type:"Giga TV / Cable Net inkl. Premium",label:"Giga TV / Cable Net inkl. Premium"},{category:"TV",type:"Giga TV / Cable Net inkl. Netflix",label:"Giga TV / Cable Net inkl. Netflix"},{category:"TV",type:"Giga TV Mobile-App",label:"Giga TV Mobile-App"},{category:"TV",type:"Multi Room",label:"Multi Room"},{category:"TV",type:"Multi Room / Home Sound",label:"Multi Room / Home Sound"},{category:"Mobile",type:"Giga Mobil XS",label:"Giga Mobil XS"},{category:"Mobile",type:"Giga Mobil S",label:"Giga Mobil S"},{category:"Mobile",type:"Giga Mobil M",label:"Giga Mobil M"},{category:"Mobile",type:"Giga Mobil L",label:"Giga Mobil L"},{category:"Mobile",type:"Giga Mobil XL",label:"Giga Mobil XL"},{category:"Mobile",type:"FamilyCard S",label:"FamilyCard S"},{category:"Mobile",type:"FamilyCard M",label:"FamilyCard M"},{category:"Mobile",type:"FamilyCard L",label:"FamilyCard L"},{category:"Mobile",type:"Red + Data",label:"Red + Data"}],Te=({product:t,index:s,categoryIndex:a,onRemove:r,onUpdateType:n,onUpdateQuantity:c})=>{const x=l=>{switch(l){case"KIP":return e.jsx(P,{className:"h-6 w-6"});case"TV":return e.jsx(E,{className:"h-6 w-6"});case"Mobile":return e.jsx(O,{className:"h-6 w-6"});default:return e.jsx(P,{className:"h-6 w-6"})}},h=l=>{switch(l){case"KIP":return"KIP Option";case"TV":return"TV Option";case"Mobile":return"Mobile Option";default:return"Option"}},f=t.category!=="KIP";return e.jsxs("div",{className:"glass-card rounded-3xl p-6 border border-gray-200/50 shadow-lg hover-lift",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h4",{className:"text-lg font-semibold text-gray-800 flex items-center gap-2",children:[x(t.category),h(t.category)," ",a+1]}),e.jsx(K,{variant:"ghost",size:"icon",className:"mobile-button h-12 w-12 rounded-full hover:bg-red-100 hover:text-red-600 transition-colors touch-feedback",onClick:r,"data-primary":"true",children:e.jsx(ve,{size:24})})]}),e.jsxs("div",{className:"space-y-4",children:[f&&e.jsxs("div",{children:[e.jsx(V,{htmlFor:`${t.category.toLowerCase()}-type-${s}`,className:"text-base font-medium text-gray-700 mb-2 block",children:"Typ"}),e.jsxs(me,{value:t.type,onValueChange:n,children:[e.jsx(ue,{id:`${t.category.toLowerCase()}-type-${s}`,className:"mobile-input rounded-2xl border-2 border-gray-200 focus:border-red-500 touch-feedback",style:{fontSize:"16px"},children:e.jsx(he,{})}),e.jsx(xe,{className:"bg-white border-2 border-gray-200 shadow-2xl rounded-2xl z-50",children:ke.filter(l=>l.category===t.category).map(l=>e.jsx(ge,{value:l.type,className:"mobile-list-item text-base touch-feedback",style:{minHeight:"48px"},children:l.label},l.type))})]})]}),e.jsxs("div",{children:[e.jsx(V,{htmlFor:`${t.category.toLowerCase()}-quantity-${s}`,className:"mobile-label",children:"Anzahl"}),e.jsx(de,{id:`${t.category.toLowerCase()}-quantity-${s}`,type:"number",min:1,value:t.quantity,onChange:l=>c(parseInt(l.target.value)),className:"mobile-input rounded-2xl border-2 border-gray-200 focus:border-red-500 touch-feedback",style:{fontSize:"16px"}})]})]})]})},Se=({products:t,onAddProduct:s,onRemoveProduct:a,onUpdateProductType:r,onUpdateProductQuantity:n})=>{const c=h=>{switch(h){case"KIP":return e.jsx(P,{className:"h-6 w-6"});case"TV":return e.jsx(E,{className:"h-6 w-6"});case"Mobile":return e.jsx(O,{className:"h-6 w-6"});default:return e.jsx(P,{className:"h-6 w-6"})}},x=(h,f)=>{const l=t.filter(g=>g.category===h);return e.jsxs(ce,{value:h.toLowerCase(),className:"space-y-4",children:[l.map((g,d)=>{const m=t.findIndex(b=>b===g);return e.jsx(Te,{product:g,index:m,categoryIndex:d,onRemove:()=>a(m),onUpdateType:b=>r(m,b),onUpdateQuantity:b=>n(m,b)},m)}),e.jsxs(K,{variant:"outline",className:"w-full h-16 md:h-20 text-lg font-semibold rounded-2xl border-2 border-red-200 hover:border-red-500 hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback hover-scale",onClick:()=>s(h),children:[e.jsx(be,{size:20,className:"mr-2"}),f]})]})};return e.jsxs(ie,{defaultValue:"kip",className:"w-full",children:[e.jsxs(oe,{className:"mobile-tabs grid-cols-3 mb-4 md:mb-6 w-full glass-card rounded-3xl p-2",children:[e.jsx(M,{value:"kip",className:"mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback","data-primary":"true",children:e.jsxs("div",{className:"flex items-center gap-1 md:gap-2",children:[c("KIP"),e.jsx("span",{className:"hidden sm:inline",children:"KIP"})]})}),e.jsx(M,{value:"tv",className:"mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback","data-primary":"true",children:e.jsxs("div",{className:"flex items-center gap-1 md:gap-2",children:[c("TV"),e.jsx("span",{className:"hidden sm:inline",children:"TV"})]})}),e.jsx(M,{value:"mobile",className:"mobile-tab text-sm md:text-base font-semibold rounded-2xl data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200 touch-feedback","data-primary":"true",children:e.jsxs("div",{className:"flex items-center gap-1 md:gap-2",children:[c("Mobile"),e.jsx("span",{className:"hidden sm:inline",children:"Mobile"})]})})]}),x("KIP","KIP hinzufügen"),x("TV","TV Option hinzufügen"),x("Mobile","Mobile Option hinzufügen")]})},Pe=({canSave:t,isSubmitting:s,selectedDoorsCount:a,productsCount:r,onSubmit:n,onBack:c})=>e.jsxs("div",{className:"w-full space-y-4",children:[e.jsx(w,{onClick:n,variant:"success",size:"lg",fullWidth:!0,loading:s,disabled:!t,children:r>0?`${r} Produkte speichern`:"Besuch ohne Produkte speichern"}),e.jsx(w,{onClick:c,variant:"outline",size:"md",fullWidth:!0,leftIcon:e.jsx(fe,{className:"h-4 w-4"}),disabled:s,children:"Zurück"})]}),Me=({address:t,house:s,salesDoors:a,selectedDoors:r,products:n,isSubmitting:c,onDoorToggle:x,onAddProduct:h,onRemoveProduct:f,onUpdateProductType:l,onUpdateProductQuantity:g,onSubmit:d,onBack:m})=>{const b=a.length>1,j=r.length>0&&!c;return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 mobile-container",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(A,{variant:"glass",padding:"none",className:"mobile-card overflow-hidden",children:[e.jsx(F,{className:"p-4 md:p-6",children:e.jsx(Ne,{address:t,house:s})}),e.jsxs(R,{className:"p-4 md:p-6 space-y-4 md:space-y-6",children:[e.jsx(we,{isMultiDoor:b,salesDoors:a,selectedDoors:r,canSave:j,isSubmitting:c}),e.jsx(Ce,{salesDoors:a,selectedDoors:r,onDoorToggle:x}),e.jsx(Se,{products:n,onAddProduct:h,onRemoveProduct:f,onUpdateProductType:l,onUpdateProductQuantity:g})]}),e.jsx(ne,{className:"p-4 md:p-6",children:e.jsx(Pe,{canSave:j,isSubmitting:c,selectedDoorsCount:r.length,productsCount:n.length,onSubmit:d,onBack:m})})]})})})},Ve=D("rounded-xl border p-4 transition-all duration-250",{variants:{variant:{error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800",success:"bg-green-50 border-green-200 text-green-800"},size:{sm:"text-sm",md:"text-base",lg:"text-lg"}},defaultVariants:{variant:"error",size:"md"}}),H=$.forwardRef(({className:t,variant:s,size:a,title:r,message:n,action:c,onDismiss:x,...h},f)=>{const g={error:L,warning:_,info:I,success:pe}[s||"error"];return e.jsx("div",{ref:f,className:N(Ve({variant:s,size:a,className:t})),...h,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(g,{className:"h-5 w-5 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[r&&e.jsx("h3",{className:"font-semibold mb-1",children:r}),e.jsx("p",{className:"text-sm leading-relaxed",children:n}),c&&e.jsx("div",{className:"mt-3",children:e.jsx(w,{variant:s==="error"?"error":"primary",size:"sm",onClick:c.onClick,children:c.label})})]}),x&&e.jsx("button",{onClick:x,className:"flex-shrink-0 p-1 rounded-md hover:bg-black/5 transition-colors","aria-label":"Schließen",children:e.jsx(L,{className:"h-4 w-4"})})]})})});H.displayName="ErrorMessage";const S=({title:t="Nicht gefunden",message:s="Die angeforderte Seite oder Ressource konnte nicht gefunden werden.",onGoHome:a,onGoBack:r})=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx(H,{variant:"error",title:t,message:s,className:"mb-4"}),e.jsxs("div",{className:"flex space-x-3",children:[r&&e.jsx(w,{variant:"outline",onClick:r,className:"flex-1",children:"Zurück"}),a&&e.jsx(w,{variant:"primary",onClick:a,className:"flex-1",children:"Zur Startseite"})]})]})}),Ie=D("flex items-center justify-center",{variants:{variant:{default:"text-blue-600",light:"text-neutral-400",dark:"text-neutral-700",success:"text-green-600",warning:"text-yellow-600",error:"text-red-600"},size:{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"}},defaultVariants:{variant:"default",size:"md"}}),q=$.forwardRef(({className:t,variant:s,size:a,text:r,...n},c)=>e.jsxs("div",{ref:c,className:N("flex flex-col items-center justify-center space-y-2",t),...n,children:[e.jsx(ye,{className:N(Ie({variant:s,size:a}),"animate-spin")}),r&&e.jsx("p",{className:N("text-sm font-medium",s==="light"?"text-neutral-400":s==="dark"?"text-neutral-700":"text-neutral-600"),children:r})]}));q.displayName="LoadingSpinner";const ze=({isLoading:t,text:s="Speichert...",children:a})=>e.jsxs("div",{className:"relative",children:[a,t&&e.jsx("div",{className:"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-2xl",children:e.jsx(q,{text:s})})]}),Be=({visitId:t})=>{const{visitId:s}=ee(),a=te(),r=t||s,{addProduct:n,visits:c,getDoorsByVisit:x,getHouseById:h,getAddressById:f}=se(),[l,g]=T.useState([]),[d,m]=T.useState([]),[b,j]=T.useState(!1),k=r?c.find(i=>i.id===r):null,z=r?x(r):[],p=z.filter(i=>i.status==="Angetroffen → Sale"),v=k?h(k.houseId):null,B=v?f(v.addressId):null;if(T.useEffect(()=>{p.length===1&&l.length===0&&(console.log("Auto-selecting single sales door:",p[0].id),g([p[0].id]))},[p,l.length]),console.log("Doors für Visit:",z),console.log("Sales Doors:",p),console.log("Selected Doors:",l),!r)return e.jsx(S,{title:"Besuch nicht gefunden",message:"Keine Besuchs-ID verfügbar. Bitte starten Sie einen neuen Besuch.",onGoHome:()=>a("/"),onGoBack:()=>a(-1)});if(!k)return e.jsx(S,{title:"Besuch nicht gefunden",message:`Der Besuch mit ID "${r}" konnte nicht gefunden werden.`,onGoHome:()=>a("/"),onGoBack:()=>a(-1)});if(!v)return e.jsx(S,{title:"Haus nicht gefunden",message:`Das Haus mit ID "${k.houseId}" konnte nicht gefunden werden.`,onGoHome:()=>a("/"),onGoBack:()=>a(-1)});if(!B)return e.jsx(S,{title:"Adresse nicht gefunden",message:`Die Adresse mit ID "${v.addressId}" konnte nicht gefunden werden.`,onGoHome:()=>a("/"),onGoBack:()=>a(-1)});const X=i=>{const o={category:i,type:i==="KIP"?"KIP Standard":i==="TV"?"TV Connect Standard":"Giga Mobil S",quantity:1};if(i==="KIP"){const u={category:"TV",type:"TV Connect Standard",quantity:1};d.some(G=>G.category==="TV"&&G.type==="TV Connect Standard")?(m([...d,o]),console.log("KIP hinzugefügt (TV Connect Standard bereits vorhanden):",o),y.success("✅ KIP Standard hinzugefügt")):(m([...d,o,u]),console.log("KIP + TV Connect Standard automatisch hinzugefügt:",[o,u]),y.success("🎯 KIP Standard + TV Connect Standard automatisch hinzugefügt!"))}else m([...d,o]),console.log("Product hinzugefügt:",o)},Q=i=>{const o=[...d];o.splice(i,1),m(o),console.log("Product entfernt, neue Liste:",o)},U=(i,o)=>{const u=[...d];u[i].type=o,m(u)},W=(i,o)=>{if(o<1)return;const u=[...d];u[i].quantity=o,m(u)},Z=i=>{g(o=>o.includes(i)?o.filter(u=>u!==i):[...o,i])},J=async()=>{if(l.length===0){y.error("Bitte wählen Sie mindestens eine Tür aus");return}console.log("Submitting mit selectedDoors:",l,"und products:",d),j(!0);try{let i=0;if(d.length>0){for(const o of l)for(const u of d)n({doorId:o,category:u.category,type:u.type,quantity:u.quantity}),i++;y.success(`${i} Produkte für ${l.length} Tür(en) erfolgreich gespeichert`)}else y.success(`Besuch für ${l.length} Tür(en) ohne Produkte gespeichert`);a("/daily-view")}catch(i){console.error(i),y.error("Fehler beim Speichern der Produkte")}finally{j(!1)}},Y=()=>{a(-1)};return e.jsx(ae,{children:e.jsx(ze,{isLoading:b,text:"Speichert Produkte...",children:e.jsx(Me,{address:B,house:v,salesDoors:p,selectedDoors:l,products:d,isSubmitting:b,onDoorToggle:Z,onAddProduct:X,onRemoveProduct:Q,onUpdateProductType:U,onUpdateProductQuantity:W,onSubmit:J,onBack:Y})})})},_e=()=>e.jsx(je,{title:"Produkte erfassen",children:e.jsx(Be,{})});export{_e as default};
