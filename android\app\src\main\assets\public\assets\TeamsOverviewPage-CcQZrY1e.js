import{b as i,j as e}from"./index-Cmt5neWh.js";import{M as r}from"./MainLayout-wyzz138D.js";import{T as s}from"./TeamleiterDashboard-B_jNRP8o.js";import"./input-BK13BBqa.js";import"./tabs-BJh52NhZ.js";import"./index-C8JwcrUT.js";import"./client-DbI4l5kI.js";import"./table-Bp6KGmPn.js";import"./badge-XkNoLG2o.js";import"./circle-check-big-DK6RP7UF.js";import"./eye-DwLHs0eg.js";const f=()=>{const{user:t}=i();return!t||t.role!=="teamleiter"?e.jsx(r,{title:"Teams Übersicht",children:e.jsx("div",{className:"p-4 text-center w-full",children:"<PERSON>e haben kein<PERSON>, diese Seite anzuzeigen."})}):e.jsx(r,{title:"Teams Übersicht",children:e.jsx("div",{className:"w-full h-full",children:t.teamId&&e.jsx(s,{teamId:t.teamId})})})};export{f as default};
