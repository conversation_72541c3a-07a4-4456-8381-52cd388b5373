import{m as I,b as k,j as e,k as h,l as g,C as B,i as S,t as G,H as V}from"./index-Cmt5neWh.js";import{T as z,a as A,b as p,c as r,d as L,e as n}from"./table-Bp6KGmPn.js";import{b as P,f as b,d as f,B as j,U as R,T as E}from"./MainLayout-wyzz138D.js";const K=()=>{const{visits:c,houses:y,products:d}=I(),{users:o}=k(),t=P(),m=[...new Set(o.filter(s=>s.teamId).map(s=>s.teamId))],N=m.map(s=>{var u;const l=o.filter(a=>a.teamId===s),i=((u=l.find(a=>a.role==="teamleiter"))==null?void 0:u.name)||"Nicht zugewiesen",T=l.filter(a=>a.role==="berater").length,M=l.filter(a=>a.role==="mentor").length,D=c.filter(a=>l.find(x=>x.id===a.userId)!==void 0),U=d.filter(a=>l.find(x=>x.id===a.userId)!==void 0);return{teamId:s,teamLeader:i,memberCount:l.length,beratersCount:T,mentorsCount:M,visitsCount:D.length,productsCount:U.length}}),v=o.length;c.length;const $=y.length,C=d.length,w=[{title:"Benutzer",value:v,icon:R,color:"from-blue-500 to-blue-600",textColor:"text-blue-600"},{title:"Teams",value:m.length,icon:j,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"},{title:"Häuser",value:$,icon:V,color:"from-green-500 to-green-600",textColor:"text-green-600"},{title:"Verkäufe",value:C,icon:E,color:"from-orange-500 to-orange-600",textColor:"text-orange-600"}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${t?"text-2xl":"text-4xl"}`,children:"Gebietsmanager Dashboard"}),e.jsx("p",{className:`text-gray-600 ${t?"text-sm":"text-lg"}`,children:"Gesamtübersicht über alle Teams und Aktivitäten"}),e.jsx("p",{className:`text-gray-500 ${t?"text-xs":"text-sm"}`,children:b(new Date,"'Stand:' d. MMMM yyyy",{locale:f})})]}),e.jsx("div",{className:`grid gap-4 md:gap-6 ${t?"grid-cols-2":"grid-cols-2 md:grid-cols-4"}`,children:w.map((s,l)=>e.jsx(h,{className:`glass-card hover-lift ${t?"p-3":"p-4"} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`,style:{animationDelay:`${l*.1}s`},children:e.jsx(g,{className:"p-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:`font-medium text-gray-600 ${t?"text-xs":"text-sm"}`,children:s.title}),e.jsx("p",{className:`font-bold ${s.textColor} ${t?"text-xl":"text-3xl"}`,children:s.value})]}),e.jsx("div",{className:`rounded-2xl bg-gradient-to-br ${s.color} p-2 shadow-lg`,children:e.jsx(s.icon,{className:`${t?"h-5 w-5":"h-6 w-6"} text-white`})})]})})},s.title))}),e.jsxs(h,{className:"glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(B,{className:`${t?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(S,{className:`flex items-center gap-3 ${t?"text-lg":"text-xl"} font-bold text-gray-800`,children:[e.jsx(j,{className:`${t?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Team-Übersicht"]}),e.jsx(G,{className:`${t?"text-xs":"text-sm"} text-gray-600`,children:b(new Date,"'Stand:' d. MMMM yyyy",{locale:f})})]}),e.jsx(g,{className:`${t?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(z,{children:[e.jsx(A,{children:e.jsxs(p,{children:[e.jsx(r,{className:`${t?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Team"}),e.jsx(r,{className:`${t?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Teamleiter"}),e.jsx(r,{className:`${t?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Mitglieder"}),e.jsx(r,{className:`${t?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Besuche"}),e.jsx(r,{className:`${t?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Verkäufe"})]})}),e.jsx(L,{children:N.map((s,l)=>{var i;return e.jsxs(p,{className:"hover:bg-blue-50/50 transition-colors animate-fade-in",style:{animationDelay:`${l*.1}s`},children:[e.jsx(n,{className:`${t?"text-xs":"text-sm"} font-medium text-gray-800`,children:s.teamId}),e.jsx(n,{className:`${t?"text-xs":"text-sm"} text-gray-700`,children:s.teamLeader}),e.jsx(n,{className:`${t?"text-xs":"text-sm"} text-gray-700`,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:s.memberCount}),e.jsxs("span",{className:"text-gray-500 text-xs",children:["(",s.beratersCount," Berater, ",s.mentorsCount," Mentoren)"]})]})}),e.jsx(n,{className:`${t?"text-xs":"text-sm"} text-gray-700`,children:e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:s.visitsCount})}),e.jsx(n,{className:`${t?"text-xs":"text-sm"} text-gray-700`,children:e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:s.productsCount})})]},(i=s.teamId)==null?void 0:i.toString())})})]})})})]})]})})};export{K as G};
