import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthProvider } from '@/context/auth/AuthContext';
import { SettingsProvider } from '@/context/settings/SettingsProvider';
import { UserDeleteDialog } from '@/components/user-management/UserDeleteDialog';
import { UserBlockDialog } from '@/components/user-management/UserBlockDialog';
import { ConfirmationDialog } from '@/components/ui/ConfirmationDialog';
import { User } from '@/types';

// Mock user for testing
const mockUser: User = {
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'berater',
  isActive: true,
  createdAt: new Date().toISOString()
};

// Mock functions
const mockOnConfirm = vi.fn();
const mockOnOpenChange = vi.fn();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SettingsProvider>
    <AuthProvider>
      {children}
    </AuthProvider>
  </SettingsProvider>
);

describe('Admin User Management Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ConfirmationDialog', () => {
    it('renders delete confirmation dialog correctly', () => {
      render(
        <TestWrapper>
          <ConfirmationDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            title="Benutzer löschen"
            description="Möchten Sie diesen Benutzer wirklich löschen?"
            type="delete"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Benutzer löschen')).toBeInTheDocument();
      expect(screen.getByText('Möchten Sie diesen Benutzer wirklich löschen?')).toBeInTheDocument();
      expect(screen.getByText('Löschen')).toBeInTheDocument();
      expect(screen.getByText('Abbrechen')).toBeInTheDocument();
    });

    it('calls onConfirm when confirm button is clicked', async () => {
      render(
        <TestWrapper>
          <ConfirmationDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            title="Test Dialog"
            description="Test description"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      const confirmButton = screen.getByText('Bestätigen');
      fireEvent.click(confirmButton);

      expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    });

    it('calls onOpenChange when cancel button is clicked', async () => {
      render(
        <TestWrapper>
          <ConfirmationDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            title="Test Dialog"
            description="Test description"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      const cancelButton = screen.getByText('Abbrechen');
      fireEvent.click(cancelButton);

      expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    });
  });

  describe('UserDeleteDialog', () => {
    it('renders user delete dialog with user information', () => {
      render(
        <TestWrapper>
          <UserDeleteDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            user={mockUser}
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Benutzer deaktivieren')).toBeInTheDocument();
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('shows hard delete confirmation when hard delete is selected', async () => {
      render(
        <TestWrapper>
          <UserDeleteDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            user={mockUser}
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      // Find and click the delete type selector
      const deleteTypeSelect = screen.getByRole('combobox');
      fireEvent.click(deleteTypeSelect);

      // Wait for options to appear and select hard delete
      await waitFor(() => {
        const hardDeleteOption = screen.getByText('Endgültig löschen');
        fireEvent.click(hardDeleteOption);
      });

      // Check if confirmation text input appears
      expect(screen.getByPlaceholderText('Test User')).toBeInTheDocument();
    });
  });

  describe('UserBlockDialog', () => {
    it('renders user block dialog correctly', () => {
      render(
        <TestWrapper>
          <UserBlockDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            user={mockUser}
            action="block"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Benutzer blockieren')).toBeInTheDocument();
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('Grund für Blockierung')).toBeInTheDocument();
      expect(screen.getByText('Sperrdauer')).toBeInTheDocument();
    });

    it('renders user unblock dialog correctly', () => {
      render(
        <TestWrapper>
          <UserBlockDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            user={mockUser}
            action="unblock"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Benutzer entsperren')).toBeInTheDocument();
      expect(screen.getByText('Grund für Entsperrung')).toBeInTheDocument();
    });

    it('shows warning for permanent block', async () => {
      render(
        <TestWrapper>
          <UserBlockDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            user={mockUser}
            action="block"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      // Find and click the duration selector
      const durationSelect = screen.getAllByRole('combobox')[1]; // Second combobox is duration
      fireEvent.click(durationSelect);

      // Wait for options to appear and select permanent
      await waitFor(() => {
        const permanentOption = screen.getByText('Dauerhaft');
        fireEvent.click(permanentOption);
      });

      // Check if warning appears
      expect(screen.getByText('Dauerhafte Sperrung')).toBeInTheDocument();
    });
  });

  describe('Mobile Optimization', () => {
    it('applies mobile-first design with proper touch targets', () => {
      render(
        <TestWrapper>
          <ConfirmationDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            title="Test Dialog"
            description="Test description"
            onConfirm={mockOnConfirm}
          />
        </TestWrapper>
      );

      const confirmButton = screen.getByText('Bestätigen');
      const cancelButton = screen.getByText('Abbrechen');

      // Check if buttons have minimum touch target height
      expect(confirmButton).toHaveClass('min-h-[44px]');
      expect(cancelButton).toHaveClass('min-h-[44px]');
    });
  });

  describe('Error Handling', () => {
    it('handles loading state correctly', () => {
      render(
        <TestWrapper>
          <ConfirmationDialog
            isOpen={true}
            onOpenChange={mockOnOpenChange}
            title="Test Dialog"
            description="Test description"
            onConfirm={mockOnConfirm}
            isLoading={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Verarbeite...')).toBeInTheDocument();
      
      // Buttons should be disabled during loading
      const confirmButton = screen.getByText('Verarbeite...');
      const cancelButton = screen.getByText('Abbrechen');
      
      expect(confirmButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
    });
  });
});
