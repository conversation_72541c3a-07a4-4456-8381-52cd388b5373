import{s as d,j as e,a as i,Q as f}from"./index-Cmt5neWh.js";import{L as u}from"./loader-circle-Brkx1kW_.js";const m=f("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-base font-semibold transition-all duration-250 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{primary:"bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500 shadow-md hover:shadow-lg",secondary:"bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus-visible:ring-neutral-500 border border-neutral-200",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500 shadow-md hover:shadow-lg",warning:"bg-yellow-500 text-white hover:bg-yellow-600 focus-visible:ring-yellow-500 shadow-md hover:shadow-lg",error:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500 shadow-md hover:shadow-lg",outline:"border-2 border-neutral-300 bg-transparent text-neutral-700 hover:bg-neutral-50 hover:border-neutral-400 focus-visible:ring-neutral-500",ghost:"text-neutral-700 hover:bg-neutral-100 focus-visible:ring-neutral-500",link:"text-blue-600 underline-offset-4 hover:underline focus-visible:ring-blue-500 p-0 h-auto"},size:{sm:"h-10 px-4 text-sm min-w-[80px]",md:"h-12 px-6 text-base min-w-[100px]",lg:"h-14 px-8 text-lg min-w-[120px]",xl:"h-16 px-10 text-xl min-w-[140px]",icon:"h-12 w-12 p-0"},fullWidth:{true:"w-full",false:""}},defaultVariants:{variant:"primary",size:"md",fullWidth:!1}}),v=d.forwardRef(({className:l,variant:o,size:r,fullWidth:s,loading:n=!1,leftIcon:t,rightIcon:a,disabled:b,children:c,...x},h)=>{const g=b||n;return e.jsxs("button",{className:i(m({variant:o,size:r,fullWidth:s,className:l})),ref:h,disabled:g,...x,children:[n&&e.jsx(u,{className:"mr-2 h-4 w-4 animate-spin"}),!n&&t&&e.jsx("span",{className:"mr-2 flex items-center",children:t}),e.jsx("span",{className:"flex-1 text-center",children:c}),!n&&a&&e.jsx("span",{className:"ml-2 flex items-center",children:a})]})});v.displayName="Button";const j=({status:l,onClick:o,loading:r=!1,disabled:s=!1})=>{const t={"N/A":{icon:"❓",label:"Nicht angetroffen",bgColor:"bg-red-600 hover:bg-red-700",textColor:"text-white"},"Angetroffen → Termin":{icon:"📅",label:"Termin vereinbaren",bgColor:"bg-blue-600 hover:bg-blue-700",textColor:"text-white"},"Angetroffen → Kein Interesse":{icon:"❌",label:"Kein Interesse",bgColor:"bg-neutral-500 hover:bg-neutral-600",textColor:"text-white"},"Angetroffen → Sale":{icon:"💰",label:"Verkauf!",bgColor:"bg-green-600 hover:bg-green-700",textColor:"text-white"}}[l];return console.log("StatusButton rendered:",{status:l,config:t,loading:r,disabled:s}),e.jsx("button",{onClick:o,disabled:r||s,className:i("w-full h-16 rounded-xl font-semibold text-lg transition-all duration-200 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl active:scale-95",t.bgColor,t.textColor,(r||s)&&"opacity-50 cursor-not-allowed"),children:r?e.jsx(u,{className:"h-6 w-6 animate-spin"}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-2xl",children:t.icon}),e.jsx("span",{children:t.label})]})})};export{v as B,j as S};
