var de=Object.defineProperty;var ce=(o,h,t)=>h in o?de(o,h,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[h]=t;var R=(o,h,t)=>ce(o,typeof h!="symbol"?h+"":h,t);import{c as _,r as x,j as e,a as f,B as S,u as ee,b as Z,d as se,v as H,e as me,f as G,g as q,h as V,C as re,i as te,k as ae,T as K,l as oe,R as W,H as he}from"./index-Cmt5neWh.js";import{T as xe,a as ue,b as Q,c as J}from"./tabs-BJh52NhZ.js";import{L as le}from"./label-NwAA2N0T.js";import{S as ge}from"./switch-DOMi03D3.js";import{R as pe,C as fe,a as be}from"./index-CB2fuKla.js";import{I as we,U as O,X as je}from"./input-BK13BBqa.js";import{C as ne}from"./circle-check-big-DK6RP7UF.js";import{E as Ne}from"./eye-off-j0pHjYk_.js";import{E as ve}from"./eye-DwLHs0eg.js";import{C as U}from"./circle-alert-m_xa0j25.js";import{C as ye}from"./chevron-down-I8tOk39n.js";import{L}from"./lock-B_L7eRBi.js";import{Z as Se}from"./zap-BNKcOEAu.js";import{C as Ce}from"./check-abM7k-xd.js";import{M as Ee}from"./mail-4oZ8O593.js";import{A as ke}from"./arrow-left-CPLDG87G.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=_("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=_("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),Ae=pe,Te=fe,Fe=be,Y=(o="light")=>{if("vibrate"in navigator){const h={light:[10],medium:[20],heavy:[30]};navigator.vibrate(h[o])}},F=x.forwardRef(({id:o,label:h,type:t="text",value:d,onChange:b,onBlur:w,onFocus:v,placeholder:u,error:p,isValid:l,required:j=!1,disabled:y=!1,className:C,icon:s,showPasswordToggle:m=!1,autoComplete:n,maxLength:N,"data-testid":I,enableHapticFeedback:k=!0,showCharacterCount:M=!1,helpText:P},E)=>{const[r,c]=x.useState(!1),[g,A]=x.useState(!1),B=t==="password"&&r?"text":t,a=p!=null,i=l&&d.length>0&&!a,D=x.useCallback(()=>{A(!0),k&&Y("light"),v==null||v()},[v,k]),z=x.useCallback(()=>{A(!1),w==null||w()},[w]),ie=x.useCallback(()=>{c($=>!$),k&&Y("medium")},[k]);return e.jsxs("div",{className:f("space-y-2",C),children:[e.jsx(le,{htmlFor:o,className:f("text-base font-semibold transition-colors duration-200",a?"text-red-600":"text-gray-700",j&&"after:content-['*'] after:ml-1 after:text-red-500"),children:h}),e.jsxs("div",{className:"relative",children:[s&&e.jsx("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-10",children:e.jsx("div",{className:f("transition-colors duration-200",a?"text-red-400":g?"text-red-500":"text-gray-400"),children:s})}),e.jsx(we,{ref:E,id:o,type:B,value:d,onChange:$=>b($.target.value),onFocus:D,onBlur:z,placeholder:u,required:j,disabled:y,autoComplete:n,maxLength:N,"data-testid":I,className:f("h-14 text-lg bg-white text-gray-900 border-2 rounded-xl transition-all duration-200","focus:ring-2 focus:ring-red-500/20 focus:outline-none",s?"pl-12":"pl-4",m||i?"pr-12":"pr-4",a?"border-red-500 focus:border-red-500":i?"border-green-500 focus:border-green-500":"border-gray-200 focus:border-red-500",y&&"opacity-50 cursor-not-allowed","min-h-[44px] touch-manipulation")}),e.jsxs("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2",children:[i&&e.jsx(ne,{className:"h-5 w-5 text-green-500"}),m&&t==="password"&&e.jsx(S,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-transparent",onClick:ie,disabled:y,tabIndex:-1,"data-testid":`${I}-password-toggle`,children:r?e.jsx(Ne,{className:"h-4 w-4 text-gray-400"}):e.jsx(ve,{className:"h-4 w-4 text-gray-400"})})]})]}),a&&e.jsxs("div",{className:"flex items-center space-x-2 text-red-600 animate-fade-in",role:"alert","aria-live":"polite",children:[e.jsx(U,{className:"h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"text-sm font-medium",children:p.message})]}),P&&!a&&e.jsx("div",{className:"text-sm text-gray-500",children:P}),(N||M)&&e.jsx("div",{className:"flex justify-end",children:e.jsxs("span",{className:f("text-xs transition-colors duration-200",N&&d.length>N*.9?"text-orange-500 font-medium":N&&d.length===N?"text-red-500 font-medium":"text-gray-400"),children:[d.length,N?`/${N}`:""]})})]})});F.displayName="FormField";const Ie=({email:o,setEmail:h,password:t,setPassword:d})=>{const b=ee(),{login:w,isLoading:v,error:u,clearError:p}=Z(),[l,j]=x.useState(!1),[y,C]=x.useState(!1),[s,m]=x.useState({emailOrName:o,password:t,rememberMe:!1,isSubmitting:!1}),n=se();x.useEffect(()=>{m(r=>({...r,emailOrName:o,password:t}))},[o,t]);const N=r=>{h(r),m(c=>({...c,emailOrName:r})),p(),n.getFieldError("emailOrName")&&n.clearError("emailOrName")},I=r=>{d(r),m(c=>({...c,password:r})),p(),n.getFieldError("password")&&n.clearError("password")},k=r=>{n.setTouched(r),(r==="emailOrName"||r==="password")&&H(s.emailOrName,s.password).forEach(g=>{g.field===r&&n.setError(g)})},M=async r=>{r.preventDefault(),n.setTouched("emailOrName"),n.setTouched("password");const c=H(s.emailOrName,s.password);if(c.length>0){c.forEach(g=>n.setError(g));return}m(g=>({...g,isSubmitting:!0}));try{(await w(s.emailOrName,s.password,y)).success&&b("/")}catch(g){console.error("Login error:",g)}finally{m(g=>({...g,isSubmitting:!1}))}},P=async r=>{let c="";const g="test123";switch(r){case"berater":c="<EMAIL>";break;case"mentor":c="<EMAIL>";break;case"teamleiter":c="<EMAIL>";break;case"manager":c="<EMAIL>";break;case"admin":c="<EMAIL>";break;default:c="<EMAIL>"}h(c),d(g);try{await w(c,g)&&b("/")}catch(A){console.error("Quick login error:",A)}},E=[{role:"berater",label:"Berater",icon:O,color:"from-blue-500 to-blue-600"},{role:"mentor",label:"Mentor",icon:Se,color:"from-green-500 to-green-600"},{role:"teamleiter",label:"Teamleiter",icon:O,color:"from-purple-500 to-purple-600"},{role:"manager",label:"Manager",icon:O,color:"from-orange-500 to-orange-600"},{role:"admin",label:"Admin",icon:L,color:"from-red-500 to-red-600"}];return e.jsxs("form",{onSubmit:M,className:"space-y-6",children:[e.jsxs(Ae,{open:l,onOpenChange:j,children:[e.jsx(Te,{asChild:!0,children:e.jsxs(S,{type:"button",variant:"outline",className:"w-full justify-between text-sm text-gray-600 border-gray-200 hover:bg-gray-50 h-10",children:[e.jsx("span",{children:"Test-Accounts"}),e.jsx(ye,{className:`h-4 w-4 transition-transform ${l?"rotate-180":""}`})]})}),e.jsx(Fe,{className:"mt-3 animate-slide-in",children:e.jsxs("div",{className:"p-3 bg-gray-50 rounded-xl border space-y-2",children:[e.jsx("div",{className:"grid grid-cols-2 gap-2",children:E.slice(0,2).map(r=>{const c=r.icon;return e.jsxs(S,{type:"button",variant:"outline",onClick:()=>P(r.role),className:`h-10 justify-center text-xs bg-gradient-to-r ${r.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`,children:[e.jsx(c,{className:"h-3 w-3 mr-1"}),r.label]},r.role)})}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:E.slice(2,4).map(r=>{const c=r.icon;return e.jsxs(S,{type:"button",variant:"outline",onClick:()=>P(r.role),className:`h-10 justify-center text-xs bg-gradient-to-r ${r.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`,children:[e.jsx(c,{className:"h-3 w-3 mr-1"}),r.label]},r.role)})}),e.jsx("div",{className:"flex justify-center",children:E.slice(4,5).map(r=>{const c=r.icon;return e.jsxs(S,{type:"button",variant:"outline",onClick:()=>P(r.role),className:`h-10 w-24 justify-center text-xs bg-gradient-to-r ${r.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`,children:[e.jsx(c,{className:"h-3 w-3 mr-1"}),r.label]},r.role)})})]})})]}),u&&u.type!=="validation"&&e.jsxs("div",{className:"flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-xl",children:[e.jsx(U,{className:"h-5 w-5 text-red-500 flex-shrink-0"}),e.jsx("span",{className:"text-sm font-medium text-red-700",children:u.message})]}),e.jsx(F,{id:"email",label:"E-Mail oder Name",type:"text",value:s.emailOrName,onChange:N,onBlur:()=>k("emailOrName"),placeholder:"<EMAIL> oder Name",error:n.getFieldError("emailOrName")||((u==null?void 0:u.field)==="emailOrName"?u:null),required:!0,icon:e.jsx(O,{className:"h-5 w-5"}),autoComplete:"username","data-testid":"login-email"}),e.jsx(F,{id:"password",label:"Passwort",type:"password",value:s.password,onChange:I,onBlur:()=>k("password"),placeholder:"••••••••",error:n.getFieldError("password")||((u==null?void 0:u.field)==="password"?u:null),required:!0,icon:e.jsx(L,{className:"h-5 w-5"}),showPasswordToggle:!0,autoComplete:"current-password","data-testid":"login-password"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(ge,{id:"remember-me",checked:y,onCheckedChange:C,className:"data-[state=checked]:bg-red-500"}),e.jsx(le,{htmlFor:"remember-me",className:"text-sm font-medium text-gray-700 cursor-pointer",children:"Angemeldet bleiben"})]}),e.jsx("span",{className:"text-xs text-gray-500",children:y?"30 Tage":"24 Stunden"})]}),e.jsx(S,{type:"submit",className:f("w-full h-14 text-lg font-semibold rounded-xl transition-all duration-200 shadow-lg","bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800","hover:scale-105 hover:shadow-xl touch-manipulation","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100","min-h-[44px]"),disabled:v||s.isSubmitting||!s.emailOrName.trim()||!s.password.trim()||!n.validateAll(),"data-testid":"login-submit",children:v||s.isSubmitting?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"}),"Anmeldung..."]}):"Anmelden"})]})},Me=({validation:o,className:h})=>{const{strength:t,requirements:d,suggestions:b}=o,w={weak:"bg-red-500",fair:"bg-orange-500",good:"bg-yellow-500",strong:"bg-green-500",excellent:"bg-emerald-500"},v={weak:"Schwach",fair:"Ausreichend",good:"Gut",strong:"Stark",excellent:"Exzellent"},u={weak:"text-red-600",fair:"text-orange-600",good:"text-yellow-600",strong:"text-green-600",excellent:"text-emerald-600"},p=o.score/7*100;return e.jsxs("div",{className:f("space-y-3",h),children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Passwort-Stärke"}),e.jsx("span",{className:f("text-sm font-semibold",u[t]),children:v[t]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:f("h-2 rounded-full transition-all duration-300 ease-in-out",w[t]),style:{width:`${p}%`}})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Anforderungen:"}),e.jsxs("div",{className:"grid grid-cols-1 gap-1",children:[e.jsx(T,{met:d.minLength,text:"Mindestens 8 Zeichen"}),e.jsx(T,{met:d.hasUppercase,text:"Großbuchstaben (A-Z)"}),e.jsx(T,{met:d.hasLowercase,text:"Kleinbuchstaben (a-z)"}),e.jsx(T,{met:d.hasNumbers,text:"Zahlen (0-9)"}),e.jsx(T,{met:d.hasSpecialChars,text:"Sonderzeichen (!@#$%^&*)"}),e.jsx(T,{met:d.noCommonPatterns,text:"Keine häufigen Muster"}),e.jsx(T,{met:d.noPersonalInfo,text:"Keine persönlichen Daten"})]})]}),o.entropy>0&&e.jsxs("div",{className:"text-xs text-gray-500 mt-2",children:["Entropie: ",Math.round(o.entropy)," Bits"]}),b.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Verbesserungen:"}),e.jsx("ul",{className:"space-y-1",children:b.map((l,j)=>e.jsxs("li",{className:"text-sm text-gray-600 flex items-center",children:[e.jsx("span",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full mr-2 flex-shrink-0"}),l]},j))})]})]})},T=({met:o,text:h})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:f("flex items-center justify-center w-4 h-4 rounded-full transition-colors duration-200",o?"bg-green-500":"bg-gray-300"),children:o?e.jsx(Ce,{className:"w-2.5 h-2.5 text-white"}):e.jsx(je,{className:"w-2.5 h-2.5 text-gray-500"})}),e.jsx("span",{className:f("text-sm transition-colors duration-200",o?"text-green-600":"text-gray-600"),children:h})]}),Re=({name:o,setName:h,email:t,setEmail:d,password:b,setPassword:w,setActiveTab:v})=>{const{register:u,isLoading:p,error:l,clearError:j}=Z(),[y,C]=x.useState(""),[s,m]=x.useState({name:o,email:t,password:b,confirmPassword:"",isSubmitting:!1,currentStep:1,maxSteps:2}),n=se(),N=me(s.password);x.useEffect(()=>{m(a=>({...a,name:o,email:t,password:b}))},[o,t,b]);const I=a=>{h(a),m(i=>({...i,name:a})),j(),n.getFieldError("name")&&n.clearError("name")},k=a=>{d(a),m(i=>({...i,email:a})),j(),n.getFieldError("email")&&n.clearError("email")},M=a=>{w(a),m(i=>({...i,password:a})),j(),n.getFieldError("password")&&n.clearError("password")},P=a=>{C(a),m(i=>({...i,confirmPassword:a})),j(),n.getFieldError("confirmPassword")&&n.clearError("confirmPassword")},E=a=>{n.setTouched(a);let i=null;switch(a){case"name":i=V(s.name);break;case"email":i=q(s.email);break;case"password":N.strength==="weak"&&(i={field:"password",message:"Passwort ist zu schwach. Bitte folgen Sie den Empfehlungen."});break;case"confirmPassword":i=G(s.password,s.confirmPassword);break}i?n.setError(i):n.clearError(a)},r=()=>{const a=V(s.name),i=q(s.email);return!a&&!i&&s.name.trim()&&s.email.trim()},c=()=>{const a=V(s.name),i=q(s.email),D=N.strength==="weak",z=G(s.password,s.confirmPassword);return!a&&!i&&!D&&!z&&s.name.trim()&&s.email.trim()&&s.password.trim()&&s.confirmPassword.trim()},g=()=>{r()&&m(a=>({...a,currentStep:2}))},A=()=>{m(a=>({...a,currentStep:1}))},B=async a=>{if(a.preventDefault(),!!c()){m(i=>({...i,isSubmitting:!0}));try{(await u({name:s.name.trim(),email:s.email.toLowerCase().trim(),password:s.password,confirmPassword:s.confirmPassword})).success}catch(i){console.error("Registration error:",i)}finally{m(i=>({...i,isSubmitting:!1}))}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:f("w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors",s.currentStep>=1?"bg-red-500 text-white":"bg-gray-200 text-gray-500"),children:s.currentStep>1?e.jsx(ne,{className:"w-4 h-4"}):"1"}),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Persönliche Daten"})]}),e.jsx("div",{className:f("w-8 h-0.5 transition-colors",s.currentStep>=2?"bg-red-500":"bg-gray-200")}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:f("w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors",s.currentStep>=2?"bg-red-500 text-white":"bg-gray-200 text-gray-500"),children:"2"}),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Passwort"})]})]}),l&&l.type!=="validation"&&e.jsxs("div",{className:"flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-xl",children:[e.jsx(U,{className:"h-5 w-5 text-red-500 flex-shrink-0"}),e.jsx("span",{className:"text-sm font-medium text-red-700",children:l.message})]}),e.jsxs("form",{onSubmit:B,className:"space-y-6",children:[s.currentStep===1&&e.jsxs("div",{className:"space-y-4 animate-fade-in",children:[e.jsx(F,{id:"register-name",label:"Vollständiger Name",type:"text",value:s.name,onChange:I,onBlur:()=>E("name"),placeholder:"Max Mustermann",error:n.getFieldError("name")||((l==null?void 0:l.field)==="name"?l:null),required:!0,icon:e.jsx(O,{className:"h-5 w-5"}),autoComplete:"name",maxLength:50,"data-testid":"register-name"}),e.jsx(F,{id:"register-email",label:"E-Mail-Adresse",type:"email",value:s.email,onChange:k,onBlur:()=>E("email"),placeholder:"<EMAIL>",error:n.getFieldError("email")||((l==null?void 0:l.field)==="email"?l:null),required:!0,icon:e.jsx(Ee,{className:"h-5 w-5"}),autoComplete:"email","data-testid":"register-email"}),e.jsx(S,{type:"button",onClick:g,disabled:!r(),className:f("w-full h-14 text-lg font-semibold rounded-xl transition-all duration-200","bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800","hover:scale-105 shadow-lg hover:shadow-xl touch-manipulation","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100","min-h-[44px]"),"data-testid":"register-next",children:e.jsxs("span",{className:"flex items-center",children:["Weiter",e.jsx(Pe,{className:"ml-2 h-5 w-5"})]})})]}),s.currentStep===2&&e.jsxs("div",{className:"space-y-4 animate-fade-in",children:[e.jsx(F,{id:"register-password",label:"Passwort",type:"password",value:s.password,onChange:M,onBlur:()=>E("password"),placeholder:"••••••••",error:n.getFieldError("password")||((l==null?void 0:l.field)==="password"?l:null),required:!0,icon:e.jsx(L,{className:"h-5 w-5"}),showPasswordToggle:!0,autoComplete:"new-password","data-testid":"register-password"}),s.password&&e.jsx(Me,{validation:N,className:"mt-3"}),e.jsx(F,{id:"register-confirm-password",label:"Passwort bestätigen",type:"password",value:s.confirmPassword,onChange:P,onBlur:()=>E("confirmPassword"),placeholder:"••••••••",error:n.getFieldError("confirmPassword")||((l==null?void 0:l.field)==="confirmPassword"?l:null),required:!0,icon:e.jsx(L,{className:"h-5 w-5"}),showPasswordToggle:!0,autoComplete:"new-password","data-testid":"register-confirm-password"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(S,{type:"button",onClick:A,variant:"outline",className:f("flex-1 h-14 text-lg font-semibold rounded-xl transition-all duration-200","border-2 border-gray-300 hover:border-red-500 hover:text-red-600","touch-manipulation min-h-[44px]"),"data-testid":"register-back",children:[e.jsx(ke,{className:"mr-2 h-5 w-5"}),"Zurück"]}),e.jsx(S,{type:"submit",disabled:!c()||p||s.isSubmitting,className:f("flex-1 h-14 text-lg font-semibold rounded-xl transition-all duration-200","bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800","hover:scale-105 shadow-lg hover:shadow-xl touch-manipulation","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100","min-h-[44px]"),"data-testid":"register-submit",children:p||s.isSubmitting?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"}),"Registrierung..."]}):"Registrieren"})]})]})]})]})},Oe=()=>e.jsxs(re,{className:"text-center pb-6 pt-10 bg-gradient-to-b from-red-50 to-white",children:[e.jsx("div",{className:"flex items-center justify-center mb-6",children:e.jsxs("div",{className:"relative",children:[e.jsx(X,{className:"h-12 w-12 text-red-600 animate-pulse"}),e.jsx("div",{className:"absolute inset-0 h-12 w-12 text-red-400 animate-ping opacity-30",children:e.jsx(X,{className:"h-12 w-12"})})]})}),e.jsx(te,{className:"text-5xl font-bold bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent",children:"Laufliste"}),e.jsx("p",{className:"text-gray-500 mt-2 text-lg",children:"Willkommen zurück"})]});class Le extends x.Component{constructor(t){super(t);R(this,"logErrorToService",(t,d)=>{const b={message:t.message,stack:t.stack,componentStack:d.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href,userId:localStorage.getItem("currentUser")?JSON.parse(localStorage.getItem("currentUser")||"{}").id:"anonymous"};console.log("Error logged to service:",b)});R(this,"handleRetry",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});R(this,"handleGoHome",()=>{localStorage.removeItem("currentUser"),localStorage.removeItem("sessionExpiry"),localStorage.removeItem("rememberMe"),window.location.href="/login"});R(this,"handleReload",()=>{window.location.reload()});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t,errorInfo:null}}componentDidCatch(t,d){console.error("Authentication Error Boundary caught an error:",t,d),this.setState({error:t,errorInfo:d}),this.props.onError&&this.props.onError(t,d),this.logErrorToService(t,d)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center p-4",children:e.jsxs(ae,{className:"w-full max-w-md mx-auto border-0 shadow-2xl rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95",children:[e.jsxs(re,{className:"text-center pb-6 pt-10 bg-gradient-to-b from-red-50 to-white",children:[e.jsx("div",{className:"flex items-center justify-center mb-6",children:e.jsxs("div",{className:"relative",children:[e.jsx(K,{className:"h-12 w-12 text-red-600"}),e.jsx("div",{className:"absolute inset-0 h-12 w-12 text-red-400 animate-ping opacity-30",children:e.jsx(K,{className:"h-12 w-12"})})]})}),e.jsx(te,{className:"text-2xl font-bold text-red-600",children:"Authentifizierungsfehler"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Es ist ein unerwarteter Fehler aufgetreten"})]}),e.jsxs(oe,{className:"px-8 pt-6 pb-10 space-y-6",children:[!1,e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("p",{className:"text-gray-700",children:"Die Anwendung konnte nicht ordnungsgemäß geladen werden. Bitte versuchen Sie eine der folgenden Optionen:"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(S,{onClick:this.handleRetry,className:"w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl",children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Erneut versuchen"]}),e.jsxs(S,{onClick:this.handleReload,variant:"outline",className:"w-full h-14 text-lg font-semibold border-2 border-gray-300 hover:border-red-500 hover:text-red-600 rounded-xl transition-all duration-200",children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Seite neu laden"]}),e.jsxs(S,{onClick:this.handleGoHome,variant:"ghost",className:"w-full h-14 text-lg font-semibold text-gray-600 hover:text-red-600 rounded-xl transition-all duration-200",children:[e.jsx(he,{className:"mr-2 h-5 w-5"}),"Zur Anmeldung"]})]})]}),e.jsxs("div",{className:"text-center pt-4 border-t border-gray-200",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Falls das Problem weiterhin besteht, wenden Sie sich an den Support."}),e.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:["Fehler-ID: ",Date.now().toString(36)]})]})]})]})}):this.props.children}}const ts=()=>{const[o,h]=x.useState("login"),[t,d]=x.useState(""),[b,w]=x.useState(""),[v,u]=x.useState(""),{isAuthenticated:p,isLoading:l,refreshSession:j,sessionExpiry:y}=Z(),C=ee(),s=x.useCallback(async()=>{try{await j()&&p&&C("/",{replace:!0})}catch(m){console.error("Session refresh failed:",m)}},[j,p,C]);return x.useEffect(()=>{y&&s();const m=setInterval(()=>{y&&s()},5*60*1e3);return()=>clearInterval(m)},[s,y]),x.useEffect(()=>{p&&!l&&C("/",{replace:!0})},[p,l,C]),l?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"w-12 h-12 border-4 border-red-600 border-t-transparent rounded-full animate-spin"}),e.jsx("p",{className:"text-gray-600 font-medium",children:"Anmeldung wird überprüft..."})]})}):p?null:e.jsx(Le,{children:e.jsxs("div",{className:"min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-red-600 via-red-500 to-pink-600 px-4 py-6 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[e.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse-slow"}),e.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse-slow",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse-slow",style:{animationDelay:"2s"}})]}),e.jsx("div",{className:"w-full max-w-md mx-auto",children:e.jsxs(ae,{className:"w-full border-0 shadow-2xl rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 animate-fade-in",children:[e.jsx(Oe,{}),e.jsxs(xe,{value:o,onValueChange:m=>h(m),children:[e.jsxs(ue,{className:"grid grid-cols-2 w-full rounded-none bg-gray-50 h-16",children:[e.jsx(Q,{value:"login",className:"data-[state=active]:bg-red-500 data-[state=active]:text-white py-4 rounded-none text-lg font-semibold transition-all duration-300 hover:bg-red-100 min-h-[44px] touch-manipulation",children:"Anmelden"}),e.jsx(Q,{value:"register",className:"data-[state=active]:bg-red-500 data-[state=active]:text-white py-4 rounded-none text-lg font-semibold transition-all duration-300 hover:bg-red-100 min-h-[44px] touch-manipulation",children:"Registrieren"})]}),e.jsxs(oe,{className:"px-8 pt-8 pb-10",children:[e.jsx(J,{value:"login",className:"animate-fade-in",children:e.jsx(Ie,{email:t,setEmail:d,password:b,setPassword:w})}),e.jsx(J,{value:"register",className:"animate-fade-in",children:e.jsx(Re,{name:v,setName:u,email:t,setEmail:d,password:b,setPassword:w,setActiveTab:h})})]})]})]})})]})})};export{ts as default};
