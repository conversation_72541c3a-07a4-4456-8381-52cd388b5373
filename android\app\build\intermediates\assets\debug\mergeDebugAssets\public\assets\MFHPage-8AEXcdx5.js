import{j as r}from"./index-Cmt5neWh.js";import{M as t}from"./MainLayout-wyzz138D.js";import{M as i}from"./ModernAddressForm-C2Sa-x79.js";import"./input-BK13BBqa.js";import"./label-NwAA2N0T.js";import"./check-abM7k-xd.js";import"./EFHVisitTracker-BC_kU7m6.js";import"./Button-ETlvKXsU.js";import"./loader-circle-Brkx1kW_.js";import"./Card-CELKqcN7.js";import"./badge-XkNoLG2o.js";import"./SimpleStatusButtons-CtnyXuDf.js";import"./clock-DhYcPjhn.js";import"./dialog-BFTaoFLK.js";import"./popover-BPm3A8JC.js";import"./subDays-BLJlWEqr.js";import"./useSwipeGestures-49dHBwDT.js";import"./zap-BNKcOEAu.js";import"./user-x-DrICOSZg.js";import"./geocodingService-r9QfKNdP.js";import"./navigation-B6ya6VuX.js";const b=()=>r.jsx(t,{title:"Mehrfamilienhaus",children:r.jsx("div",{className:"min-h-full flex items-start justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50",children:r.jsx("div",{className:"w-full max-w-2xl",children:r.jsx(i,{houseType:"MFH"})})})});export{b as default};
