import{j as e,k as i,C as o,i as d,l as m}from"./index-Cmt5neWh.js";import{S as r}from"./Button-ETlvKXsU.js";import{C as c}from"./circle-check-big-DK6RP7UF.js";import{S as x}from"./SimpleStatusButtons-CtnyXuDf.js";import"./loader-circle-Brkx1kW_.js";const u=({currentStatus:t,onStatusUpdate:a,isUpdating:s})=>{const n=["Angetroffen → Sale","Angetroffen → Termin","Angetroffen → Kein Interesse","N/A"];return console.log("StatusButtons rendered with:",{currentStatus:t,isUpdating:s,statusOptions:n}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-neutral-800 mb-6 text-center",children:"Was ist passiert?"}),e.jsx("div",{className:"grid grid-cols-1 gap-4",children:n.map((l,h)=>e.jsxs("div",{className:"w-full",children:[e.jsx(r,{status:l,onClick:()=>a(l),loading:s,disabled:t===l}),t===l&&e.jsxs("div",{className:"flex items-center justify-center mt-2 text-sm text-green-600",children:[e.jsx(c,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:"Aktueller Status"})]})]},l))})]})},N=()=>{const t=(s,n)=>{console.log("Status clicked:",s,"Comment:",n),alert(`Status clicked: ${s}${n?`
Kommentar: ${n}`:""}`)},a=["Angetroffen → Sale","Angetroffen → Termin","Angetroffen → Kein Interesse","N/A"];return e.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:e.jsxs(i,{className:"max-w-md mx-auto",children:[e.jsx(o,{children:e.jsx(d,{children:"Button Test - Debug"})}),e.jsx(m,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Status Buttons Test"}),a.map(s=>e.jsxs("div",{className:"border p-2 rounded",children:[e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["Status: ",s]}),e.jsx(r,{status:s,onClick:()=>t(s),loading:!1,disabled:!1})]},s)),e.jsxs("div",{className:"mt-8",children:[e.jsx("h4",{className:"text-md font-semibold mb-2",children:"Simple StatusButtons Test"}),e.jsx(x,{currentStatus:"N/A",onStatusUpdate:t,isUpdating:!1})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h4",{className:"text-md font-semibold mb-2",children:"Original StatusButtons Component Test"}),e.jsx(u,{currentStatus:"N/A",onStatusUpdate:t,isUpdating:!1})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h4",{className:"text-md font-semibold mb-2",children:"Simple Test Buttons"}),e.jsx("button",{className:"w-full h-16 bg-blue-600 text-white rounded-xl mb-2 hover:bg-blue-700",onClick:()=>alert("Simple button works!"),children:"Simple Blue Button"}),e.jsx("button",{className:"w-full h-16 bg-green-600 text-white rounded-xl mb-2 hover:bg-green-700",onClick:()=>alert("Green button works!"),children:"Simple Green Button"})]})]})})]})})};export{N as default};
