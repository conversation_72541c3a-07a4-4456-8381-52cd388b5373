import{r,j as t,H as g}from"./index-Cmt5neWh.js";import{u as w,B as j,M as N}from"./MainLayout-wyzz138D.js";import{T as S,a as T,b as u,c as h}from"./tabs-BJh52NhZ.js";import{M as p}from"./ModernAddressForm-C2Sa-x79.js";import{t as d}from"./useSwipeGestures-49dHBwDT.js";import"./input-BK13BBqa.js";import"./index-C8JwcrUT.js";import"./label-NwAA2N0T.js";import"./check-abM7k-xd.js";import"./EFHVisitTracker-BC_kU7m6.js";import"./Button-ETlvKXsU.js";import"./loader-circle-Brkx1kW_.js";import"./Card-CELKqcN7.js";import"./badge-XkNoLG2o.js";import"./SimpleStatusButtons-CtnyXuDf.js";import"./clock-DhYcPjhn.js";import"./dialog-BFTaoFLK.js";import"./popover-BPm3A8JC.js";import"./subDays-BLJlWEqr.js";import"./zap-BNKcOEAu.js";import"./user-x-DrICOSZg.js";import"./geocodingService-r9QfKNdP.js";import"./navigation-B6ya6VuX.js";const y=(e,s=50)=>{const i=r.useRef(null),o=r.useRef(null),c=a=>{o.current=null,i.current={x:a.targetTouches[0].clientX,y:a.targetTouches[0].clientY}},l=a=>{o.current={x:a.targetTouches[0].clientX,y:a.targetTouches[0].clientY}},m=()=>{if(!i.current||!o.current)return;const a=i.current.x-o.current.x,n=i.current.y-o.current.y,x=a>s,v=a<-s,f=n>s,b=n<-s;Math.abs(a)>Math.abs(n)?(x&&e.onSwipeLeft&&e.onSwipeLeft(),v&&e.onSwipeRight&&e.onSwipeRight()):(f&&e.onSwipeUp&&e.onSwipeUp(),b&&e.onSwipeDown&&e.onSwipeDown())};return r.useEffect(()=>(document.addEventListener("touchstart",c),document.addEventListener("touchmove",l),document.addEventListener("touchend",m),()=>{document.removeEventListener("touchstart",c),document.removeEventListener("touchmove",l),document.removeEventListener("touchend",m)}),[e]),null},E=()=>{const{setOpenMobile:e}=w(),[s,i]=r.useState("efh");return y({onSwipeRight:()=>{e(!0),d("light")},onSwipeLeft:()=>{s==="efh"?(i("mfh"),d("light")):(i("efh"),d("light"))}}),t.jsx("div",{className:"min-h-full w-full mobile-container bg-gradient-to-br from-red-50 via-white to-red-50",children:t.jsx("div",{className:"w-full h-full",children:t.jsxs(S,{value:s,onValueChange:i,className:"w-full h-full",children:[t.jsxs("div",{className:"relative bg-gradient-to-r from-red-500 via-red-600 to-red-500 shadow-2xl",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-red-400/20 via-transparent to-red-400/20"}),t.jsxs(T,{className:"mobile-tabs grid-cols-2 relative bg-transparent rounded-none border-0 m-0 p-2 gap-2 h-20 md:h-24",children:[t.jsx(u,{value:"efh",className:"mobile-tab group relative py-3 px-4 md:py-4 md:px-8 rounded-2xl font-bold transition-all duration-300 transform active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm touch-feedback","data-primary":"true",children:t.jsxs("div",{className:"flex items-center gap-2 md:gap-3",children:[t.jsx("div",{className:"p-1.5 md:p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300",children:t.jsx(g,{className:"h-5 w-5 md:h-6 md:w-6 text-white group-data-[state=active]:text-white"})}),t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"text-lg md:text-xl font-bold",children:"EFH"}),t.jsx("div",{className:"text-xs md:text-sm opacity-80 font-medium hidden sm:block",children:"Einfamilienhaus"})]})]})}),t.jsx(u,{value:"mfh",className:"mobile-tab group relative py-3 px-4 md:py-4 md:px-8 rounded-2xl font-bold transition-all duration-300 transform active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm touch-feedback","data-primary":"true",children:t.jsxs("div",{className:"flex items-center gap-2 md:gap-3",children:[t.jsx("div",{className:"p-1.5 md:p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300",children:t.jsx(j,{className:"h-5 w-5 md:h-6 md:w-6 text-white group-data-[state=active]:text-white"})}),t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"text-lg md:text-xl font-bold",children:"MFH"}),t.jsx("div",{className:"text-xs md:text-sm opacity-80 font-medium hidden sm:block",children:"Mehrfamilienhaus"})]})]})})]}),t.jsx("div",{className:"absolute top-2 left-4 w-2 h-2 bg-white/30 rounded-full hidden sm:block"}),t.jsx("div",{className:"absolute top-4 right-6 w-1 h-1 bg-white/40 rounded-full hidden sm:block"}),t.jsx("div",{className:"absolute bottom-3 left-8 w-1.5 h-1.5 bg-white/25 rounded-full hidden sm:block"})]}),t.jsx(h,{value:"efh",className:"p-0 m-0 animate-fade-in",children:t.jsx(p,{houseType:"EFH"})}),t.jsx(h,{value:"mfh",className:"p-0 m-0 animate-fade-in",children:t.jsx(p,{houseType:"MFH"})})]})})})},W=()=>t.jsx(N,{title:"Laufliste",children:t.jsx(E,{})});export{W as default};
