import{c as Be,r as n,j as e,x as Te,A as re,B as S,T as P,k as W,l as J,D as Se,C as Ue,i as Ee,y as ue,E as Gs,J as D,b as _e,F as $e,S as Ls,a as F,G as Ie,I as Hs}from"./index-Cmt5neWh.js";import{S as V,b as Oe,n as Me,o as qs,O as Zs,W as Ks,p as Ws,q as Js,D as Xs,r as Fe,P as Ys,R as Qs,M as Ae,U as ea}from"./MainLayout-wyzz138D.js";import{c as sa,a as aa,r as ta,u as la}from"./functions-DcGEt8N_.js";import{T as ra,a as na,b as Re,c as q,d as ia,e as Z}from"./table-Bp6KGmPn.js";import{B as w}from"./badge-XkNoLG2o.js";import{E as Pe}from"./eye-DwLHs0eg.js";import{S as Ve,T as Ge}from"./trash-2-BcpLsksG.js";import{U as ca}from"./user-check-BIUVRotN.js";import{U as Le}from"./user-x-DrICOSZg.js";import{R as He}from"./rotate-ccw-YT9XFzuk.js";import{C as oa,S as z,a as B,b as T,c as U,d as j}from"./select-IVIdgARa.js";import{C as da}from"./chevron-down-I8tOk39n.js";import{D as ne,b as ie,c as ce,d as oe,a as ma}from"./dialog-BFTaoFLK.js";import{L as v}from"./label-NwAA2N0T.js";import{U as pe,I as M,X as qe,c as xa,a as ha}from"./input-BK13BBqa.js";import{M as ge}from"./mail-4oZ8O593.js";import{C as ua}from"./clock-DhYcPjhn.js";import{S as Ze}from"./switch-DOMi03D3.js";import{P as Ke}from"./plus-C74OdLeW.js";import{S as pa}from"./search-BlRJyMtb.js";import{F as ga}from"./filter-DvioeHYD.js";import"./client-DbI4l5kI.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./check-abM7k-xd.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ja=Be("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=Be("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),va=({users:a,userProfiles:t,teams:s,isMobile:l,onViewUser:d,onEditUser:m,onResetUserStats:p,onDeleteUser:i,onBlockUser:o,onUnblockUser:c})=>{const[g,k]=n.useState("name"),[x,N]=n.useState("asc"),y=h=>{g===h?N(x==="asc"?"desc":"asc"):(k(h),N("asc"))},E=()=>[...a].sort((h,u)=>{let b,C;switch(g){case"name":b=h.name.toLowerCase(),C=u.name.toLowerCase();break;case"email":b=h.email.toLowerCase(),C=u.email.toLowerCase();break;case"role":b=h.role.toLowerCase(),C=u.role.toLowerCase();break;case"team":{const A=s.find(X=>X.id===h.teamId),I=s.find(X=>X.id===u.teamId);b=((A==null?void 0:A.name)||"").toLowerCase(),C=((I==null?void 0:I.name)||"").toLowerCase();break}default:return 0}return x==="asc"?b.localeCompare(C):C.localeCompare(b)}),f=({field:h,children:u})=>e.jsxs("button",{onClick:()=>y(h),className:"flex items-center gap-1 hover:text-blue-600 transition-colors",children:[u,g===h&&(x==="asc"?e.jsx(oa,{className:"h-3 w-3"}):e.jsx(da,{className:"h-3 w-3"}))]}),R=E();return e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs(ra,{children:[e.jsx(na,{children:e.jsxs(Re,{className:"border-b-2",children:[e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:e.jsx(f,{field:"name",children:"Name"})}),e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:e.jsx(f,{field:"email",children:"E-Mail"})}),e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:e.jsx(f,{field:"role",children:"Rolle"})}),e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:e.jsx(f,{field:"team",children:"Team"})}),e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Status"}),e.jsx(q,{className:`${l?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Aktionen"})]})}),e.jsx(ia,{children:R.map((h,u)=>{const b=t.find(A=>A.id===h.id),C=s.find(A=>A.id===h.teamId);return e.jsxs(Re,{className:`hover:bg-blue-50/70 transition-colors animate-fade-in ${u%2===0?"bg-gray-50/30":"bg-white"}`,children:[e.jsx(Z,{className:`${l?"text-xs":"text-sm"} font-medium text-gray-800 py-3`,children:h.name}),e.jsx(Z,{className:`${l?"text-xs":"text-sm"} text-gray-700 py-3`,children:e.jsx("div",{className:"truncate max-w-[200px]",title:h.email,children:h.email})}),e.jsx(Z,{className:"py-3",children:e.jsx("div",{className:"flex flex-wrap gap-1",children:Te(h)?e.jsx(e.Fragment,{children:re(h).map((A,I)=>e.jsxs(w,{variant:I===0?"default":"outline",className:"text-xs flex items-center gap-1",children:[I===0&&e.jsx(V,{className:"h-2 w-2"}),A,I===0&&e.jsx("span",{className:"text-xs opacity-75",children:"(P)"})]},A))}):e.jsx(w,{variant:"outline",className:"capitalize text-xs",children:h.role})})}),e.jsx(Z,{className:`${l?"text-xs":"text-sm"} text-gray-700 py-3`,children:(C==null?void 0:C.name)||h.teamId||e.jsx("span",{className:"text-gray-400 italic",children:"Kein Team"})}),e.jsx(Z,{className:"py-3",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[h.isBlocked?e.jsx(w,{variant:"destructive",className:"text-xs",children:"Blockiert"}):(b==null?void 0:b.is_active)!==!1?e.jsx(w,{variant:"default",className:"text-xs",children:"Aktiv"}):e.jsx(w,{variant:"secondary",className:"text-xs",children:"Inaktiv"}),h.deletedAt&&e.jsx(w,{variant:"outline",className:"text-xs text-red-600 border-red-300",children:"Gelöscht"})]})}),e.jsx(Z,{className:"py-3",children:e.jsxs("div",{className:`flex gap-1 ${l?"flex-wrap":""}`,children:[e.jsx(S,{variant:"outline",size:"sm",onClick:()=>d(h),className:"text-blue-600 border-blue-200 hover:bg-blue-50 min-h-[44px] min-w-[44px] p-0",title:"Benutzer anzeigen",children:e.jsx(Pe,{className:"h-3 w-3"})}),e.jsx(S,{variant:"outline",size:"sm",onClick:()=>m(h),className:"text-green-600 border-green-200 hover:bg-green-50 min-h-[44px] min-w-[44px] p-0",title:"Benutzer bearbeiten",children:e.jsx(Ve,{className:"h-3 w-3"})}),h.isBlocked?e.jsx(S,{variant:"outline",size:"sm",onClick:()=>c(h),className:"text-green-600 border-green-200 hover:bg-green-50 min-h-[44px] min-w-[44px] p-0",title:"Benutzer entsperren",children:e.jsx(ca,{className:"h-3 w-3"})}):e.jsx(S,{variant:"outline",size:"sm",onClick:()=>o(h),className:"text-orange-600 border-orange-200 hover:bg-orange-50 min-h-[44px] min-w-[44px] p-0",title:"Benutzer blockieren",children:e.jsx(Le,{className:"h-3 w-3"})}),e.jsx(S,{variant:"outline",size:"sm",onClick:()=>p(h),className:"text-purple-600 border-purple-200 hover:bg-purple-50 min-h-[44px] min-w-[44px] p-0",title:"Statistiken zurücksetzen",children:e.jsx(He,{className:"h-3 w-3"})}),e.jsx(S,{variant:"outline",size:"sm",onClick:()=>i(h),className:"text-red-600 border-red-200 hover:bg-red-50 min-h-[44px] min-w-[44px] p-0",title:"Benutzer löschen",children:e.jsx(Ge,{className:"h-3 w-3"})})]})})]},h.id)})})]}),R.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("p",{children:"Keine Benutzer gefunden."}),e.jsx("p",{className:"text-sm",children:"Versuchen Sie, Ihre Filter zu ändern."})]})]})},Na=({isOpen:a,onOpenChange:t,user:s})=>s?e.jsx(ne,{open:a,onOpenChange:t,children:e.jsxs(ie,{className:"sm:max-w-[500px]",children:[e.jsx(ce,{children:e.jsxs(oe,{className:"flex items-center gap-2",children:[e.jsx(Pe,{className:"h-5 w-5 text-blue-600"}),"Benutzer Details: ",s.name]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Name"}),e.jsx("p",{className:"text-sm",children:s.name})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"E-Mail"}),e.jsx("p",{className:"text-sm",children:s.email})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Rolle"}),e.jsx(w,{variant:"outline",className:"capitalize",children:s.role})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Status"}),e.jsx(w,{variant:s.is_active!==!1?"default":"secondary",children:s.is_active!==!1?"Aktiv":"Inaktiv"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"Statistiken"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Gesamte Besuche"}),e.jsx("p",{className:"text-lg font-semibold text-blue-600",children:s.total_visits||0})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Gesamte Verkäufe"}),e.jsx("p",{className:"text-lg font-semibold text-green-600",children:s.total_sales||0})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Resets"}),e.jsxs("p",{className:"text-sm",children:[s.stats_reset_count||0,"x"]})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-600",children:"Letzter Reset"}),e.jsx("p",{className:"text-sm",children:s.last_stats_reset?new Date(s.last_stats_reset).toLocaleDateString("de-DE"):"Nie"})]})]})]})]})]})}):null,ba=a=>{switch(a){case"delete":return{icon:Ge,iconColor:"text-red-600",titleColor:"text-red-700",confirmVariant:"destructive",confirmText:"Löschen"};case"block":return{icon:Le,iconColor:"text-orange-600",titleColor:"text-orange-700",confirmVariant:"destructive",confirmText:"Blockieren"};case"unblock":return{icon:V,iconColor:"text-green-600",titleColor:"text-green-700",confirmVariant:"default",confirmText:"Entsperren"};case"danger":return{icon:P,iconColor:"text-red-600",titleColor:"text-red-700",confirmVariant:"destructive",confirmText:"Bestätigen"};default:return{icon:P,iconColor:"text-yellow-600",titleColor:"text-yellow-700",confirmVariant:"default",confirmText:"Bestätigen"}}},je=({isOpen:a,onOpenChange:t,title:s,description:l,confirmText:d,cancelText:m="Abbrechen",type:p="warning",onConfirm:i,isLoading:o=!1,children:c})=>{const g=Oe(),{icon:k,iconColor:x,titleColor:N,confirmVariant:y,confirmText:E}=ba(p),f=()=>{i()},R=()=>{o||t(!1)};return e.jsx(ne,{open:a,onOpenChange:t,children:e.jsxs(ie,{className:`sm:max-w-[425px] ${g?"mx-4":""}`,children:[e.jsx(ce,{children:e.jsxs(oe,{className:`flex items-center gap-2 ${N}`,children:[e.jsx(k,{className:`h-5 w-5 ${x}`}),s]})}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsx("p",{className:"text-gray-600 leading-relaxed",children:l}),c&&e.jsx("div",{className:"border-t pt-4",children:c})]}),e.jsxs("div",{className:"flex gap-3 justify-end",children:[e.jsx(S,{variant:"outline",onClick:R,disabled:o,className:"min-h-[44px] px-6",children:m}),e.jsx(S,{variant:y,onClick:f,disabled:o,className:"min-h-[44px] px-6",children:o?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Verarbeite..."]}):d||E})]})]})})},ya=a=>e.jsx(je,{...a,type:"delete"}),wa=a=>e.jsx(je,{...a,type:"block"}),ka=a=>e.jsx(je,{...a,type:"unblock"}),Ca=({isOpen:a,onOpenChange:t,user:s,onConfirm:l,isLoading:d=!1})=>{const[m,p]=n.useState("soft"),[i,o]=n.useState(""),[c,g]=n.useState("");if(!s)return null;const k=()=>{m==="hard"&&c!==s.name||l(m,i)},x=()=>{d||(p("soft"),o(""),g(""),t(!1))};return m==="soft"||m==="hard"&&s.name,e.jsx(ya,{isOpen:a,onOpenChange:x,title:`Benutzer ${m==="soft"?"deaktivieren":"löschen"}`,description:m==="soft"?`Möchten Sie den Benutzer "${s.name}" deaktivieren? Der Benutzer kann sich nicht mehr anmelden, aber alle Daten bleiben erhalten.`:`WARNUNG: Diese Aktion löscht den Benutzer "${s.name}" und alle zugehörigen Daten unwiderruflich aus dem System.`,confirmText:m==="soft"?"Deaktivieren":"Endgültig löschen",onConfirm:k,isLoading:d,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(W,{className:"bg-gray-50 border-gray-200",children:e.jsx(J,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(pe,{className:"h-5 w-5 text-gray-600 mt-0.5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:s.name}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-3 w-3"}),e.jsx("span",{className:"truncate",children:s.email})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-3 w-3"}),e.jsx(w,{variant:"outline",className:"text-xs",children:s.role})]})]})]})]})})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(v,{children:"Löschungstyp"}),e.jsxs(z,{value:m,onValueChange:N=>p(N),children:[e.jsx(B,{className:"min-h-[44px]",children:e.jsx(T,{})}),e.jsxs(U,{children:[e.jsx(j,{value:"soft",children:e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("span",{className:"font-medium",children:"Deaktivieren (Empfohlen)"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Benutzer wird deaktiviert, Daten bleiben erhalten"})]})}),e.jsx(j,{value:"hard",children:e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("span",{className:"font-medium text-red-600",children:"Endgültig löschen"}),e.jsx("span",{className:"text-xs text-red-500",children:"Benutzer und alle Daten werden unwiderruflich gelöscht"})]})})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{htmlFor:"delete-reason",children:["Grund ",m==="soft"?"(Optional)":"(Erforderlich)"]}),e.jsx(M,{id:"delete-reason",value:i,onChange:N=>o(N.target.value),placeholder:`Grund für ${m==="soft"?"Deaktivierung":"Löschung"} eingeben...`,className:"min-h-[44px]"})]}),m==="hard"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(v,{htmlFor:"confirmation-text",className:"text-red-700",children:"Bestätigung erforderlich"}),e.jsxs("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-start gap-2 mb-2",children:[e.jsx(P,{className:"h-4 w-4 text-red-600 mt-0.5 flex-shrink-0"}),e.jsxs("p",{className:"text-sm text-red-700",children:["Geben Sie den Benutzernamen ",e.jsxs("strong",{children:['"',s.name,'"']})," ein, um die endgültige Löschung zu bestätigen."]})]}),e.jsx(M,{id:"confirmation-text",value:c,onChange:N=>g(N.target.value),placeholder:s.name,className:"min-h-[44px] border-red-300 focus:border-red-500"})]})]}),m==="hard"&&e.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(P,{className:"h-4 w-4 text-red-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-red-700",children:[e.jsx("p",{className:"font-medium mb-1",children:"Diese Aktion kann nicht rückgängig gemacht werden!"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-xs",children:[e.jsx("li",{children:"Alle Benutzerdaten werden gelöscht"}),e.jsx("li",{children:"Besuchshistorie geht verloren"}),e.jsx("li",{children:"Zugewiesene Teams werden aktualisiert"}),e.jsx("li",{children:"Statistiken werden bereinigt"})]})]})]})})]})})},ze={policy_violation:"Verstoß gegen Richtlinien",security_concern:"Sicherheitsbedenken",inactive_account:"Inaktives Konto",administrative:"Administrative Gründe",other:"Sonstiges"},Da={temporary_1h:"1 Stunde",temporary_24h:"24 Stunden",temporary_7d:"7 Tage",temporary_30d:"30 Tage",permanent:"Dauerhaft"},Sa=({isOpen:a,onOpenChange:t,user:s,action:l,onConfirm:d,isLoading:m=!1})=>{const[p,i]=n.useState("administrative"),[o,c]=n.useState("temporary_24h"),[g,k]=n.useState(""),[x,N]=n.useState("");if(!s)return null;const y=()=>{if(l==="block"){const f=p==="other"?g:ze[p];d(f,o,g)}else d(x)},E=()=>{m||(i("administrative"),c("temporary_24h"),k(""),N(""),t(!1))};return l==="unblock"?x.trim().length>0:p!=="other"||g.trim().length>0,l==="unblock"?e.jsx(ka,{isOpen:a,onOpenChange:E,title:"Benutzer entsperren",description:`Möchten Sie den Benutzer "${s.name}" entsperren? Der Benutzer kann sich danach wieder normal anmelden.`,confirmText:"Entsperren",onConfirm:y,isLoading:m,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(W,{className:"bg-gray-50 border-gray-200",children:e.jsx(J,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(pe,{className:"h-5 w-5 text-gray-600 mt-0.5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:s.name}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-3 w-3"}),e.jsx("span",{className:"truncate",children:s.email})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-3 w-3"}),e.jsx(w,{variant:"outline",className:"text-xs",children:s.role})]})]})]})]})})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(v,{htmlFor:"unblock-reason",children:"Grund für Entsperrung"}),e.jsx(M,{id:"unblock-reason",value:x,onChange:f=>N(f.target.value),placeholder:"Grund für die Entsperrung eingeben...",className:"min-h-[44px]"})]})]})}):e.jsx(wa,{isOpen:a,onOpenChange:E,title:"Benutzer blockieren",description:`Möchten Sie den Benutzer "${s.name}" blockieren? Der Benutzer kann sich während der Sperrung nicht anmelden.`,confirmText:"Blockieren",onConfirm:y,isLoading:m,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(W,{className:"bg-gray-50 border-gray-200",children:e.jsx(J,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(pe,{className:"h-5 w-5 text-gray-600 mt-0.5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:s.name}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-3 w-3"}),e.jsx("span",{className:"truncate",children:s.email})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-3 w-3"}),e.jsx(w,{variant:"outline",className:"text-xs",children:s.role})]})]})]})]})})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(v,{children:"Grund für Blockierung"}),e.jsxs(z,{value:p,onValueChange:f=>i(f),children:[e.jsx(B,{className:"min-h-[44px]",children:e.jsx(T,{})}),e.jsx(U,{children:Object.entries(ze).map(([f,R])=>e.jsx(j,{value:f,children:R},f))})]})]}),p==="other"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(v,{htmlFor:"custom-reason",children:"Benutzerdefinierter Grund"}),e.jsx(M,{id:"custom-reason",value:g,onChange:f=>k(f.target.value),placeholder:"Grund für die Blockierung eingeben...",className:"min-h-[44px]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(ua,{className:"h-4 w-4"}),"Sperrdauer"]}),e.jsxs(z,{value:o,onValueChange:f=>c(f),children:[e.jsx(B,{className:"min-h-[44px]",children:e.jsx(T,{})}),e.jsx(U,{children:Object.entries(Da).map(([f,R])=>e.jsx(j,{value:f,children:e.jsxs("div",{className:"flex items-center gap-2",children:[f==="permanent"&&e.jsx(P,{className:"h-3 w-3 text-red-500"}),e.jsx("span",{className:f==="permanent"?"text-red-600 font-medium":"",children:R})]})},f))})]})]}),o==="permanent"&&e.jsx("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(P,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-orange-700",children:[e.jsx("p",{className:"font-medium mb-1",children:"Dauerhafte Sperrung"}),e.jsx("p",{children:"Der Benutzer wird dauerhaft gesperrt und kann nur durch einen Administrator entsperrt werden."})]})]})})]})})},Aa=({user:a,currentUser:t,onUpdateUser:s})=>{const[l,d]=n.useState(!1),[m,p]=n.useState(""),[i,o]=n.useState(a.isMultiRole||!1),[c,g]=n.useState(re(a)),k=["berater","mentor","teamleiter","gebietsmanager","admin"],x=re(a),N=Te(a),y=()=>{if(!m)return;const u=[...c];u.includes(m)?D.error("Diese Rolle ist bereits zugewiesen"):(u.push(m),Gs(u)?(g(u),p("")):D.error("Diese Rollenkombination ist nicht gültig"))},E=u=>{if(c.length<=1){D.error("Mindestens eine Rolle muss zugewiesen bleiben");return}g(c.filter(b=>b!==u))},f=()=>{if(c.length===0){D.error("Mindestens eine Rolle muss zugewiesen sein");return}if(!c.every(C=>Se(t,C))){D.error("Sie haben nicht die Berechtigung, alle ausgewählten Rollen zuzuweisen");return}const b={...a,role:c[0],roles:i&&c.length>1?c:void 0,isMultiRole:i&&c.length>1};s(b),d(!1),D.success("Benutzerrollen wurden erfolgreich aktualisiert")},R=()=>{g(re(a)),o(a.isMultiRole||!1),p(""),d(!1)},h=k.filter(u=>!c.includes(u)&&Se(t,u));return e.jsxs(W,{className:"w-full",children:[e.jsx(Ue,{children:e.jsxs(Ee,{className:"flex items-center gap-2 text-lg",children:[e.jsx(ja,{className:"h-5 w-5 text-blue-600"}),"Rollenverwaltung",N&&e.jsxs(w,{variant:"secondary",className:"ml-2",children:[e.jsx(V,{className:"h-3 w-3 mr-1"}),"Multi-Role"]})]})}),e.jsx(J,{className:"space-y-4",children:l?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ze,{id:"multi-role",checked:i,onCheckedChange:o}),e.jsx(v,{htmlFor:"multi-role",className:"text-sm",children:"Multi-Role aktivieren"})]}),e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-700",children:"Zugewiesene Rollen:"}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-1 min-h-[2rem] p-2 border rounded-md bg-gray-50",children:c.length===0?e.jsx("span",{className:"text-gray-400 text-sm",children:"Keine Rollen zugewiesen"}):c.map((u,b)=>e.jsxs(w,{variant:b===0?"default":"secondary",className:"text-xs flex items-center gap-1",children:[ue(u),b===0&&e.jsx("span",{className:"text-xs opacity-75",children:"(Primär)"}),c.length>1&&e.jsx("button",{onClick:()=>E(u),className:"ml-1 hover:bg-red-200 rounded-full min-h-[44px] min-w-[44px] flex items-center justify-center",children:e.jsx(qe,{className:"h-3 w-3"})})]},u))})]}),i&&h.length>0&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(z,{value:m,onValueChange:p,children:[e.jsx(B,{className:"flex-1",children:e.jsx(T,{placeholder:"Rolle hinzufügen..."})}),e.jsx(U,{children:h.map(u=>e.jsx(j,{value:u,children:ue(u)},u))})]}),e.jsx(S,{onClick:y,disabled:!m,size:"sm",variant:"outline",children:e.jsx(Ke,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"flex gap-2 pt-2",children:[e.jsx(S,{onClick:f,size:"sm",className:"flex-1",children:"Speichern"}),e.jsx(S,{onClick:R,variant:"outline",size:"sm",className:"flex-1",children:"Abbrechen"})]})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx(v,{className:"text-sm font-medium text-gray-700",children:"Aktuelle Rollen:"}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:x.map(u=>e.jsxs(w,{variant:u===a.role?"default":"secondary",className:"text-xs",children:[ue(u),u===a.role&&e.jsx("span",{className:"ml-1 text-xs opacity-75",children:"(Primär)"})]},u))})]}),N&&e.jsxs("div",{className:"text-xs text-gray-500 bg-blue-50 p-2 rounded",children:[e.jsx(V,{className:"h-3 w-3 inline mr-1"}),"Dieser Benutzer kann zwischen verschiedenen Rollen wechseln"]}),e.jsx(S,{onClick:()=>d(!0),variant:"outline",size:"sm",className:"w-full",children:"Rollen bearbeiten"})]})})]})},Ra=({isOpen:a,onOpenChange:t,user:s,teams:l,mentors:d,onUpdateRole:m,onUpdateTeam:p,onUpdateMentor:i,onUpdateActive:o,onSave:c,onUpdateUser:g})=>{const{user:k}=_e();return!s||!k?null:e.jsx(ne,{open:a,onOpenChange:t,children:e.jsxs(ie,{className:"sm:max-w-[500px]",children:[e.jsx(ce,{children:e.jsxs(oe,{className:"flex items-center gap-2",children:[e.jsx(Ve,{className:"h-5 w-5 text-blue-600"}),"Benutzer bearbeiten: ",s.name]})}),e.jsxs("div",{className:"grid gap-4 py-4",children:[g&&e.jsx(Aa,{user:s,currentUser:k,onUpdateUser:g}),!g&&e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"edit-role",children:"Rolle ändern"}),e.jsxs(z,{value:s.role,onValueChange:x=>m(x),children:[e.jsx(B,{children:e.jsx(T,{})}),e.jsxs(U,{children:[e.jsx(j,{value:"berater",children:"Berater"}),e.jsx(j,{value:"mentor",children:"Mentor"}),e.jsx(j,{value:"teamleiter",children:"Teamleiter"}),e.jsx(j,{value:"gebietsmanager",children:"Gebietsmanager"}),e.jsx(j,{value:"admin",children:"Administrator"})]})]})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"edit-team",children:"Team zuweisen"}),e.jsxs(z,{value:s.teamId||"",onValueChange:x=>p(x),children:[e.jsx(B,{children:e.jsx(T,{placeholder:"Team auswählen"})}),e.jsx(U,{children:l.map(x=>e.jsx(j,{value:x.id||"",children:x.name},x.id))})]})]}),s.role==="berater"&&e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"edit-mentor",children:"Mentor zuweisen"}),e.jsxs(z,{value:s.mentorId||"",onValueChange:x=>i(x),children:[e.jsx(B,{children:e.jsx(T,{placeholder:"Mentor auswählen"})}),e.jsx(U,{children:d.map(x=>e.jsx(j,{value:x.id,children:x.name},x.id))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ze,{id:"active",checked:s.is_active!==!1,onCheckedChange:o}),e.jsx(v,{htmlFor:"active",children:"Benutzer ist aktiv"})]})]}),e.jsx(S,{onClick:c,children:"Änderungen speichern"})]})})},za=({isOpen:a,onOpenChange:t,newUser:s,onUpdateNewUser:l,teams:d,mentors:m,onCreate:p})=>e.jsxs(ne,{open:a,onOpenChange:t,children:[e.jsx(ma,{asChild:!0,children:e.jsxs(S,{className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx(Ke,{className:"h-4 w-4 mr-2"}),"Neuen Benutzer anlegen"]})}),e.jsxs(ie,{className:"sm:max-w-[425px]",children:[e.jsx(ce,{children:e.jsxs(oe,{className:"flex items-center gap-2",children:[e.jsx(fa,{className:"h-5 w-5 text-blue-600"}),"Neuen Benutzer erstellen"]})}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"name",children:"Name"}),e.jsx(M,{id:"name",value:s.name,onChange:i=>l({...s,name:i.target.value}),placeholder:"Name des Benutzers"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"email",children:"E-Mail"}),e.jsx(M,{id:"email",type:"email",value:s.email,onChange:i=>l({...s,email:i.target.value}),placeholder:"<EMAIL>"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"password",children:"Passwort"}),e.jsx(M,{id:"password",type:"password",value:s.password,onChange:i=>l({...s,password:i.target.value}),placeholder:"Passwort"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"role",children:"Rolle"}),e.jsxs(z,{value:s.role,onValueChange:i=>l({...s,role:i}),children:[e.jsx(B,{children:e.jsx(T,{placeholder:"Rolle auswählen"})}),e.jsxs(U,{children:[e.jsx(j,{value:"berater",children:"Berater"}),e.jsx(j,{value:"mentor",children:"Mentor"}),e.jsx(j,{value:"teamleiter",children:"Teamleiter"}),e.jsx(j,{value:"gebietsmanager",children:"Gebietsmanager"}),e.jsx(j,{value:"admin",children:"Administrator"})]})]})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"team",children:"Team"}),e.jsxs(z,{value:s.teamId||"",onValueChange:i=>l({...s,teamId:i}),children:[e.jsx(B,{children:e.jsx(T,{placeholder:"Team auswählen"})}),e.jsx(U,{children:d.map(i=>e.jsx(j,{value:i.id||"",children:i.name},i.id))})]})]}),s.role==="berater"&&e.jsxs("div",{className:"grid gap-2",children:[e.jsx(v,{htmlFor:"mentor",children:"Mentor"}),e.jsxs(z,{value:s.mentorId||"",onValueChange:i=>l({...s,mentorId:i}),children:[e.jsx(B,{children:e.jsx(T,{placeholder:"Mentor auswählen"})}),e.jsx(U,{children:m.map(i=>e.jsx(j,{value:i.id,children:i.name},i.id))})]})]})]}),e.jsx(S,{onClick:p,children:"Benutzer erstellen"})]})]}),Ba=({searchTerm:a,onSearchChange:t,selectedRole:s,onRoleChange:l,selectedTeam:d,onTeamChange:m,selectedStatus:p,onStatusChange:i,teams:o,filteredCount:c,totalCount:g,onResetFilters:k,hasActiveFilters:x})=>{var N;return e.jsx(W,{className:"mb-6",children:e.jsxs(J,{className:"p-4",children:[e.jsxs("div",{className:"relative mb-4",children:[e.jsx(pa,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx(M,{placeholder:"Suche nach Name, E-Mail oder Rolle...",value:a,onChange:y=>t(y.target.value),className:"pl-10"})]}),e.jsxs("div",{className:"flex flex-wrap gap-4 items-center mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ga,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Filter:"})]}),e.jsxs(z,{value:s,onValueChange:l,children:[e.jsx(B,{className:"w-40",children:e.jsx(T,{placeholder:"Alle Rollen"})}),e.jsxs(U,{children:[e.jsx(j,{value:"all",children:"Alle Rollen"}),e.jsx(j,{value:"berater",children:"Berater"}),e.jsx(j,{value:"mentor",children:"Mentor"}),e.jsx(j,{value:"teamleiter",children:"Teamleiter"}),e.jsx(j,{value:"gebietsmanager",children:"Gebietsmanager"}),e.jsx(j,{value:"admin",children:"Administrator"})]})]}),e.jsxs(z,{value:d,onValueChange:m,children:[e.jsx(B,{className:"w-40",children:e.jsx(T,{placeholder:"Alle Teams"})}),e.jsxs(U,{children:[e.jsx(j,{value:"all",children:"Alle Teams"}),o.map(y=>e.jsx(j,{value:y.id,children:y.name},y.id))]})]}),e.jsxs(z,{value:p,onValueChange:i,children:[e.jsx(B,{className:"w-32",children:e.jsx(T,{placeholder:"Status"})}),e.jsxs(U,{children:[e.jsx(j,{value:"all",children:"Alle"}),e.jsx(j,{value:"active",children:"Aktiv"}),e.jsx(j,{value:"inactive",children:"Inaktiv"})]})]}),x&&e.jsxs(S,{variant:"outline",size:"sm",onClick:k,children:[e.jsx(qe,{className:"h-3 w-3 mr-1"}),"Filter zurücksetzen"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-2",children:x&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Aktive Filter:"}),a&&e.jsxs(w,{variant:"secondary",className:"text-xs",children:['Suche: "',a,'"']}),s!=="all"&&e.jsxs(w,{variant:"secondary",className:"text-xs",children:["Rolle: ",s]}),d!=="all"&&e.jsxs(w,{variant:"secondary",className:"text-xs",children:["Team: ",(N=o.find(y=>y.id===d))==null?void 0:N.name]}),p!=="all"&&e.jsxs(w,{variant:"secondary",className:"text-xs",children:["Status: ",p==="active"?"Aktiv":"Inaktiv"]})]})}),e.jsxs("div",{className:"text-sm text-gray-600",children:[c," von ",g," Benutzern"]})]})]})})};var We="AlertDialog",[Ta,ft]=xa(We,[Me]),O=Me(),Je=a=>{const{__scopeAlertDialog:t,...s}=a,l=O(t);return e.jsx(Qs,{...l,...s,modal:!0})};Je.displayName=We;var Ua="AlertDialogTrigger",Ea=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,d=O(s);return e.jsx(qs,{...d,...l,ref:t})});Ea.displayName=Ua;var _a="AlertDialogPortal",Xe=a=>{const{__scopeAlertDialog:t,...s}=a,l=O(t);return e.jsx(Ys,{...l,...s})};Xe.displayName=_a;var $a="AlertDialogOverlay",Ye=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,d=O(s);return e.jsx(Zs,{...d,...l,ref:t})});Ye.displayName=$a;var K="AlertDialogContent",[Ia,Oa]=Ta(K),Qe=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,children:l,...d}=a,m=O(s),p=n.useRef(null),i=$e(t,p),o=n.useRef(null);return e.jsx(Ks,{contentName:K,titleName:es,docsSlug:"alert-dialog",children:e.jsx(Ia,{scope:s,cancelRef:o,children:e.jsxs(Ws,{role:"alertdialog",...m,...d,ref:i,onOpenAutoFocus:ha(d.onOpenAutoFocus,c=>{var g;c.preventDefault(),(g=o.current)==null||g.focus({preventScroll:!0})}),onPointerDownOutside:c=>c.preventDefault(),onInteractOutside:c=>c.preventDefault(),children:[e.jsx(Ls,{children:l}),e.jsx(Fa,{contentRef:p})]})})})});Qe.displayName=K;var es="AlertDialogTitle",ss=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,d=O(s);return e.jsx(Js,{...d,...l,ref:t})});ss.displayName=es;var as="AlertDialogDescription",ts=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,d=O(s);return e.jsx(Xs,{...d,...l,ref:t})});ts.displayName=as;var Ma="AlertDialogAction",ls=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,d=O(s);return e.jsx(Fe,{...d,...l,ref:t})});ls.displayName=Ma;var rs="AlertDialogCancel",ns=n.forwardRef((a,t)=>{const{__scopeAlertDialog:s,...l}=a,{cancelRef:d}=Oa(rs,s),m=O(s),p=$e(t,d);return e.jsx(Fe,{...m,...l,ref:p})});ns.displayName=rs;var Fa=({contentRef:a})=>{const t=`\`${K}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${K}\` by passing a \`${as}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${K}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return n.useEffect(()=>{var l;document.getElementById((l=a.current)==null?void 0:l.getAttribute("aria-describedby"))||console.warn(t)},[t,a]),null},Pa=Je,Va=Xe,is=Ye,cs=Qe,os=ls,ds=ns,ms=ss,xs=ts;const Ga=Pa,La=Va,hs=n.forwardRef(({className:a,...t},s)=>e.jsx(is,{className:F("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...t,ref:s}));hs.displayName=is.displayName;const us=n.forwardRef(({className:a,...t},s)=>e.jsxs(La,{children:[e.jsx(hs,{}),e.jsx(cs,{ref:s,className:F("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...t})]}));us.displayName=cs.displayName;const ps=({className:a,...t})=>e.jsx("div",{className:F("flex flex-col space-y-2 text-center sm:text-left",a),...t});ps.displayName="AlertDialogHeader";const gs=({className:a,...t})=>e.jsx("div",{className:F("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t});gs.displayName="AlertDialogFooter";const js=n.forwardRef(({className:a,...t},s)=>e.jsx(ms,{ref:s,className:F("text-lg font-semibold",a),...t}));js.displayName=ms.displayName;const fs=n.forwardRef(({className:a,...t},s)=>e.jsx(xs,{ref:s,className:F("text-sm text-muted-foreground",a),...t}));fs.displayName=xs.displayName;const vs=n.forwardRef(({className:a,...t},s)=>e.jsx(os,{ref:s,className:F(Ie(),a),...t}));vs.displayName=os.displayName;const Ns=n.forwardRef(({className:a,...t},s)=>e.jsx(ds,{ref:s,className:F(Ie({variant:"outline"}),"mt-2 sm:mt-0",a),...t}));Ns.displayName=ds.displayName;const Ha=({isOpen:a,onOpenChange:t,user:s,onConfirm:l})=>s?e.jsx(Ga,{open:a,onOpenChange:t,children:e.jsxs(us,{children:[e.jsxs(ps,{children:[e.jsxs(js,{className:"flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5 text-orange-500"}),"Statistiken zurücksetzen"]}),e.jsx(fs,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("p",{children:["Möchten Sie wirklich die Statistiken von ",e.jsx("strong",{children:s.name})," zurücksetzen?"]}),e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(He,{className:"h-4 w-4 text-orange-600 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"font-medium text-orange-800 mb-1",children:"Folgende Daten werden zurückgesetzt:"}),e.jsxs("ul",{className:"text-orange-700 space-y-1",children:[e.jsx("li",{children:"• Gesamte Besuche → 0"}),e.jsx("li",{children:"• Gesamte Verkäufe → 0"}),e.jsx("li",{children:"• Reset-Zähler wird um 1 erhöht"}),e.jsx("li",{children:"• Zeitstempel des letzten Resets wird aktualisiert"})]})]})]})}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Hinweis:"})," Diese Aktion kann nicht rückgängig gemacht werden. Alle bisherigen Statistiken gehen unwiderruflich verloren."]})]})})]}),e.jsxs(gs,{children:[e.jsx(Ns,{children:"Abbrechen"}),e.jsx(vs,{onClick:l,className:"bg-orange-600 hover:bg-orange-700",children:"Statistiken zurücksetzen"})]})]})}):null,vt=()=>{const{users:a,updateUser:t,createUser:s,deleteUser:l,blockUser:d,unblockUser:m,user:p}=_e(),i=Oe(),[o,c]=n.useState(null),[g,k]=n.useState([]),[x,N]=n.useState([]),[y,E]=n.useState(!1),[f,R]=n.useState(!1),[h,u]=n.useState(!1),[b,C]=n.useState(!1),[A,I]=n.useState(null),[X,de]=n.useState(!1),[ae,fe]=n.useState(null),[bs,te]=n.useState(!1),[G,me]=n.useState(null),[le,ve]=n.useState("block"),[Y,Ne]=n.useState(""),[Q,be]=n.useState("all"),[ee,ye]=n.useState("all"),[L,we]=n.useState("all"),[H,ke]=n.useState({name:"",email:"",password:"",role:"berater",teamId:"",mentorId:""});n.useEffect(()=>{se(),ys()},[]);const se=async()=>{try{const r=await sa();k(r||[])}catch(r){console.error("Error loading user profiles:",r)}},ys=async()=>{try{const r=await aa();N(r||[])}catch(r){console.error("Error loading teams:",r)}},xe=n.useMemo(()=>a.filter(r=>{const _=g.find(he=>he.id===r.id);x.find(he=>he.id===r.teamId);const $=Y.toLowerCase(),Fs=!Y||r.name.toLowerCase().includes($)||r.email.toLowerCase().includes($)||r.role.toLowerCase().includes($),Ps=Q==="all"||r.role===Q,Vs=ee==="all"||r.teamId===ee,De=(_==null?void 0:_.is_active)!==!1;return Fs&&Ps&&Vs&&(L==="all"||L==="active"&&De||L==="inactive"&&!De)}),[a,g,x,Y,Q,ee,L]),ws=Y!==""||Q!=="all"||ee!=="all"||L!=="all",ks=()=>{Ne(""),be("all"),ye("all"),we("all")},Cs=r=>{const _=g.find($=>$.id===r.id);c({...r,..._}),R(!0)},Ds=r=>{const _=g.find($=>$.id===r.id);c({...r,..._}),E(!0)},Ss=r=>{I(r),C(!0)},As=r=>{fe(r),de(!0)},Rs=r=>{me(r),ve("block"),te(!0)},zs=r=>{me(r),ve("unblock"),te(!0)},Bs=async()=>{if(!(!p||!A))try{await ta(A.id,p.id),D.success(`Statistiken von ${A.name} wurden zurückgesetzt`),se()}catch(r){console.error("Error resetting user statistics:",r),D.error("Fehler beim Zurücksetzen der Statistiken")}finally{C(!1),I(null)}},Ts=async(r,_)=>{if(ae)try{await l(ae.id,r,_),D.success(`Benutzer ${ae.name} wurde ${r==="soft"?"deaktiviert":"gelöscht"}`),se()}catch($){console.error("Error deleting user:",$),D.error("Fehler beim Löschen des Benutzers")}finally{de(!1),fe(null)}},Us=async(r,_)=>{if(G)try{le==="block"?(await d(G.id,r,_),D.success(`Benutzer ${G.name} wurde blockiert`)):(await m(G.id,r),D.success(`Benutzer ${G.name} wurde entsperrt`)),se()}catch($){console.error(`Error ${le}ing user:`,$),D.error(`Fehler beim ${le==="block"?"Blockieren":"Entsperren"} des Benutzers`)}finally{te(!1),me(null)}},Es=r=>{o&&c({...o,role:r})},_s=r=>{o&&c({...o,teamId:r})},$s=r=>{o&&c({...o,mentorId:r})},Is=r=>{o&&c({...o,is_active:r})},Os=async()=>{if(o){if(t(o),g.find(r=>r.id===o.id))try{await la(o.id,{team_id:o.teamId||null,is_active:o.is_active??!0})}catch(r){console.error("Error updating user profile:",r)}D.success(`Benutzer ${o.name} wurde aktualisiert`),c(null),R(!1),se()}},Ms=()=>{if(!H.name||!H.email||!H.password){D.error("Bitte füllen Sie alle Pflichtfelder aus");return}s(H),D.success(`Benutzer ${H.name} wurde erstellt`),ke({name:"",email:"",password:"",role:"berater",teamId:"",mentorId:""}),u(!1)},Ce=a.filter(r=>r.role==="mentor");return!p||!Hs(p)?e.jsx(Ae,{title:"Benutzerverwaltung",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})})})}):e.jsx(Ae,{title:"Benutzerverwaltung",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${i?"text-2xl":"text-4xl"}`,children:"Benutzerverwaltung"}),e.jsx("p",{className:`text-gray-600 ${i?"text-sm":"text-lg"}`,children:"Verwalten Sie Benutzer, Rollen und Statistiken"})]}),e.jsx(Ba,{searchTerm:Y,onSearchChange:Ne,selectedRole:Q,onRoleChange:be,selectedTeam:ee,onTeamChange:ye,selectedStatus:L,onStatusChange:we,teams:x,filteredCount:xe.length,totalCount:a.length,onResetFilters:ks,hasActiveFilters:ws}),e.jsx("div",{className:"flex justify-end",children:e.jsx(za,{isOpen:h,onOpenChange:u,newUser:H,onUpdateNewUser:ke,teams:x,mentors:Ce,onCreate:Ms})}),e.jsxs(W,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsx(Ue,{className:`${i?"p-4 pb-2":"p-6 pb-4"}`,children:e.jsxs(Ee,{className:`flex items-center gap-3 ${i?"text-lg":"text-xl"} font-bold text-gray-800`,children:[e.jsx(ea,{className:`${i?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Benutzer (",xe.length,")"]})}),e.jsx(J,{className:`${i?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx(va,{users:xe,userProfiles:g,teams:x,isMobile:i,onViewUser:Ds,onEditUser:Cs,onResetUserStats:Ss,onDeleteUser:As,onBlockUser:Rs,onUnblockUser:zs})})]}),e.jsx(Na,{isOpen:y,onOpenChange:E,user:o}),e.jsx(Ra,{isOpen:f,onOpenChange:R,user:o,teams:x,mentors:Ce,onUpdateRole:Es,onUpdateTeam:_s,onUpdateMentor:$s,onUpdateActive:Is,onSave:Os,onUpdateUser:t}),e.jsx(Ha,{isOpen:b,onOpenChange:C,user:A,onConfirm:Bs}),e.jsx(Ca,{isOpen:X,onOpenChange:de,user:ae,onConfirm:Ts}),e.jsx(Sa,{isOpen:bs,onOpenChange:te,user:G,action:le,onConfirm:Us})]})})})};export{vt as default};
