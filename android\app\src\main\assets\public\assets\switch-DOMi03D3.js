import{r as s,F as _,j as r,a as x}from"./index-Cmt5neWh.js";import{c as H,u as I,P as S,a as M,b as B}from"./input-BK13BBqa.js";import{u as q}from"./index-CEhV84jC.js";var v="Switch",[z,$]=H(v),[A,F]=z(v),g=s.forwardRef((e,o)=>{const{__scopeSwitch:t,name:n,checked:a,defaultChecked:l,required:i,disabled:c,value:d="on",onCheckedChange:b,form:m,...p}=e,[u,E]=s.useState(null),R=_(o,f=>E(f)),k=s.useRef(!1),w=u?m||!!u.closest("form"):!0,[h=!1,N]=I({prop:a,defaultProp:l,onChange:b});return r.jsxs(A,{scope:t,checked:h,disabled:c,children:[r.jsx(S.button,{type:"button",role:"switch","aria-checked":h,"aria-required":i,"data-state":P(h),"data-disabled":c?"":void 0,disabled:c,value:d,...p,ref:R,onClick:M(e.onClick,f=>{N(T=>!T),w&&(k.current=f.isPropagationStopped(),k.current||f.stopPropagation())})}),w&&r.jsx(O,{control:u,bubbles:!k.current,name:n,value:d,checked:h,required:i,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=v;var C="SwitchThumb",y=s.forwardRef((e,o)=>{const{__scopeSwitch:t,...n}=e,a=F(C,t);return r.jsx(S.span,{"data-state":P(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:o})});y.displayName=C;var O=e=>{const{control:o,checked:t,bubbles:n=!0,...a}=e,l=s.useRef(null),i=q(t),c=B(o);return s.useEffect(()=>{const d=l.current,b=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(b,"checked").set;if(i!==t&&p){const u=new Event("click",{bubbles:n});p.call(d,t),d.dispatchEvent(u)}},[i,t,n]),r.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...a,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function P(e){return e?"checked":"unchecked"}var j=g,D=y;const L=s.forwardRef(({className:e,...o},t)=>r.jsx(j,{className:x("peer inline-flex min-h-[44px] min-w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...o,ref:t,children:r.jsx(D,{className:x("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));L.displayName=j.displayName;export{L as S};
