import{c as A,r as u,j as C,s as b,F as P,P as R,V as I,a0 as T,a as _}from"./index-Cmt5neWh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=A("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=A("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function K(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e==null||e(r),n===!1||!r.defaultPrevented)return t==null?void 0:t(r)}}function $(e,t=[]){let n=[];function o(i,s){const c=u.createContext(s),a=n.length;n=[...n,s];function f(d){const{scope:p,children:m,...l}=d,v=(p==null?void 0:p[e][a])||c,S=u.useMemo(()=>l,Object.values(l));return C.jsx(v.Provider,{value:S,children:m})}function x(d,p){const m=(p==null?void 0:p[e][a])||c,l=u.useContext(m);if(l)return l;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return f.displayName=i+"Provider",[f,x]}const r=()=>{const i=n.map(s=>u.createContext(s));return function(c){const a=(c==null?void 0:c[e])||i;return u.useMemo(()=>({[`__scope${e}`]:{...c,[e]:a}}),[c,a])}};return r.scopeName=e,[o,j(r,...t)]}function j(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(i){const s=o.reduce((c,{useScope:a,scopeName:f})=>{const d=a(i)[`__scope${f}`];return{...c,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Q(e){const t=e+"CollectionProvider",[n,o]=$(t),[r,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:l,children:v}=m,S=b.useRef(null),h=b.useRef(new Map).current;return C.jsx(r,{scope:l,itemMap:h,collectionRef:S,children:v})};s.displayName=t;const c=e+"CollectionSlot",a=b.forwardRef((m,l)=>{const{scope:v,children:S}=m,h=i(c,v),N=P(l,h.collectionRef);return C.jsx(R,{ref:N,children:S})});a.displayName=c;const f=e+"CollectionItemSlot",x="data-radix-collection-item",d=b.forwardRef((m,l)=>{const{scope:v,children:S,...h}=m,N=b.useRef(null),w=P(l,N),y=i(f,v);return b.useEffect(()=>(y.itemMap.set(N,{ref:N,...h}),()=>void y.itemMap.delete(N))),C.jsx(R,{[x]:"",ref:w,children:S})});d.displayName=f;function p(m){const l=i(e+"CollectionConsumer",m);return b.useCallback(()=>{const S=l.collectionRef.current;if(!S)return[];const h=Array.from(S.querySelectorAll(`[${x}]`));return Array.from(l.itemMap.values()).sort((y,E)=>h.indexOf(y.ref.current)-h.indexOf(E.ref.current))},[l.collectionRef,l.itemMap])}return[{Provider:s,Slot:a,ItemSlot:d},p,o]}function Y(e,t){const n=u.createContext(t),o=i=>{const{children:s,...c}=i,a=u.useMemo(()=>c,Object.values(c));return C.jsx(n.Provider,{value:a,children:s})};o.displayName=e+"Provider";function r(i){const s=u.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[o,r]}function Z(e,t=[]){let n=[];function o(i,s){const c=u.createContext(s),a=n.length;n=[...n,s];const f=d=>{var h;const{scope:p,children:m,...l}=d,v=((h=p==null?void 0:p[e])==null?void 0:h[a])||c,S=u.useMemo(()=>l,Object.values(l));return C.jsx(v.Provider,{value:S,children:m})};f.displayName=i+"Provider";function x(d,p){var v;const m=((v=p==null?void 0:p[e])==null?void 0:v[a])||c,l=u.useContext(m);if(l)return l;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[f,x]}const r=()=>{const i=n.map(s=>u.createContext(s));return function(c){const a=(c==null?void 0:c[e])||i;return u.useMemo(()=>({[`__scope${e}`]:{...c,[e]:a}}),[c,a])}};return r.scopeName=e,[o,U(r,...t)]}function U(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(i){const s=o.reduce((c,{useScope:a,scopeName:f})=>{const d=a(i)[`__scope${f}`];return{...c,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var k=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],H=k.reduce((e,t)=>{const n=u.forwardRef((o,r)=>{const{asChild:i,...s}=o,c=i?R:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C.jsx(c,{...s,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ee(e,t){e&&I.flushSync(()=>e.dispatchEvent(t))}function O(e){const t=u.useRef(e);return u.useEffect(()=>{t.current=e}),u.useMemo(()=>(...n)=>{var o;return(o=t.current)==null?void 0:o.call(t,...n)},[])}var g=globalThis!=null&&globalThis.document?u.useLayoutEffect:()=>{};function L(e,t){return u.useReducer((n,o)=>t[n][o]??n,e)}var z=e=>{const{present:t,children:n}=e,o=D(t),r=typeof n=="function"?n({present:o.isPresent}):u.Children.only(n),i=P(o.ref,W(r));return typeof n=="function"||o.isPresent?u.cloneElement(r,{ref:i}):null};z.displayName="Presence";function D(e){const[t,n]=u.useState(),o=u.useRef({}),r=u.useRef(e),i=u.useRef("none"),s=e?"mounted":"unmounted",[c,a]=L(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return u.useEffect(()=>{const f=M(o.current);i.current=c==="mounted"?f:"none"},[c]),g(()=>{const f=o.current,x=r.current;if(x!==e){const p=i.current,m=M(f);e?a("MOUNT"):m==="none"||(f==null?void 0:f.display)==="none"?a("UNMOUNT"):a(x&&p!==m?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,a]),g(()=>{if(t){let f;const x=t.ownerDocument.defaultView??window,d=m=>{const v=M(o.current).includes(m.animationName);if(m.target===t&&v&&(a("ANIMATION_END"),!r.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",f=x.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},p=m=>{m.target===t&&(i.current=M(o.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{x.clearTimeout(f),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:u.useCallback(f=>{f&&(o.current=getComputedStyle(f)),n(f)},[])}}function M(e){return(e==null?void 0:e.animationName)||"none"}function W(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function te({prop:e,defaultProp:t,onChange:n=()=>{}}){const[o,r]=F({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:o,c=O(n),a=u.useCallback(f=>{if(i){const d=typeof f=="function"?f(e):f;d!==e&&c(d)}else r(f)},[i,e,r,c]);return[s,a]}function F({defaultProp:e,onChange:t}){const n=u.useState(e),[o]=n,r=u.useRef(o),i=O(t);return u.useEffect(()=>{r.current!==o&&(i(o),r.current=o)},[o,r,i]),n}var B=T.useId||(()=>{}),V=0;function ne(e){const[t,n]=u.useState(B());return g(()=>{e||n(o=>o??String(V++))},[e]),e||(t?`radix-${t}`:"")}function oe(e){const[t,n]=u.useState(void 0);return g(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const i=r[0];let s,c;if("borderBoxSize"in i){const a=i.borderBoxSize,f=Array.isArray(a)?a[0]:a;s=f.inlineSize,c=f.blockSize}else s=e.offsetWidth,c=e.offsetHeight;n({width:s,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}const X=u.forwardRef(({className:e,type:t,...n},o)=>C.jsx("input",{type:t,className:_("flex h-12 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm min-h-[48px] touch-feedback",e),style:{fontSize:"16px",...n.style},ref:o,...n}));X.displayName="Input";export{X as I,H as P,G as U,J as X,K as a,oe as b,Z as c,z as d,ne as e,Q as f,g,O as h,ee as i,Y as j,te as u};
