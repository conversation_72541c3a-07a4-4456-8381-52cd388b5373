import{r as i,j as p,a as I}from"./index-Cmt5neWh.js";import{P as $}from"./input-BK13BBqa.js";function M(e,r=[]){let a=[];function s(l,u){const o=i.createContext(u),n=a.length;a=[...a,u];function d(v){const{scope:c,children:x,...f}=v,j=(c==null?void 0:c[e][n])||o,E=i.useMemo(()=>f,Object.values(f));return p.jsx(j.Provider,{value:E,children:x})}function N(v,c){const x=(c==null?void 0:c[e][n])||o,f=i.useContext(x);if(f)return f;if(u!==void 0)return u;throw new Error(`\`${v}\` must be used within \`${l}\``)}return d.displayName=l+"Provider",[d,N]}const t=()=>{const l=a.map(u=>i.createContext(u));return function(o){const n=(o==null?void 0:o[e])||l;return i.useMemo(()=>({[`__scope${e}`]:{...o,[e]:n}}),[o,n])}};return t.scopeName=e,[s,R(t,...r)]}function R(...e){const r=e[0];if(e.length===1)return r;const a=()=>{const s=e.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(l){const u=s.reduce((o,{useScope:n,scopeName:d})=>{const v=n(l)[`__scope${d}`];return{...o,...v}},{});return i.useMemo(()=>({[`__scope${r.scopeName}`]:u}),[u])}};return a.scopeName=r.scopeName,a}var g="Progress",P=100,[V,F]=M(g),[A,D]=V(g),S=i.forwardRef((e,r)=>{const{__scopeProgress:a,value:s=null,max:t,getValueLabel:l=L,...u}=e;(t||t===0)&&!b(t)&&console.error(O(`${t}`,"Progress"));const o=b(t)?t:P;s!==null&&!h(s,o)&&console.error(T(`${s}`,"Progress"));const n=h(s,o)?s:null,d=m(n)?l(n,o):void 0;return p.jsx(A,{scope:a,value:n,max:o,children:p.jsx($.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":m(n)?n:void 0,"aria-valuetext":d,role:"progressbar","data-state":w(n,o),"data-value":n??void 0,"data-max":o,...u,ref:r})})});S.displayName=g;var _="ProgressIndicator",C=i.forwardRef((e,r)=>{const{__scopeProgress:a,...s}=e,t=D(_,a);return p.jsx($.div,{"data-state":w(t.value,t.max),"data-value":t.value??void 0,"data-max":t.max,...s,ref:r})});C.displayName=_;function L(e,r){return`${Math.round(e/r*100)}%`}function w(e,r){return e==null?"indeterminate":e===r?"complete":"loading"}function m(e){return typeof e=="number"}function b(e){return m(e)&&!isNaN(e)&&e>0}function h(e,r){return m(e)&&!isNaN(e)&&e<=r&&e>=0}function O(e,r){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${r}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${P}\`.`}function T(e,r){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${r}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${P} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var y=S,G=C;const X=i.forwardRef(({className:e,value:r,...a},s)=>p.jsx(y,{ref:s,className:I("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:p.jsx(G,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));X.displayName=y.displayName;export{X as P};
