import{s as l,j as e,a as t,Q as w}from"./index-Cmt5neWh.js";const u=w("rounded-2xl border transition-all duration-250",{variants:{variant:{default:"bg-white border-neutral-200 shadow-md hover:shadow-lg",elevated:"bg-white border-neutral-200 shadow-lg hover:shadow-xl",outlined:"bg-white border-2 border-neutral-300 shadow-sm hover:shadow-md",glass:"bg-white/95 backdrop-blur-sm border-white/20 shadow-xl",success:"bg-green-50 border-green-200 shadow-md",warning:"bg-yellow-50 border-yellow-200 shadow-md",error:"bg-red-50 border-red-200 shadow-md",info:"bg-blue-50 border-blue-200 shadow-md"},padding:{none:"p-0",sm:"p-4",md:"p-6",lg:"p-8"},interactive:{true:"cursor-pointer hover:scale-[1.02] active:scale-[0.98]",false:""}},defaultVariants:{variant:"default",padding:"md",interactive:!1}}),m=l.forwardRef(({className:a,variant:s,padding:r,interactive:d,children:n,...o},i)=>e.jsx("div",{ref:i,className:t(u({variant:s,padding:r,interactive:d,className:a})),...o,children:n}));m.displayName="Card";const x=l.forwardRef(({className:a,children:s,...r},d)=>e.jsx("div",{ref:d,className:t("flex flex-col space-y-1.5 pb-4",a),...r,children:s}));x.displayName="CardHeader";const h=l.forwardRef(({className:a,children:s,as:r="h3",...d},n)=>e.jsx(r,{ref:n,className:t("text-xl font-semibold leading-none tracking-tight text-neutral-900",a),...d,children:s}));h.displayName="CardTitle";const b=l.forwardRef(({className:a,children:s,...r},d)=>e.jsx("p",{ref:d,className:t("text-base text-neutral-600",a),...r,children:s}));b.displayName="CardDescription";const g=l.forwardRef(({className:a,children:s,...r},d)=>e.jsx("div",{ref:d,className:t("pt-0",a),...r,children:s}));g.displayName="CardContent";const p=l.forwardRef(({className:a,children:s,...r},d)=>e.jsx("div",{ref:d,className:t("flex items-center pt-4",a),...r,children:s}));p.displayName="CardFooter";const N=({street:a,houseNumber:s,city:r,zipCode:d,houseType:n,step:o,totalSteps:i,className:f})=>e.jsx(m,{variant:"glass",className:t("mb-6",f),children:e.jsx(x,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(h,{className:"text-lg",children:[a," ",s]}),e.jsxs(b,{children:[d," ",r," • ",n]})]}),o&&i&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-sm font-medium text-neutral-600",children:["Schritt ",o," von ",i]}),e.jsx("div",{className:"flex space-x-1",children:Array.from({length:i},(v,c)=>e.jsx("div",{className:t("w-2 h-2 rounded-full",c<o?"bg-blue-600":"bg-neutral-300")},c))})]})]})})});export{N as A,m as C,g as a,x as b,h as c,p as d};
