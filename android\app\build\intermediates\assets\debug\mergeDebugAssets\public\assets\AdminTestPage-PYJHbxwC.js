import{b as L,r as o,j as e,T as F,k as x,C as h,i as u,l as g,B as n,J as m}from"./index-Cmt5neWh.js";import{M as w,S as j,U as P,B as U}from"./MainLayout-wyzz138D.js";import{B as y}from"./badge-XkNoLG2o.js";import{g as E,h as $,a as O,b as q,c as D}from"./functions-DcGEt8N_.js";import{D as v}from"./database-DPAjLTxQ.js";import{L as M}from"./loader-circle-Brkx1kW_.js";import{C as A}from"./circle-check-big-DK6RP7UF.js";import{C}from"./circle-x-IVLZ_bbf.js";import"./input-BK13BBqa.js";import"./client-DbI4l5kI.js";const Q=()=>{const{user:S}=L(),[c,k]=o.useState({}),[p,N]=o.useState(!1),[d,B]=o.useState(0),R=async()=>{N(!0);const a={};try{console.log("Testing Areas...");const s=await E();a.areas={success:!0,count:(s==null?void 0:s.length)||0,data:s};const l=await $({name:`Test Area ${Date.now()}`,description:"Test area for validation",postal_codes:["12345","67890"]});a.createArea={success:!0,data:l}}catch(s){console.error("Areas test failed:",s),a.areas={success:!1,error:s.message}}try{console.log("Testing Teams...");const s=await O();a.teams={success:!0,count:(s==null?void 0:s.length)||0,data:s}}catch(s){console.error("Teams test failed:",s),a.teams={success:!1,error:s.message}}try{console.log("Testing Audit Logs...");const s=await q(10);a.auditLogs={success:!0,count:(s==null?void 0:s.length)||0,data:s}}catch(s){console.error("Audit logs test failed:",s),a.auditLogs={success:!1,error:s.message}}try{console.log("Testing User Profiles...");const s=await D();a.userProfiles={success:!0,count:(s==null?void 0:s.length)||0,data:s}}catch(s){console.error("User profiles test failed:",s),a.userProfiles={success:!1,error:s.message}}k(a),N(!1);const t=Object.values(a).filter(s=>s.success).length,r=Object.keys(a).length;t===r?m.success(`All ${r} Supabase tests passed! Using fallback data.`):m.warning(`${t}/${r} tests passed. Some functions may be using fallback data.`)},f=()=>{const a=document.querySelectorAll('button, [role="button"], input, select, textarea');let t=0;a.forEach(r=>{const s=r.getBoundingClientRect(),l=window.getComputedStyle(r),b=parseInt(l.minHeight)||s.height,T=parseInt(l.minWidth)||s.width;(b<44||T<44)&&(t++,console.warn("Touch target violation:",r,{height:b,width:T}))}),B(t),t===0?m.success("All touch targets meet the 44px minimum requirement!"):m.error(`Found ${t} touch target violations`)};o.useEffect(()=>{setTimeout(f,1e3)},[]);const i=({title:a,result:t})=>e.jsxs(x,{className:"mb-4",children:[e.jsx(h,{className:"pb-3",children:e.jsxs(u,{className:"flex items-center gap-2 text-lg",children:[t!=null&&t.success?e.jsx(A,{className:"h-5 w-5 text-green-600"}):e.jsx(C,{className:"h-5 w-5 text-red-600"}),a]})}),e.jsx(g,{children:t!=null&&t.success?e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{variant:"default",className:"bg-green-100 text-green-800",children:"Success"}),t.count!==void 0&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Found ",t.count," records"]}),t.data&&e.jsxs("details",{className:"text-xs",children:[e.jsx("summary",{className:"cursor-pointer text-blue-600 hover:text-blue-800",children:"View Data"}),e.jsx("pre",{className:"mt-2 p-2 bg-gray-100 rounded overflow-auto max-h-32",children:JSON.stringify(t.data,null,2)})]})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{variant:"destructive",children:"Failed"}),(t==null?void 0:t.error)&&e.jsxs("p",{className:"text-sm text-red-600",children:["Error: ",t.error]})]})})]});return S?e.jsx(w,{title:"Admin Test",children:e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-4xl",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Admin Functionality Test"}),e.jsx("p",{className:"text-gray-600",children:"Test Supabase integration and touch target compliance"})]}),e.jsxs(x,{className:"mb-6",children:[e.jsx(h,{children:e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5 text-blue-600"}),"Test Controls"]})}),e.jsxs(g,{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-4 flex-wrap",children:[e.jsx(n,{onClick:R,disabled:p,className:"min-h-[44px]",children:p?e.jsxs(e.Fragment,{children:[e.jsx(M,{className:"h-4 w-4 mr-2 animate-spin"}),"Testing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Test Supabase Functions"]})}),e.jsxs(n,{onClick:f,variant:"outline",className:"min-h-[44px]",children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Check Touch Targets"]})]}),e.jsxs("div",{className:"p-4 rounded-lg border bg-gray-50",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d===0?e.jsx(A,{className:"h-5 w-5 text-green-600"}):e.jsx(C,{className:"h-5 w-5 text-red-600"}),e.jsx("span",{className:"font-medium",children:"Touch Target Compliance"})]}),e.jsx("p",{className:"text-sm text-gray-600",children:d===0?"All interactive elements meet the 44px minimum requirement":`${d} elements are smaller than 44px`})]})]})]}),Object.keys(c).length>0&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Test Results"}),e.jsx(i,{title:"Areas Management",result:c.areas}),e.jsx(i,{title:"Area Creation",result:c.createArea}),e.jsx(i,{title:"Teams Management",result:c.teams}),e.jsx(i,{title:"Audit Logs",result:c.auditLogs}),e.jsx(i,{title:"User Profiles",result:c.userProfiles})]}),e.jsxs(x,{className:"mt-6",children:[e.jsx(h,{children:e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-5 w-5 text-blue-600"}),"Sample Touch Target Elements"]})}),e.jsxs(g,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsx(n,{size:"sm",className:"min-h-[44px]",children:"Small Button"}),e.jsx(n,{variant:"outline",className:"min-h-[44px]",children:"Outline"}),e.jsx(n,{variant:"ghost",className:"min-h-[44px]",children:"Ghost"}),e.jsx(n,{size:"icon",className:"min-h-[44px] min-w-[44px]",children:e.jsx(P,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-2 border rounded hover:bg-gray-50 min-h-[44px] min-w-[44px]",children:e.jsx(U,{className:"h-4 w-4"})}),e.jsx("button",{className:"p-3 border rounded hover:bg-gray-50 min-h-[44px] min-w-[44px]",children:e.jsx(j,{className:"h-4 w-4"})})]})]})]})]})}):e.jsx(w,{title:"Admin Test",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(F,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Authentication Required"}),e.jsx("p",{className:"text-gray-600",children:"Please log in to access the admin test page."})]})})})};export{Q as default};
