import{r as o,j as t,a as l}from"./index-Cmt5neWh.js";const r=o.forwardRef(({className:a,...e},s)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:s,className:l("w-full caption-bottom text-sm",a),...e})}));r.displayName="Table";const d=o.forwardRef(({className:a,...e},s)=>t.jsx("thead",{ref:s,className:l("[&_tr]:border-b",a),...e}));d.displayName="TableHeader";const m=o.forwardRef(({className:a,...e},s)=>t.jsx("tbody",{ref:s,className:l("[&_tr:last-child]:border-0",a),...e}));m.displayName="TableBody";const b=o.forwardRef(({className:a,...e},s)=>t.jsx("tfoot",{ref:s,className:l("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...e}));b.displayName="TableFooter";const c=o.forwardRef(({className:a,...e},s)=>t.jsx("tr",{ref:s,className:l("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...e}));c.displayName="TableRow";const f=o.forwardRef(({className:a,...e},s)=>t.jsx("th",{ref:s,className:l("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...e}));f.displayName="TableHead";const i=o.forwardRef(({className:a,...e},s)=>t.jsx("td",{ref:s,className:l("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...e}));i.displayName="TableCell";const n=o.forwardRef(({className:a,...e},s)=>t.jsx("caption",{ref:s,className:l("mt-4 text-sm text-muted-foreground",a),...e}));n.displayName="TableCaption";export{r as T,d as a,c as b,f as c,m as d,i as e};
