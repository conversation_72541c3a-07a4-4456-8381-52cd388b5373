import{b as M,m as T,j as e,k as i,l as n,C as x,i as m,t as h}from"./index-Cmt5neWh.js";import{b as V,M as p,U as I,T as u,f as A,d as R}from"./MainLayout-wyzz138D.js";import{C as g}from"./chart-column-TdCSSvzn.js";import{R as b,B as j,C as f,X as y,Y as N,T as k,L as w,a as v}from"./BarChart-DJI4ZcuR.js";import"./input-BK13BBqa.js";const Y=()=>{const{user:d,users:C}=M(),{visits:o,products:c}=T(),s=V();if(!d||d.role!=="mentor")return e.jsx(p,{title:"Berater Statistiken",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:e.jsx(i,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm p-6",children:e.jsx(n,{className:"text-center",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})})})})});const l=C.filter(t=>t.mentorId===d.id),$=l.reduce((t,a)=>t+o.filter(r=>r.userId===a.id).length,0),S=l.reduce((t,a)=>t+c.filter(r=>r.userId===a.id).length,0),B=l.map(t=>{const a=o.filter(r=>r.userId===t.id).length;return{name:t.name,visits:a}}),z=l.map(t=>{const a=c.filter(r=>r.userId===t.id).length;return{name:t.name,sales:a}}),D=[{title:"Team Berater",value:l.length,icon:I,color:"from-blue-500 to-blue-600",textColor:"text-blue-600"},{title:"Gesamt Besuche",value:$,icon:g,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"},{title:"Gesamt Verkäufe",value:S,icon:u,color:"from-green-500 to-green-600",textColor:"text-green-600"}];return e.jsx(p,{title:"Berater Statistiken",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${s?"text-2xl":"text-4xl"}`,children:"Berater Statistiken"}),e.jsxs("p",{className:`text-gray-600 ${s?"text-sm":"text-lg"}`,children:["Statistiken für ",l.length," zugewiesene Berater"]}),e.jsx("p",{className:`text-gray-500 ${s?"text-xs":"text-sm"}`,children:A(new Date,"'Stand:' d. MMMM yyyy",{locale:R})})]}),e.jsx("div",{className:`grid gap-4 md:gap-6 ${s?"grid-cols-1":"grid-cols-1 md:grid-cols-3"}`,children:D.map((t,a)=>e.jsx(i,{className:`glass-card hover-lift ${s?"p-4":"p-6"} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`,style:{animationDelay:`${a*.1}s`},children:e.jsx(n,{className:"p-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:`font-medium text-gray-600 ${s?"text-xs":"text-sm"}`,children:t.title}),e.jsx("p",{className:`font-bold ${t.textColor} ${s?"text-2xl":"text-4xl"}`,children:t.value})]}),e.jsx("div",{className:`rounded-2xl bg-gradient-to-br ${t.color} p-3 shadow-lg`,children:e.jsx(t.icon,{className:`${s?"h-6 w-6":"h-8 w-8"} text-white`})})]})})},t.title))}),e.jsxs("div",{className:"space-y-6 md:space-y-8",children:[e.jsxs(i,{className:"glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(x,{className:`${s?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(m,{className:`flex items-center gap-3 ${s?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(g,{className:`${s?"h-5 w-5":"h-6 w-6"} text-purple-600`}),"Besuche pro Berater"]}),e.jsx(h,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:"Verteilung der Besuche auf das Team"})]}),e.jsx(n,{className:`${s?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{style:{height:s?"250px":"400px"},className:"w-full",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(j,{data:B,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx(f,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(y,{dataKey:"name",tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(N,{tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(k,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),e.jsx(w,{}),e.jsx(v,{dataKey:"visits",name:"Anzahl Besuche",fill:"#8b5cf6",radius:[4,4,0,0]})]})})})})]}),e.jsxs(i,{className:"glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsxs(x,{className:`${s?"p-4 pb-2":"p-6 pb-4"}`,children:[e.jsxs(m,{className:`flex items-center gap-3 ${s?"text-lg":"text-2xl"} font-bold text-gray-800`,children:[e.jsx(u,{className:`${s?"h-5 w-5":"h-6 w-6"} text-green-600`}),"Verkäufe pro Berater"]}),e.jsx(h,{className:`${s?"text-xs":"text-sm"} text-gray-600`,children:"Verkaufsleistung des Teams"})]}),e.jsx(n,{className:`${s?"p-4 pt-0":"p-6 pt-0"}`,children:e.jsx("div",{style:{height:s?"250px":"400px"},className:"w-full",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(j,{data:z,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx(f,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(y,{dataKey:"name",tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(N,{tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(k,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),e.jsx(w,{}),e.jsx(v,{dataKey:"sales",name:"Anzahl Verkäufe",fill:"#10b981",radius:[4,4,0,0]})]})})})})]})]})]})})})};export{Y as default};
