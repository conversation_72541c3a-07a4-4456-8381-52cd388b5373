import{c as b,N as V,u as B,m as W,r as v,J as i,j as e,k as $,C as H,H as K,i as L,t as P,l as J,B as o,n as U}from"./index-Cmt5neWh.js";import{M as R}from"./MainLayout-wyzz138D.js";import{U as _,I as q}from"./input-BK13BBqa.js";import{L as f}from"./label-NwAA2N0T.js";import{S as N,a as y,b as w,c as G,d as l}from"./select-IVIdgARa.js";import{P as Q}from"./plus-C74OdLeW.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./chevron-down-I8tOk39n.js";import"./check-abM7k-xd.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=b("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=b("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=b("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]),Z=()=>{const{houseId:h}=V(),c=B(),{addVisit:k,addDoor:S,getHouseById:C,getAddressById:O}=W(),[t,d]=v.useState([{id:"1",name:"",floor:"",status:"N/A"}]),[p,j]=v.useState(!1);if(!h)return c("/"),null;const u=C(h);if(!u)return c("/"),i.error("Haus nicht gefunden"),null;const m=O(u.addressId);if(!m)return c("/"),i.error("Adresse nicht gefunden"),null;const M=()=>{const s=(Math.max(...t.map(r=>parseInt(r.id)))+1).toString();d([...t,{id:s,name:"",floor:"",status:"N/A"}])},T=s=>{t.length>1&&d(t.filter(r=>r.id!==s))},I=s=>{const r=t[s],a=(Math.max(...t.map(F=>parseInt(F.id)))+1).toString(),n={...r,id:a,name:r.name+" (Kopie)",status:"N/A"};d([...t.slice(0,s+1),n,...t.slice(s+1)])},x=(s,r,a)=>{d(t.map(n=>n.id===s?{...n,[r]:a}:n))},g=s=>{let r=[];switch(s){case"small":r=[{name:"EG links",floor:"EG",status:"N/A"},{name:"EG rechts",floor:"EG",status:"N/A"},{name:"1. OG links",floor:"1. OG",status:"N/A"},{name:"1. OG rechts",floor:"1. OG",status:"N/A"}];break;case"medium":r=[{name:"EG Wohnung 1",floor:"EG",status:"N/A"},{name:"EG Wohnung 2",floor:"EG",status:"N/A"},{name:"1. OG Wohnung 3",floor:"1. OG",status:"N/A"},{name:"1. OG Wohnung 4",floor:"1. OG",status:"N/A"},{name:"2. OG Wohnung 5",floor:"2. OG",status:"N/A"},{name:"2. OG Wohnung 6",floor:"2. OG",status:"N/A"}];break;case"large":r=Array.from({length:12},(a,n)=>({name:`Wohnung ${n+1}`,floor:n<3?"EG":n<6?"1. OG":n<9?"2. OG":"3. OG",status:"N/A"}));break}d(r.map((a,n)=>({...a,id:(n+1).toString()}))),i.success(`${s==="small"?"Kleine":s==="medium"?"Mittlere":"Große"} MFH-Vorlage angewendet`)},z=()=>{if(t.length===0)return i.error("Bitte fügen Sie mindestens eine Tür hinzu"),!1;const s=t.filter(r=>!r.name.trim());return s.length>0?(i.error(`Bitte geben Sie Namen für alle ${s.length} Türen ein`),!1):!0},D=async()=>{if(z()){j(!0);try{const s=k({houseId:h,timestamp:new Date().toISOString(),status:"N/A"});let r=!1;t.forEach(a=>{S({visitId:s.id,name:a.name,floor:a.floor||void 0,status:a.status}),a.status==="Angetroffen → Sale"&&(r=!0)}),i.success(`Mehrfamilienhaus mit ${t.length} Türen erfolgreich gespeichert`),c(r?`/products/${s.id}`:"/daily-view")}catch(s){console.error(s),i.error("Fehler beim Speichern des Mehrfamilienhauses")}finally{j(!1)}}},E=s=>{switch(s){case"N/A":return"bg-gray-50 border-gray-200 text-gray-700";case"Angetroffen → Termin":return"bg-yellow-50 border-yellow-200 text-yellow-700";case"Angetroffen → Kein Interesse":return"bg-red-50 border-red-200 text-red-700";case"Angetroffen → Sale":return"bg-green-50 border-green-200 text-green-700";default:return"bg-gray-50 border-gray-200 text-gray-700"}};return e.jsxs($,{className:"w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden animate-fade-in",children:[e.jsxs(H,{className:"text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx(K,{className:"h-8 w-8 text-red-600"})}),e.jsx(L,{className:"text-2xl font-bold text-gray-800",children:"Mehrfamilienhaus"}),e.jsxs(P,{className:"text-lg mt-2",children:[m.street," ",u.houseNumber,", ",m.zipCode," ",m.city]})]}),e.jsxs(J,{className:"px-8 pt-6",children:[e.jsxs("div",{className:"mb-8 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200",children:[e.jsxs("h3",{className:"font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[e.jsx(A,{className:"h-4 w-4"}),"Schnell-Vorlagen"]}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsx(o,{type:"button",variant:"outline",size:"sm",onClick:()=>g("small"),className:"text-xs hover:bg-blue-100",children:"Klein (4 Türen)"}),e.jsx(o,{type:"button",variant:"outline",size:"sm",onClick:()=>g("medium"),className:"text-xs hover:bg-purple-100",children:"Mittel (6 Türen)"}),e.jsx(o,{type:"button",variant:"outline",size:"sm",onClick:()=>g("large"),className:"text-xs hover:bg-indigo-100",children:"Groß (12 Türen)"})]})]}),e.jsxs("div",{className:"space-y-4",children:[t.map((s,r)=>e.jsxs("div",{className:`p-6 border-2 rounded-2xl relative transition-all duration-200 hover:shadow-lg ${E(s.status)}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(X,{className:"h-5 w-5 text-gray-400 cursor-move"}),e.jsx(_,{className:"h-5 w-5 text-red-500"}),e.jsxs("h3",{className:"font-semibold text-lg",children:["Tür ",r+1]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{type:"button",variant:"ghost",size:"icon",onClick:()=>I(r),className:"h-8 w-8 hover:bg-blue-100",title:"Tür duplizieren",children:e.jsx(A,{size:14})}),t.length>1&&e.jsx(o,{type:"button",variant:"ghost",size:"icon",onClick:()=>T(s.id),className:"h-8 w-8 hover:bg-red-100",title:"Tür entfernen",children:e.jsx(Y,{size:14,className:"text-red-500"})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(f,{htmlFor:`name-${s.id}`,className:"text-sm font-semibold text-gray-700",children:"Name/Bewohner"}),e.jsx(q,{id:`name-${s.id}`,value:s.name,onChange:a=>x(s.id,"name",a.target.value),placeholder:"z.B. 'Familie Müller' oder 'Wohnung 3A'",className:"mt-1 h-12 text-base border-2 rounded-lg"})]}),e.jsxs("div",{children:[e.jsx(f,{htmlFor:`floor-${s.id}`,className:"text-sm font-semibold text-gray-700",children:"Stockwerk"}),e.jsxs(N,{value:s.floor,onValueChange:a=>x(s.id,"floor",a),children:[e.jsx(y,{className:"mt-1 h-12 text-base border-2 rounded-lg",children:e.jsx(w,{placeholder:"Wählen..."})}),e.jsxs(G,{children:[e.jsx(l,{value:"KG",children:"Keller"}),e.jsx(l,{value:"EG",children:"Erdgeschoss"}),e.jsx(l,{value:"1. OG",children:"1. Obergeschoss"}),e.jsx(l,{value:"2. OG",children:"2. Obergeschoss"}),e.jsx(l,{value:"3. OG",children:"3. Obergeschoss"}),e.jsx(l,{value:"4. OG",children:"4. Obergeschoss"}),e.jsx(l,{value:"DG",children:"Dachgeschoss"})]})]})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx(f,{htmlFor:`status-${s.id}`,className:"text-sm font-semibold text-gray-700",children:"Besuchsstatus"}),e.jsxs(N,{value:s.status,onValueChange:a=>x(s.id,"status",a),children:[e.jsx(y,{className:"mt-1 h-12 text-base border-2 rounded-lg",children:e.jsx(w,{})}),e.jsxs(G,{children:[e.jsx(l,{value:"N/A",children:"N/A (Nicht angetroffen)"}),e.jsx(l,{value:"Angetroffen → Termin",children:"Angetroffen → Termin"}),e.jsx(l,{value:"Angetroffen → Kein Interesse",children:"Angetroffen → Kein Interesse"}),e.jsx(l,{value:"Angetroffen → Sale",children:"Angetroffen → Sale"})]})]})]})]},s.id)),e.jsxs(o,{type:"button",variant:"outline",onClick:M,className:"w-full h-16 text-lg border-2 border-dashed border-gray-300 hover:border-red-400 hover:bg-red-50 rounded-xl transition-all duration-200",children:[e.jsx(Q,{size:20,className:"mr-2"}),"Weitere Tür hinzufügen"]})]})]}),e.jsx(U,{className:"px-8 pb-8",children:e.jsxs("div",{className:"w-full space-y-4",children:[e.jsxs("div",{className:"text-center text-sm text-gray-600",children:[t.length," ",t.length===1?"Tür":"Türen"," erfasst"]}),e.jsx(o,{onClick:D,className:"w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50",disabled:p||t.some(s=>!s.name.trim()),children:p?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"}),"Speichern..."]}):"Besuch abschließen und speichern"})]})})]})},de=()=>e.jsx(R,{title:"Mehrfamilienhaus verwalten",children:e.jsx("div",{className:"min-h-full flex items-start justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50",children:e.jsx("div",{className:"w-full max-w-4xl",children:e.jsx(Z,{})})})});export{de as default};
