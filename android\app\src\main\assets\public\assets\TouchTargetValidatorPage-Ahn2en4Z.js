import{r as i,j as e,k as N,C as y,i as T,T as w,B as r,R as C,l as S,W as v,X as x}from"./index-Cmt5neWh.js";import{M as E}from"./MainLayout-wyzz138D.js";import{B as V}from"./badge-XkNoLG2o.js";import{C as A}from"./circle-check-big-DK6RP7UF.js";import{E as L}from"./eye-off-j0pHjYk_.js";import{E as k}from"./eye-DwLHs0eg.js";import"./input-BK13BBqa.js";const z=()=>{const[a,g]=i.useState([]),[c,o]=i.useState(!1),[l,u]=i.useState(!1),[d,p]=i.useState(null),n=()=>{o(!0),setTimeout(()=>{const t=v();g(t),p(new Date),o(!1)},500)},f=()=>{document.querySelectorAll(".touch-target-violation-highlight").forEach(t=>{t.classList.remove("touch-target-violation-highlight")}),l&&a.forEach(t=>{t.element.classList.add("touch-target-violation-highlight")})},h=t=>{const s=t.element;s.tagName==="BUTTON"?(s.classList.add("mobile-button","touch-feedback"),s.getAttribute("data-primary")==="true"&&s.classList.add("mobile-action-button")):s.tagName==="INPUT"||s.tagName==="SELECT"||s.tagName==="TEXTAREA"?s.classList.add("mobile-input","touch-feedback"):s.getAttribute("role")==="tab"?s.classList.add("mobile-tab","touch-feedback"):(s.style.minHeight=`${x.MINIMUM}px`,s.style.minWidth=`${x.MINIMUM}px`,s.classList.add("touch-feedback")),setTimeout(n,100)},j=()=>{a.forEach(t=>{h(t)})};i.useEffect(()=>{n()},[]),i.useEffect(()=>{f()},[l,a]);const b=(t,s)=>{const m=Math.min(t,s);return m<32?"bg-red-100 text-red-800 border-red-200":m<40?"bg-orange-100 text-orange-800 border-orange-200":"bg-yellow-100 text-yellow-800 border-yellow-200"};return e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
        .touch-target-violation-highlight {
          outline: 3px solid #ef4444 !important;
          outline-offset: 2px !important;
          background-color: rgba(239, 68, 68, 0.1) !important;
          position: relative !important;
        }
        .touch-target-violation-highlight::after {
          content: '⚠️';
          position: absolute;
          top: -10px;
          right: -10px;
          background: #ef4444;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          z-index: 1000;
        }
      `}),e.jsxs(N,{className:"w-full max-w-4xl mx-auto",children:[e.jsx(y,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(T,{className:"flex items-center gap-2",children:[a.length===0?e.jsx(A,{className:"h-5 w-5 text-green-500"}):e.jsx(w,{className:"h-5 w-5 text-red-500"}),"Touch Target Validator"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(r,{variant:"outline",size:"sm",onClick:()=>u(!l),className:"flex items-center gap-2",children:[l?e.jsx(L,{className:"h-4 w-4"}):e.jsx(k,{className:"h-4 w-4"}),l?"Hide":"Show"," Highlights"]}),e.jsxs(r,{variant:"outline",size:"sm",onClick:n,disabled:c,className:"flex items-center gap-2",children:[e.jsx(C,{className:`h-4 w-4 ${c?"animate-spin":""}`}),"Validate"]})]})]})}),e.jsx(S,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:a.length===0?e.jsx("span",{className:"text-green-600 font-medium",children:"✅ All touch targets meet the 44px minimum requirement"}):e.jsxs("span",{className:"text-red-600 font-medium",children:["⚠️ Found ",a.length," touch target violations"]})}),d&&e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Last validated: ",d.toLocaleTimeString()]})]}),a.length>0&&e.jsx(r,{onClick:j,className:"bg-red-600 hover:bg-red-700 text-white",children:"Auto-Fix All"})]}),a.length>0&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg",children:"Touch Target Violations:"}),a.map((t,s)=>e.jsx("div",{className:"border rounded-lg p-4 space-y-2",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsxs(V,{className:b(t.currentSize.width,t.currentSize.height),children:[t.currentSize.width.toFixed(0),"×",t.currentSize.height.toFixed(0),"px"]}),e.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:t.tagName.toLowerCase()}),t.className&&e.jsxs("code",{className:"text-xs bg-blue-100 px-2 py-1 rounded text-blue-800",children:[".",t.className.split(" ").slice(0,2).join(".")]})]}),e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:[e.jsx("strong",{children:"Selector:"})," ",t.selector]}),t.textContent&&e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Content:"}),' "',t.textContent,'"']})]}),e.jsx(r,{size:"sm",onClick:()=>h(t),className:"ml-4",children:"Fix"})]})},s))]}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-blue-900 mb-2",children:"Touch Target Guidelines:"}),e.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Minimum:"})," 44×44px (Apple/Google standard)"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Comfortable:"})," 48×48px (recommended for primary actions)"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Large:"})," 56×56px (critical actions)"]}),e.jsxs("li",{children:["• Use ",e.jsx("code",{children:".mobile-button"}),", ",e.jsx("code",{children:".mobile-input"}),", ",e.jsx("code",{children:".mobile-action-button"})," classes"]}),e.jsxs("li",{children:["• Add ",e.jsx("code",{children:'data-primary="true"'})," for important buttons"]})]})]})]})})]})]})},G=()=>e.jsx(E,{title:"Touch Target Validator",children:e.jsx("div",{className:"w-full p-4",children:e.jsx(z,{})})});export{G as default};
