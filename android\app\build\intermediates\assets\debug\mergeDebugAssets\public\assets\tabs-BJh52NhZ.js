import{r as s,j as m,F as le,a as A}from"./index-Cmt5neWh.js";import{f as de,u as G,h as fe,P as R,a as I,e as k,c as ve,d as me}from"./input-BK13BBqa.js";import{u as O}from"./index-C8JwcrUT.js";function be(e,t=[]){let o=[];function n(d,r){const a=s.createContext(r),f=o.length;o=[...o,r];function i(u){const{scope:v,children:C,...l}=u,g=(v==null?void 0:v[e][f])||a,w=s.useMemo(()=>l,Object.values(l));return m.jsx(g.Provider,{value:w,children:C})}function b(u,v){const C=(v==null?void 0:v[e][f])||a,l=s.useContext(C);if(l)return l;if(r!==void 0)return r;throw new Error(`\`${u}\` must be used within \`${d}\``)}return i.displayName=d+"Provider",[i,b]}const c=()=>{const d=o.map(r=>s.createContext(r));return function(a){const f=(a==null?void 0:a[e])||d;return s.useMemo(()=>({[`__scope${e}`]:{...a,[e]:f}}),[a,f])}};return c.scopeName=e,[n,pe(c,...t)]}function pe(...e){const t=e[0];if(e.length===1)return t;const o=()=>{const n=e.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(d){const r=n.reduce((a,{useScope:f,scopeName:i})=>{const u=f(d)[`__scope${i}`];return{...a,...u}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return o.scopeName=t.scopeName,o}var _="rovingFocusGroup.onEntryFocus",ge={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[N,$,xe]=de(h),[Te,K]=be(h,[xe]),[Ie,Ce]=Te(h),L=s.forwardRef((e,t)=>m.jsx(N.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(N.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx(Fe,{...e,ref:t})})}));L.displayName=h;var Fe=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:o,orientation:n,loop:c=!1,dir:d,currentTabStopId:r,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:f,onEntryFocus:i,preventScrollOnEntryFocus:b=!1,...u}=e,v=s.useRef(null),C=le(t,v),l=O(d),[g=null,w]=G({prop:r,defaultProp:a,onChange:f}),[x,T]=s.useState(!1),S=fe(i),re=$(o),y=s.useRef(!1),[se,M]=s.useState(0);return s.useEffect(()=>{const p=v.current;if(p)return p.addEventListener(_,S),()=>p.removeEventListener(_,S)},[S]),m.jsx(Ie,{scope:o,orientation:n,dir:l,loop:c,currentTabStopId:g,onItemFocus:s.useCallback(p=>w(p),[w]),onItemShiftTab:s.useCallback(()=>T(!0),[]),onFocusableItemAdd:s.useCallback(()=>M(p=>p+1),[]),onFocusableItemRemove:s.useCallback(()=>M(p=>p-1),[]),children:m.jsx(R.div,{tabIndex:x||se===0?-1:0,"data-orientation":n,...u,ref:C,style:{outline:"none",...e.style},onMouseDown:I(e.onMouseDown,()=>{y.current=!0}),onFocus:I(e.onFocus,p=>{const ae=!y.current;if(p.target===p.currentTarget&&ae&&!x){const D=new CustomEvent(_,ge);if(p.currentTarget.dispatchEvent(D),!D.defaultPrevented){const E=re().filter(F=>F.focusable),ce=E.find(F=>F.active),ie=E.find(F=>F.id===g),ue=[ce,ie,...E].filter(Boolean).map(F=>F.ref.current);U(ue,b)}}y.current=!1}),onBlur:I(e.onBlur,()=>T(!1))})})}),V="RovingFocusGroupItem",B=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:o,focusable:n=!0,active:c=!1,tabStopId:d,...r}=e,a=k(),f=d||a,i=Ce(V,o),b=i.currentTabStopId===f,u=$(o),{onFocusableItemAdd:v,onFocusableItemRemove:C}=i;return s.useEffect(()=>{if(n)return v(),()=>C()},[n,v,C]),m.jsx(N.ItemSlot,{scope:o,id:f,focusable:n,active:c,children:m.jsx(R.span,{tabIndex:b?0:-1,"data-orientation":i.orientation,...r,ref:t,onMouseDown:I(e.onMouseDown,l=>{n?i.onItemFocus(f):l.preventDefault()}),onFocus:I(e.onFocus,()=>i.onItemFocus(f)),onKeyDown:I(e.onKeyDown,l=>{if(l.key==="Tab"&&l.shiftKey){i.onItemShiftTab();return}if(l.target!==l.currentTarget)return;const g=he(l,i.orientation,i.dir);if(g!==void 0){if(l.metaKey||l.ctrlKey||l.altKey||l.shiftKey)return;l.preventDefault();let x=u().filter(T=>T.focusable).map(T=>T.ref.current);if(g==="last")x.reverse();else if(g==="prev"||g==="next"){g==="prev"&&x.reverse();const T=x.indexOf(l.currentTarget);x=i.loop?Se(x,T+1):x.slice(T+1)}setTimeout(()=>U(x))}})})})});B.displayName=V;var Re={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function we(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function he(e,t,o){const n=we(e.key,o);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return Re[n]}function U(e,t=!1){const o=document.activeElement;for(const n of e)if(n===o||(n.focus({preventScroll:t}),document.activeElement!==o))return}function Se(e,t){return e.map((o,n)=>e[(t+n)%e.length])}var ye=L,Ee=B,P="Tabs",[_e,$e]=ve(P,[K]),H=K(),[Ne,j]=_e(P),Y=s.forwardRef((e,t)=>{const{__scopeTabs:o,value:n,onValueChange:c,defaultValue:d,orientation:r="horizontal",dir:a,activationMode:f="automatic",...i}=e,b=O(a),[u,v]=G({prop:n,onChange:c,defaultProp:d});return m.jsx(Ne,{scope:o,baseId:k(),value:u,onValueChange:v,orientation:r,dir:b,activationMode:f,children:m.jsx(R.div,{dir:b,"data-orientation":r,...i,ref:t})})});Y.displayName=P;var z="TabsList",q=s.forwardRef((e,t)=>{const{__scopeTabs:o,loop:n=!0,...c}=e,d=j(z,o),r=H(o);return m.jsx(ye,{asChild:!0,...r,orientation:d.orientation,dir:d.dir,loop:n,children:m.jsx(R.div,{role:"tablist","aria-orientation":d.orientation,...c,ref:t})})});q.displayName=z;var J="TabsTrigger",Q=s.forwardRef((e,t)=>{const{__scopeTabs:o,value:n,disabled:c=!1,...d}=e,r=j(J,o),a=H(o),f=Z(r.baseId,n),i=ee(r.baseId,n),b=n===r.value;return m.jsx(Ee,{asChild:!0,...a,focusable:!c,active:b,children:m.jsx(R.button,{type:"button",role:"tab","aria-selected":b,"aria-controls":i,"data-state":b?"active":"inactive","data-disabled":c?"":void 0,disabled:c,id:f,...d,ref:t,onMouseDown:I(e.onMouseDown,u=>{!c&&u.button===0&&u.ctrlKey===!1?r.onValueChange(n):u.preventDefault()}),onKeyDown:I(e.onKeyDown,u=>{[" ","Enter"].includes(u.key)&&r.onValueChange(n)}),onFocus:I(e.onFocus,()=>{const u=r.activationMode!=="manual";!b&&!c&&u&&r.onValueChange(n)})})})});Q.displayName=J;var W="TabsContent",X=s.forwardRef((e,t)=>{const{__scopeTabs:o,value:n,forceMount:c,children:d,...r}=e,a=j(W,o),f=Z(a.baseId,n),i=ee(a.baseId,n),b=n===a.value,u=s.useRef(b);return s.useEffect(()=>{const v=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(v)},[]),m.jsx(me,{present:c||b,children:({present:v})=>m.jsx(R.div,{"data-state":b?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":f,hidden:!v,id:i,tabIndex:0,...r,ref:t,style:{...e.style,animationDuration:u.current?"0s":void 0},children:v&&d})})});X.displayName=W;function Z(e,t){return`${e}-trigger-${t}`}function ee(e,t){return`${e}-content-${t}`}var Ae=Y,te=q,oe=Q,ne=X;const Ke=Ae,Pe=s.forwardRef(({className:e,...t},o)=>m.jsx(te,{ref:o,className:A("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Pe.displayName=te.displayName;const je=s.forwardRef(({className:e,...t},o)=>m.jsx(oe,{ref:o,className:A("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));je.displayName=oe.displayName;const Me=s.forwardRef(({className:e,...t},o)=>m.jsx(ne,{ref:o,className:A("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Me.displayName=ne.displayName;export{Ke as T,Pe as a,je as b,Me as c};
