import{o as Jn,p as ue,r as B,q as re,s as A}from"./index-Cmt5neWh.js";var $y=Array.isArray,Ne=$y,Ty=typeof Jn=="object"&&Jn&&Jn.Object===Object&&Jn,Yp=Ty,Ey=Yp,jy=typeof self=="object"&&self&&self.Object===Object&&self,My=Ey||jy||Function("return this")(),at=My,Cy=at,Iy=Cy.Symbol,Un=Iy,ul=Un,Zp=Object.prototype,Ny=Zp.hasOwnProperty,ky=Zp.toString,Hr=ul?ul.toStringTag:void 0;function Dy(e){var t=Ny.call(e,Hr),r=e[Hr];try{e[Hr]=void 0;var n=!0}catch{}var i=ky.call(e);return n&&(t?e[Hr]=r:delete e[Hr]),i}var By=Dy,Ry=Object.prototype,Ly=Ry.toString;function Fy(e){return Ly.call(e)}var Wy=Fy,cl=Un,zy=By,Uy=Wy,qy="[object Null]",Hy="[object Undefined]",ll=cl?cl.toStringTag:void 0;function Gy(e){return e==null?e===void 0?Hy:qy:ll&&ll in Object(e)?zy(e):Uy(e)}var wt=Gy;function Ky(e){return e!=null&&typeof e=="object"}var Ot=Ky,Xy=wt,Vy=Ot,Yy="[object Symbol]";function Zy(e){return typeof e=="symbol"||Vy(e)&&Xy(e)==Yy}var Mr=Zy,Jy=Ne,Qy=Mr,eg=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,tg=/^\w*$/;function rg(e,t){if(Jy(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Qy(e)?!0:tg.test(e)||!eg.test(e)||t!=null&&e in Object(t)}var Yu=rg;function ng(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var jt=ng;const Cr=ue(jt);var ig=wt,ag=jt,og="[object AsyncFunction]",ug="[object Function]",cg="[object GeneratorFunction]",lg="[object Proxy]";function sg(e){if(!ag(e))return!1;var t=ig(e);return t==ug||t==cg||t==og||t==lg}var Zu=sg;const Z=ue(Zu);var fg=at,pg=fg["__core-js_shared__"],hg=pg,Qa=hg,sl=function(){var e=/[^.]+$/.exec(Qa&&Qa.keys&&Qa.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function dg(e){return!!sl&&sl in e}var vg=dg,yg=Function.prototype,gg=yg.toString;function mg(e){if(e!=null){try{return gg.call(e)}catch{}try{return e+""}catch{}}return""}var Jp=mg,bg=Zu,xg=vg,wg=jt,Og=Jp,Sg=/[\\^$.*+?()[\]{}|]/g,_g=/^\[object .+?Constructor\]$/,Ag=Function.prototype,Pg=Object.prototype,$g=Ag.toString,Tg=Pg.hasOwnProperty,Eg=RegExp("^"+$g.call(Tg).replace(Sg,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function jg(e){if(!wg(e)||xg(e))return!1;var t=bg(e)?Eg:_g;return t.test(Og(e))}var Mg=jg;function Cg(e,t){return e==null?void 0:e[t]}var Ig=Cg,Ng=Mg,kg=Ig;function Dg(e,t){var r=kg(e,t);return Ng(r)?r:void 0}var Zt=Dg,Bg=Zt,Rg=Bg(Object,"create"),sa=Rg,fl=sa;function Lg(){this.__data__=fl?fl(null):{},this.size=0}var Fg=Lg;function Wg(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var zg=Wg,Ug=sa,qg="__lodash_hash_undefined__",Hg=Object.prototype,Gg=Hg.hasOwnProperty;function Kg(e){var t=this.__data__;if(Ug){var r=t[e];return r===qg?void 0:r}return Gg.call(t,e)?t[e]:void 0}var Xg=Kg,Vg=sa,Yg=Object.prototype,Zg=Yg.hasOwnProperty;function Jg(e){var t=this.__data__;return Vg?t[e]!==void 0:Zg.call(t,e)}var Qg=Jg,em=sa,tm="__lodash_hash_undefined__";function rm(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=em&&t===void 0?tm:t,this}var nm=rm,im=Fg,am=zg,om=Xg,um=Qg,cm=nm;function Ir(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ir.prototype.clear=im;Ir.prototype.delete=am;Ir.prototype.get=om;Ir.prototype.has=um;Ir.prototype.set=cm;var lm=Ir;function sm(){this.__data__=[],this.size=0}var fm=sm;function pm(e,t){return e===t||e!==e&&t!==t}var Ju=pm,hm=Ju;function dm(e,t){for(var r=e.length;r--;)if(hm(e[r][0],t))return r;return-1}var fa=dm,vm=fa,ym=Array.prototype,gm=ym.splice;function mm(e){var t=this.__data__,r=vm(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():gm.call(t,r,1),--this.size,!0}var bm=mm,xm=fa;function wm(e){var t=this.__data__,r=xm(t,e);return r<0?void 0:t[r][1]}var Om=wm,Sm=fa;function _m(e){return Sm(this.__data__,e)>-1}var Am=_m,Pm=fa;function $m(e,t){var r=this.__data__,n=Pm(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Tm=$m,Em=fm,jm=bm,Mm=Om,Cm=Am,Im=Tm;function Nr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Nr.prototype.clear=Em;Nr.prototype.delete=jm;Nr.prototype.get=Mm;Nr.prototype.has=Cm;Nr.prototype.set=Im;var pa=Nr,Nm=Zt,km=at,Dm=Nm(km,"Map"),Qu=Dm,pl=lm,Bm=pa,Rm=Qu;function Lm(){this.size=0,this.__data__={hash:new pl,map:new(Rm||Bm),string:new pl}}var Fm=Lm;function Wm(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var zm=Wm,Um=zm;function qm(e,t){var r=e.__data__;return Um(t)?r[typeof t=="string"?"string":"hash"]:r.map}var ha=qm,Hm=ha;function Gm(e){var t=Hm(this,e).delete(e);return this.size-=t?1:0,t}var Km=Gm,Xm=ha;function Vm(e){return Xm(this,e).get(e)}var Ym=Vm,Zm=ha;function Jm(e){return Zm(this,e).has(e)}var Qm=Jm,eb=ha;function tb(e,t){var r=eb(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var rb=tb,nb=Fm,ib=Km,ab=Ym,ob=Qm,ub=rb;function kr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}kr.prototype.clear=nb;kr.prototype.delete=ib;kr.prototype.get=ab;kr.prototype.has=ob;kr.prototype.set=ub;var ec=kr,Qp=ec,cb="Expected a function";function tc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(cb);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(tc.Cache||Qp),r}tc.Cache=Qp;var eh=tc;const lb=ue(eh);var sb=eh,fb=500;function pb(e){var t=sb(e,function(n){return r.size===fb&&r.clear(),n}),r=t.cache;return t}var hb=pb,db=hb,vb=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,yb=/\\(\\)?/g,gb=db(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(vb,function(r,n,i,a){t.push(i?a.replace(yb,"$1"):n||r)}),t}),mb=gb;function bb(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var rc=bb,hl=Un,xb=rc,wb=Ne,Ob=Mr,Sb=1/0,dl=hl?hl.prototype:void 0,vl=dl?dl.toString:void 0;function th(e){if(typeof e=="string")return e;if(wb(e))return xb(e,th)+"";if(Ob(e))return vl?vl.call(e):"";var t=e+"";return t=="0"&&1/e==-Sb?"-0":t}var _b=th,Ab=_b;function Pb(e){return e==null?"":Ab(e)}var rh=Pb,$b=Ne,Tb=Yu,Eb=mb,jb=rh;function Mb(e,t){return $b(e)?e:Tb(e,t)?[e]:Eb(jb(e))}var nh=Mb,Cb=Mr,Ib=1/0;function Nb(e){if(typeof e=="string"||Cb(e))return e;var t=e+"";return t=="0"&&1/e==-Ib?"-0":t}var da=Nb,kb=nh,Db=da;function Bb(e,t){t=kb(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[Db(t[r++])];return r&&r==n?e:void 0}var nc=Bb,Rb=nc;function Lb(e,t,r){var n=e==null?void 0:Rb(e,t);return n===void 0?r:n}var ih=Lb;const Ue=ue(ih);function Fb(e){return e==null}var Wb=Fb;const te=ue(Wb);var zb=wt,Ub=Ne,qb=Ot,Hb="[object String]";function Gb(e){return typeof e=="string"||!Ub(e)&&qb(e)&&zb(e)==Hb}var Kb=Gb;const qn=ue(Kb);var ah={exports:{}},ne={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ic=Symbol.for("react.element"),ac=Symbol.for("react.portal"),va=Symbol.for("react.fragment"),ya=Symbol.for("react.strict_mode"),ga=Symbol.for("react.profiler"),ma=Symbol.for("react.provider"),ba=Symbol.for("react.context"),Xb=Symbol.for("react.server_context"),xa=Symbol.for("react.forward_ref"),wa=Symbol.for("react.suspense"),Oa=Symbol.for("react.suspense_list"),Sa=Symbol.for("react.memo"),_a=Symbol.for("react.lazy"),Vb=Symbol.for("react.offscreen"),oh;oh=Symbol.for("react.module.reference");function He(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case ic:switch(e=e.type,e){case va:case ga:case ya:case wa:case Oa:return e;default:switch(e=e&&e.$$typeof,e){case Xb:case ba:case xa:case _a:case Sa:case ma:return e;default:return t}}case ac:return t}}}ne.ContextConsumer=ba;ne.ContextProvider=ma;ne.Element=ic;ne.ForwardRef=xa;ne.Fragment=va;ne.Lazy=_a;ne.Memo=Sa;ne.Portal=ac;ne.Profiler=ga;ne.StrictMode=ya;ne.Suspense=wa;ne.SuspenseList=Oa;ne.isAsyncMode=function(){return!1};ne.isConcurrentMode=function(){return!1};ne.isContextConsumer=function(e){return He(e)===ba};ne.isContextProvider=function(e){return He(e)===ma};ne.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===ic};ne.isForwardRef=function(e){return He(e)===xa};ne.isFragment=function(e){return He(e)===va};ne.isLazy=function(e){return He(e)===_a};ne.isMemo=function(e){return He(e)===Sa};ne.isPortal=function(e){return He(e)===ac};ne.isProfiler=function(e){return He(e)===ga};ne.isStrictMode=function(e){return He(e)===ya};ne.isSuspense=function(e){return He(e)===wa};ne.isSuspenseList=function(e){return He(e)===Oa};ne.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===va||e===ga||e===ya||e===wa||e===Oa||e===Vb||typeof e=="object"&&e!==null&&(e.$$typeof===_a||e.$$typeof===Sa||e.$$typeof===ma||e.$$typeof===ba||e.$$typeof===xa||e.$$typeof===oh||e.getModuleId!==void 0)};ne.typeOf=He;ah.exports=ne;var uh=ah.exports,Yb=wt,Zb=Ot,Jb="[object Number]";function Qb(e){return typeof e=="number"||Zb(e)&&Yb(e)==Jb}var ch=Qb;const e0=ue(ch);var t0=ch;function r0(e){return t0(e)&&e!=+e}var n0=r0;const Hn=ue(n0);var Ye=function(t){return t===0?0:t>0?1:-1},Wt=function(t){return qn(t)&&t.indexOf("%")===t.length-1},R=function(t){return e0(t)&&!Hn(t)},be=function(t){return R(t)||qn(t)},i0=0,Aa=function(t){var r=++i0;return"".concat(t||"").concat(r)},Ze=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!R(t)&&!qn(t))return n;var a;if(Wt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Hn(a)&&(a=n),i&&a>r&&(a=r),a},At=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},a0=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},er=function(t,r){return R(t)&&R(r)?function(n){return t+n*(r-t)}:function(){return r}};function To(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ue(n,t))===r})}function or(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Eo(e){"@babel/helpers - typeof";return Eo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Eo(e)}var o0=["viewBox","children"],u0=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],yl=["points","pathLength"],eo={svg:o0,polygon:yl,polyline:yl},oc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],hi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(B.isValidElement(t)&&(n=t.props),!Cr(n))return null;var i={};return Object.keys(n).forEach(function(a){oc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},c0=function(t,r,n){return function(i){return t(r,n,i),null}},di=function(t,r,n){if(!Cr(t)||Eo(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];oc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=c0(o,r,n))}),i},l0=["children"],s0=["children"];function gl(e,t){if(e==null)return{};var r=f0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function f0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ml={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},vt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},bl=null,to=null,uc=function e(t){if(t===bl&&Array.isArray(to))return to;var r=[];return B.Children.forEach(t,function(n){te(n)||(uh.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),to=r,bl=t,r};function Je(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return vt(i)}):n=[vt(t)],uc(e).forEach(function(i){var a=Ue(i,"type.displayName")||Ue(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Be(e,t){var r=Je(e,t);return r&&r[0]}var xl=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!R(n)||n<=0||!R(i)||i<=0)},p0=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],h0=function(t){return t&&t.type&&qn(t.type)&&p0.indexOf(t.type)>=0},d0=function(t,r,n,i){var a,o=(a=eo==null?void 0:eo[i])!==null&&a!==void 0?a:[];return!Z(t)&&(i&&o.includes(r)||u0.includes(r))||n&&oc.includes(r)},ee=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(B.isValidElement(t)&&(i=t.props),!Cr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;d0((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},jo=function e(t,r){if(t===r)return!0;var n=B.Children.count(t);if(n!==B.Children.count(r))return!1;if(n===0)return!0;if(n===1)return wl(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!wl(a,o))return!1}return!0},wl=function(t,r){if(te(t)&&te(r))return!0;if(!te(t)&&!te(r)){var n=t.props||{},i=n.children,a=gl(n,l0),o=r.props||{},u=o.children,c=gl(o,s0);return i&&u?or(a,c)&&jo(i,u):!i&&!u?or(a,c):!1}return!1},Ol=function(t,r){var n=[],i={};return uc(t).forEach(function(a,o){if(h0(a))n.push(a);else if(a){var u=vt(a.type),c=r[u]||{},l=c.handler,f=c.once;if(l&&(!f||!i[u])){var s=l(a,u,o);n.push(s),i[u]=!0}}}),n},v0=function(t){var r=t&&t.type;return r&&ml[r]?ml[r]:null},y0=function(t,r){return uc(r).indexOf(t)},g0=["children","width","height","viewBox","className","style","title","desc"];function Mo(){return Mo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mo.apply(this,arguments)}function m0(e,t){if(e==null)return{};var r=b0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function b0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Co(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,l=m0(e,g0),f=i||{width:r,height:n,x:0,y:0},s=re("recharts-surface",a);return A.createElement("svg",Mo({},ee(l,!0,"svg"),{className:s,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),A.createElement("title",null,u),A.createElement("desc",null,c),t)}var x0=["children","className"];function Io(){return Io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Io.apply(this,arguments)}function w0(e,t){if(e==null)return{};var r=O0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function O0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Oe=A.forwardRef(function(e,t){var r=e.children,n=e.className,i=w0(e,x0),a=re("recharts-layer",n);return A.createElement("g",Io({className:a},ee(i,!0),{ref:t}),r)}),yt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function S0(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var _0=S0,A0=_0;function P0(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:A0(e,t,r)}var $0=P0,T0="\\ud800-\\udfff",E0="\\u0300-\\u036f",j0="\\ufe20-\\ufe2f",M0="\\u20d0-\\u20ff",C0=E0+j0+M0,I0="\\ufe0e\\ufe0f",N0="\\u200d",k0=RegExp("["+N0+T0+C0+I0+"]");function D0(e){return k0.test(e)}var lh=D0;function B0(e){return e.split("")}var R0=B0,sh="\\ud800-\\udfff",L0="\\u0300-\\u036f",F0="\\ufe20-\\ufe2f",W0="\\u20d0-\\u20ff",z0=L0+F0+W0,U0="\\ufe0e\\ufe0f",q0="["+sh+"]",No="["+z0+"]",ko="\\ud83c[\\udffb-\\udfff]",H0="(?:"+No+"|"+ko+")",fh="[^"+sh+"]",ph="(?:\\ud83c[\\udde6-\\uddff]){2}",hh="[\\ud800-\\udbff][\\udc00-\\udfff]",G0="\\u200d",dh=H0+"?",vh="["+U0+"]?",K0="(?:"+G0+"(?:"+[fh,ph,hh].join("|")+")"+vh+dh+")*",X0=vh+dh+K0,V0="(?:"+[fh+No+"?",No,ph,hh,q0].join("|")+")",Y0=RegExp(ko+"(?="+ko+")|"+V0+X0,"g");function Z0(e){return e.match(Y0)||[]}var J0=Z0,Q0=R0,ex=lh,tx=J0;function rx(e){return ex(e)?tx(e):Q0(e)}var nx=rx,ix=$0,ax=lh,ox=nx,ux=rh;function cx(e){return function(t){t=ux(t);var r=ax(t)?ox(t):void 0,n=r?r[0]:t.charAt(0),i=r?ix(r,1).join(""):t.slice(1);return n[e]()+i}}var lx=cx,sx=lx,fx=sx("toUpperCase"),px=fx;const Pa=ue(px);function oe(e){return function(){return e}}const yh=Math.cos,vi=Math.sin,Qe=Math.sqrt,yi=Math.PI,$a=2*yi,Do=Math.PI,Bo=2*Do,Rt=1e-6,hx=Bo-Rt;function gh(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function dx(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return gh;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class vx{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?gh:dx(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,l=i-r,f=o-t,s=u-r,p=f*f+s*s;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Rt)if(!(Math.abs(s*c-l*f)>Rt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,y=i-u,v=c*c+l*l,d=h*h+y*y,w=Math.sqrt(v),b=Math.sqrt(p),x=a*Math.tan((Do-Math.acos((v+p-d)/(2*w*b)))/2),m=x/b,g=x/w;Math.abs(m-1)>Rt&&this._append`L${t+m*f},${r+m*s}`,this._append`A${a},${a},0,0,${+(s*h>f*y)},${this._x1=t+g*c},${this._y1=r+g*l}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),l=t+u,f=r+c,s=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${l},${f}`:(Math.abs(this._x1-l)>Rt||Math.abs(this._y1-f)>Rt)&&this._append`L${l},${f}`,n&&(p<0&&(p=p%Bo+Bo),p>hx?this._append`A${n},${n},0,1,${s},${t-u},${r-c}A${n},${n},0,1,${s},${this._x1=l},${this._y1=f}`:p>Rt&&this._append`A${n},${n},0,${+(p>=Do)},${s},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function cc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new vx(t)}function lc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function mh(e){this._context=e}mh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Ta(e){return new mh(e)}function bh(e){return e[0]}function xh(e){return e[1]}function wh(e,t){var r=oe(!0),n=null,i=Ta,a=null,o=cc(u);e=typeof e=="function"?e:e===void 0?bh:oe(e),t=typeof t=="function"?t:t===void 0?xh:oe(t);function u(c){var l,f=(c=lc(c)).length,s,p=!1,h;for(n==null&&(a=i(h=o())),l=0;l<=f;++l)!(l<f&&r(s=c[l],l,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(s,l,c),+t(s,l,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function Qn(e,t,r){var n=null,i=oe(!0),a=null,o=Ta,u=null,c=cc(l);e=typeof e=="function"?e:e===void 0?bh:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?xh:oe(+r);function l(s){var p,h,y,v=(s=lc(s)).length,d,w=!1,b,x=new Array(v),m=new Array(v);for(a==null&&(u=o(b=c())),p=0;p<=v;++p){if(!(p<v&&i(d=s[p],p,s))===w)if(w=!w)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=p-1;y>=h;--y)u.point(x[y],m[y]);u.lineEnd(),u.areaEnd()}w&&(x[p]=+e(d,p,s),m[p]=+t(d,p,s),u.point(n?+n(d,p,s):x[p],r?+r(d,p,s):m[p]))}if(b)return u=null,b+""||null}function f(){return wh().defined(i).curve(o).context(a)}return l.x=function(s){return arguments.length?(e=typeof s=="function"?s:oe(+s),n=null,l):e},l.x0=function(s){return arguments.length?(e=typeof s=="function"?s:oe(+s),l):e},l.x1=function(s){return arguments.length?(n=s==null?null:typeof s=="function"?s:oe(+s),l):n},l.y=function(s){return arguments.length?(t=typeof s=="function"?s:oe(+s),r=null,l):t},l.y0=function(s){return arguments.length?(t=typeof s=="function"?s:oe(+s),l):t},l.y1=function(s){return arguments.length?(r=s==null?null:typeof s=="function"?s:oe(+s),l):r},l.lineX0=l.lineY0=function(){return f().x(e).y(t)},l.lineY1=function(){return f().x(e).y(r)},l.lineX1=function(){return f().x(n).y(t)},l.defined=function(s){return arguments.length?(i=typeof s=="function"?s:oe(!!s),l):i},l.curve=function(s){return arguments.length?(o=s,a!=null&&(u=o(a)),l):o},l.context=function(s){return arguments.length?(s==null?a=u=null:u=o(a=s),l):a},l}class Oh{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function yx(e){return new Oh(e,!0)}function gx(e){return new Oh(e,!1)}const sc={draw(e,t){const r=Qe(t/yi);e.moveTo(r,0),e.arc(0,0,r,0,$a)}},mx={draw(e,t){const r=Qe(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Sh=Qe(1/3),bx=Sh*2,xx={draw(e,t){const r=Qe(t/bx),n=r*Sh;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},wx={draw(e,t){const r=Qe(t),n=-r/2;e.rect(n,n,r,r)}},Ox=.8908130915292852,_h=vi(yi/10)/vi(7*yi/10),Sx=vi($a/10)*_h,_x=-yh($a/10)*_h,Ax={draw(e,t){const r=Qe(t*Ox),n=Sx*r,i=_x*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=$a*a/5,u=yh(o),c=vi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},ro=Qe(3),Px={draw(e,t){const r=-Qe(t/(ro*3));e.moveTo(0,r*2),e.lineTo(-ro*r,-r),e.lineTo(ro*r,-r),e.closePath()}},Le=-.5,Fe=Qe(3)/2,Ro=1/Qe(12),$x=(Ro/2+1)*3,Tx={draw(e,t){const r=Qe(t/$x),n=r/2,i=r*Ro,a=n,o=r*Ro+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Le*n-Fe*i,Fe*n+Le*i),e.lineTo(Le*a-Fe*o,Fe*a+Le*o),e.lineTo(Le*u-Fe*c,Fe*u+Le*c),e.lineTo(Le*n+Fe*i,Le*i-Fe*n),e.lineTo(Le*a+Fe*o,Le*o-Fe*a),e.lineTo(Le*u+Fe*c,Le*c-Fe*u),e.closePath()}};function Ex(e,t){let r=null,n=cc(i);e=typeof e=="function"?e:oe(e||sc),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function gi(){}function mi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Ah(e){this._context=e}Ah.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:mi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:mi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function jx(e){return new Ah(e)}function Ph(e){this._context=e}Ph.prototype={areaStart:gi,areaEnd:gi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:mi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Mx(e){return new Ph(e)}function $h(e){this._context=e}$h.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:mi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Cx(e){return new $h(e)}function Th(e){this._context=e}Th.prototype={areaStart:gi,areaEnd:gi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Ix(e){return new Th(e)}function Sl(e){return e<0?-1:1}function _l(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Sl(a)+Sl(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Al(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function no(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function bi(e){this._context=e}bi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:no(this,this._t0,Al(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,no(this,Al(this,r=_l(this,e,t)),r);break;default:no(this,this._t0,r=_l(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Eh(e){this._context=new jh(e)}(Eh.prototype=Object.create(bi.prototype)).point=function(e,t){bi.prototype.point.call(this,t,e)};function jh(e){this._context=e}jh.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Nx(e){return new bi(e)}function kx(e){return new Eh(e)}function Mh(e){this._context=e}Mh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Pl(e),i=Pl(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Pl(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Dx(e){return new Mh(e)}function Ea(e,t){this._context=e,this._t=t}Ea.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Bx(e){return new Ea(e,.5)}function Rx(e){return new Ea(e,0)}function Lx(e){return new Ea(e,1)}function sr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Lo(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Fx(e,t){return e[t]}function Wx(e){const t=[];return t.key=e,t}function zx(){var e=oe([]),t=Lo,r=sr,n=Fx;function i(a){var o=Array.from(e.apply(this,arguments),Wx),u,c=o.length,l=-1,f;for(const s of a)for(u=0,++l;u<c;++u)(o[u][l]=[0,+n(s,o[u].key,l,a)]).data=s;for(u=0,f=lc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Lo:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??sr,i):r},i}function Ux(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}sr(e,t)}}function qx(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}sr(e,t)}}function Hx(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,l=0;u<o;++u){for(var f=e[t[u]],s=f[n][1]||0,p=f[n-1][1]||0,h=(s-p)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,w=v[n-1][1]||0;h+=d-w}c+=s,l+=h*s}i[n-1][1]+=i[n-1][0]=r,c&&(r-=l/c)}i[n-1][1]+=i[n-1][0]=r,sr(e,t)}}function cn(e){"@babel/helpers - typeof";return cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cn(e)}var Gx=["type","size","sizeType"];function Fo(){return Fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fo.apply(this,arguments)}function $l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Tl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$l(Object(r),!0).forEach(function(n){Kx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$l(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Kx(e,t,r){return t=Xx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xx(e){var t=Vx(e,"string");return cn(t)=="symbol"?t:t+""}function Vx(e,t){if(cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Yx(e,t){if(e==null)return{};var r=Zx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Zx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Ch={symbolCircle:sc,symbolCross:mx,symbolDiamond:xx,symbolSquare:wx,symbolStar:Ax,symbolTriangle:Px,symbolWye:Tx},Jx=Math.PI/180,Qx=function(t){var r="symbol".concat(Pa(t));return Ch[r]||sc},ew=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*Jx;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},tw=function(t,r){Ch["symbol".concat(Pa(t))]=r},fc=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=Yx(t,Gx),l=Tl(Tl({},c),{},{type:n,size:a,sizeType:u}),f=function(){var d=Qx(n),w=Ex().type(d).size(ew(a,u,n));return w()},s=l.className,p=l.cx,h=l.cy,y=ee(l,!0);return p===+p&&h===+h&&a===+a?A.createElement("path",Fo({},y,{className:re("recharts-symbols",s),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};fc.registerSymbol=tw;function fr(e){"@babel/helpers - typeof";return fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fr(e)}function Wo(){return Wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wo.apply(this,arguments)}function El(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?El(Object(r),!0).forEach(function(n){ln(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):El(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nh(n.key),n)}}function aw(e,t,r){return t&&iw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ow(e,t,r){return t=xi(t),uw(e,Ih()?Reflect.construct(t,r||[],xi(e).constructor):t.apply(e,r))}function uw(e,t){if(t&&(fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cw(e)}function cw(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ih(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ih=function(){return!!e})()}function xi(e){return xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xi(e)}function lw(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zo(e,t)}function zo(e,t){return zo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},zo(e,t)}function ln(e,t,r){return t=Nh(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nh(e){var t=sw(e,"string");return fr(t)=="symbol"?t:t+""}function sw(e,t){if(fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var We=32,pc=function(e){function t(){return nw(this,t),ow(this,t,arguments)}return lw(t,e),aw(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=We/2,o=We/6,u=We/3,c=n.inactive?i:n.color;if(n.type==="plainline")return A.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:We,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return A.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(We,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return A.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(We/8,"h").concat(We,"v").concat(We*3/4,"h").concat(-We,"z"),className:"recharts-legend-icon"});if(A.isValidElement(n.legendIcon)){var l=rw({},n);return delete l.legendIcon,A.cloneElement(n.legendIcon,l)}return A.createElement(fc,{fill:c,cx:a,cy:a,size:We,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,l=i.inactiveColor,f={x:0,y:0,width:We,height:We},s={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,y){var v=h.formatter||c,d=re(ln(ln({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",h.inactive));if(h.type==="none")return null;var w=Z(h.value)?null:h.value;yt(!Z(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var b=h.inactive?l:h.color;return A.createElement("li",Wo({className:d,style:s,key:"legend-item-".concat(y)},di(n.props,h,y)),A.createElement(Co,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),A.createElement("span",{className:"recharts-legend-item-text",style:{color:b}},v?v(w,h,y):w))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return A.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(B.PureComponent);ln(pc,"displayName","Legend");ln(pc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var fw=pa;function pw(){this.__data__=new fw,this.size=0}var hw=pw;function dw(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var vw=dw;function yw(e){return this.__data__.get(e)}var gw=yw;function mw(e){return this.__data__.has(e)}var bw=mw,xw=pa,ww=Qu,Ow=ec,Sw=200;function _w(e,t){var r=this.__data__;if(r instanceof xw){var n=r.__data__;if(!ww||n.length<Sw-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Ow(n)}return r.set(e,t),this.size=r.size,this}var Aw=_w,Pw=pa,$w=hw,Tw=vw,Ew=gw,jw=bw,Mw=Aw;function Dr(e){var t=this.__data__=new Pw(e);this.size=t.size}Dr.prototype.clear=$w;Dr.prototype.delete=Tw;Dr.prototype.get=Ew;Dr.prototype.has=jw;Dr.prototype.set=Mw;var kh=Dr,Cw="__lodash_hash_undefined__";function Iw(e){return this.__data__.set(e,Cw),this}var Nw=Iw;function kw(e){return this.__data__.has(e)}var Dw=kw,Bw=ec,Rw=Nw,Lw=Dw;function wi(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new Bw;++t<r;)this.add(e[t])}wi.prototype.add=wi.prototype.push=Rw;wi.prototype.has=Lw;var Dh=wi;function Fw(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Bh=Fw;function Ww(e,t){return e.has(t)}var Rh=Ww,zw=Dh,Uw=Bh,qw=Rh,Hw=1,Gw=2;function Kw(e,t,r,n,i,a){var o=r&Hw,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var l=a.get(e),f=a.get(t);if(l&&f)return l==t&&f==e;var s=-1,p=!0,h=r&Gw?new zw:void 0;for(a.set(e,t),a.set(t,e);++s<u;){var y=e[s],v=t[s];if(n)var d=o?n(v,y,s,t,e,a):n(y,v,s,e,t,a);if(d!==void 0){if(d)continue;p=!1;break}if(h){if(!Uw(t,function(w,b){if(!qw(h,b)&&(y===w||i(y,w,r,n,a)))return h.push(b)})){p=!1;break}}else if(!(y===v||i(y,v,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Lh=Kw,Xw=at,Vw=Xw.Uint8Array,Yw=Vw;function Zw(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var Jw=Zw;function Qw(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var hc=Qw,jl=Un,Ml=Yw,e1=Ju,t1=Lh,r1=Jw,n1=hc,i1=1,a1=2,o1="[object Boolean]",u1="[object Date]",c1="[object Error]",l1="[object Map]",s1="[object Number]",f1="[object RegExp]",p1="[object Set]",h1="[object String]",d1="[object Symbol]",v1="[object ArrayBuffer]",y1="[object DataView]",Cl=jl?jl.prototype:void 0,io=Cl?Cl.valueOf:void 0;function g1(e,t,r,n,i,a,o){switch(r){case y1:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case v1:return!(e.byteLength!=t.byteLength||!a(new Ml(e),new Ml(t)));case o1:case u1:case s1:return e1(+e,+t);case c1:return e.name==t.name&&e.message==t.message;case f1:case h1:return e==t+"";case l1:var u=r1;case p1:var c=n&i1;if(u||(u=n1),e.size!=t.size&&!c)return!1;var l=o.get(e);if(l)return l==t;n|=a1,o.set(e,t);var f=t1(u(e),u(t),n,i,a,o);return o.delete(e),f;case d1:if(io)return io.call(e)==io.call(t)}return!1}var m1=g1;function b1(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var Fh=b1,x1=Fh,w1=Ne;function O1(e,t,r){var n=t(e);return w1(e)?n:x1(n,r(e))}var S1=O1;function _1(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var A1=_1;function P1(){return[]}var $1=P1,T1=A1,E1=$1,j1=Object.prototype,M1=j1.propertyIsEnumerable,Il=Object.getOwnPropertySymbols,C1=Il?function(e){return e==null?[]:(e=Object(e),T1(Il(e),function(t){return M1.call(e,t)}))}:E1,I1=C1;function N1(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var k1=N1,D1=wt,B1=Ot,R1="[object Arguments]";function L1(e){return B1(e)&&D1(e)==R1}var F1=L1,Nl=F1,W1=Ot,Wh=Object.prototype,z1=Wh.hasOwnProperty,U1=Wh.propertyIsEnumerable,q1=Nl(function(){return arguments}())?Nl:function(e){return W1(e)&&z1.call(e,"callee")&&!U1.call(e,"callee")},dc=q1,Oi={exports:{}};function H1(){return!1}var G1=H1;Oi.exports;(function(e,t){var r=at,n=G1,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,l=c||n;e.exports=l})(Oi,Oi.exports);var zh=Oi.exports,K1=9007199254740991,X1=/^(?:0|[1-9]\d*)$/;function V1(e,t){var r=typeof e;return t=t??K1,!!t&&(r=="number"||r!="symbol"&&X1.test(e))&&e>-1&&e%1==0&&e<t}var vc=V1,Y1=9007199254740991;function Z1(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Y1}var yc=Z1,J1=wt,Q1=yc,eO=Ot,tO="[object Arguments]",rO="[object Array]",nO="[object Boolean]",iO="[object Date]",aO="[object Error]",oO="[object Function]",uO="[object Map]",cO="[object Number]",lO="[object Object]",sO="[object RegExp]",fO="[object Set]",pO="[object String]",hO="[object WeakMap]",dO="[object ArrayBuffer]",vO="[object DataView]",yO="[object Float32Array]",gO="[object Float64Array]",mO="[object Int8Array]",bO="[object Int16Array]",xO="[object Int32Array]",wO="[object Uint8Array]",OO="[object Uint8ClampedArray]",SO="[object Uint16Array]",_O="[object Uint32Array]",le={};le[yO]=le[gO]=le[mO]=le[bO]=le[xO]=le[wO]=le[OO]=le[SO]=le[_O]=!0;le[tO]=le[rO]=le[dO]=le[nO]=le[vO]=le[iO]=le[aO]=le[oO]=le[uO]=le[cO]=le[lO]=le[sO]=le[fO]=le[pO]=le[hO]=!1;function AO(e){return eO(e)&&Q1(e.length)&&!!le[J1(e)]}var PO=AO;function $O(e){return function(t){return e(t)}}var Uh=$O,Si={exports:{}};Si.exports;(function(e,t){var r=Yp,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Si,Si.exports);var TO=Si.exports,EO=PO,jO=Uh,kl=TO,Dl=kl&&kl.isTypedArray,MO=Dl?jO(Dl):EO,qh=MO,CO=k1,IO=dc,NO=Ne,kO=zh,DO=vc,BO=qh,RO=Object.prototype,LO=RO.hasOwnProperty;function FO(e,t){var r=NO(e),n=!r&&IO(e),i=!r&&!n&&kO(e),a=!r&&!n&&!i&&BO(e),o=r||n||i||a,u=o?CO(e.length,String):[],c=u.length;for(var l in e)(t||LO.call(e,l))&&!(o&&(l=="length"||i&&(l=="offset"||l=="parent")||a&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||DO(l,c)))&&u.push(l);return u}var WO=FO,zO=Object.prototype;function UO(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||zO;return e===r}var qO=UO;function HO(e,t){return function(r){return e(t(r))}}var Hh=HO,GO=Hh,KO=GO(Object.keys,Object),XO=KO,VO=qO,YO=XO,ZO=Object.prototype,JO=ZO.hasOwnProperty;function QO(e){if(!VO(e))return YO(e);var t=[];for(var r in Object(e))JO.call(e,r)&&r!="constructor"&&t.push(r);return t}var eS=QO,tS=Zu,rS=yc;function nS(e){return e!=null&&rS(e.length)&&!tS(e)}var Gn=nS,iS=WO,aS=eS,oS=Gn;function uS(e){return oS(e)?iS(e):aS(e)}var ja=uS,cS=S1,lS=I1,sS=ja;function fS(e){return cS(e,sS,lS)}var pS=fS,Bl=pS,hS=1,dS=Object.prototype,vS=dS.hasOwnProperty;function yS(e,t,r,n,i,a){var o=r&hS,u=Bl(e),c=u.length,l=Bl(t),f=l.length;if(c!=f&&!o)return!1;for(var s=c;s--;){var p=u[s];if(!(o?p in t:vS.call(t,p)))return!1}var h=a.get(e),y=a.get(t);if(h&&y)return h==t&&y==e;var v=!0;a.set(e,t),a.set(t,e);for(var d=o;++s<c;){p=u[s];var w=e[p],b=t[p];if(n)var x=o?n(b,w,p,t,e,a):n(w,b,p,e,t,a);if(!(x===void 0?w===b||i(w,b,r,n,a):x)){v=!1;break}d||(d=p=="constructor")}if(v&&!d){var m=e.constructor,g=t.constructor;m!=g&&"constructor"in e&&"constructor"in t&&!(typeof m=="function"&&m instanceof m&&typeof g=="function"&&g instanceof g)&&(v=!1)}return a.delete(e),a.delete(t),v}var gS=yS,mS=Zt,bS=at,xS=mS(bS,"DataView"),wS=xS,OS=Zt,SS=at,_S=OS(SS,"Promise"),AS=_S,PS=Zt,$S=at,TS=PS($S,"Set"),Gh=TS,ES=Zt,jS=at,MS=ES(jS,"WeakMap"),CS=MS,Uo=wS,qo=Qu,Ho=AS,Go=Gh,Ko=CS,Kh=wt,Br=Jp,Rl="[object Map]",IS="[object Object]",Ll="[object Promise]",Fl="[object Set]",Wl="[object WeakMap]",zl="[object DataView]",NS=Br(Uo),kS=Br(qo),DS=Br(Ho),BS=Br(Go),RS=Br(Ko),Lt=Kh;(Uo&&Lt(new Uo(new ArrayBuffer(1)))!=zl||qo&&Lt(new qo)!=Rl||Ho&&Lt(Ho.resolve())!=Ll||Go&&Lt(new Go)!=Fl||Ko&&Lt(new Ko)!=Wl)&&(Lt=function(e){var t=Kh(e),r=t==IS?e.constructor:void 0,n=r?Br(r):"";if(n)switch(n){case NS:return zl;case kS:return Rl;case DS:return Ll;case BS:return Fl;case RS:return Wl}return t});var LS=Lt,ao=kh,FS=Lh,WS=m1,zS=gS,Ul=LS,ql=Ne,Hl=zh,US=qh,qS=1,Gl="[object Arguments]",Kl="[object Array]",ei="[object Object]",HS=Object.prototype,Xl=HS.hasOwnProperty;function GS(e,t,r,n,i,a){var o=ql(e),u=ql(t),c=o?Kl:Ul(e),l=u?Kl:Ul(t);c=c==Gl?ei:c,l=l==Gl?ei:l;var f=c==ei,s=l==ei,p=c==l;if(p&&Hl(e)){if(!Hl(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new ao),o||US(e)?FS(e,t,r,n,i,a):WS(e,t,c,r,n,i,a);if(!(r&qS)){var h=f&&Xl.call(e,"__wrapped__"),y=s&&Xl.call(t,"__wrapped__");if(h||y){var v=h?e.value():e,d=y?t.value():t;return a||(a=new ao),i(v,d,r,n,a)}}return p?(a||(a=new ao),zS(e,t,r,n,i,a)):!1}var KS=GS,XS=KS,Vl=Ot;function Xh(e,t,r,n,i){return e===t?!0:e==null||t==null||!Vl(e)&&!Vl(t)?e!==e&&t!==t:XS(e,t,r,n,Xh,i)}var gc=Xh,VS=kh,YS=gc,ZS=1,JS=2;function QS(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],l=e[c],f=u[1];if(o&&u[2]){if(l===void 0&&!(c in e))return!1}else{var s=new VS;if(n)var p=n(l,f,c,e,t,s);if(!(p===void 0?YS(f,l,ZS|JS,n,s):p))return!1}}return!0}var e_=QS,t_=jt;function r_(e){return e===e&&!t_(e)}var Vh=r_,n_=Vh,i_=ja;function a_(e){for(var t=i_(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,n_(i)]}return t}var o_=a_;function u_(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var Yh=u_,c_=e_,l_=o_,s_=Yh;function f_(e){var t=l_(e);return t.length==1&&t[0][2]?s_(t[0][0],t[0][1]):function(r){return r===e||c_(r,e,t)}}var p_=f_;function h_(e,t){return e!=null&&t in Object(e)}var d_=h_,v_=nh,y_=dc,g_=Ne,m_=vc,b_=yc,x_=da;function w_(e,t,r){t=v_(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=x_(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&b_(i)&&m_(o,i)&&(g_(e)||y_(e)))}var O_=w_,S_=d_,__=O_;function A_(e,t){return e!=null&&__(e,t,S_)}var P_=A_,$_=gc,T_=ih,E_=P_,j_=Yu,M_=Vh,C_=Yh,I_=da,N_=1,k_=2;function D_(e,t){return j_(e)&&M_(t)?C_(I_(e),t):function(r){var n=T_(r,e);return n===void 0&&n===t?E_(r,e):$_(t,n,N_|k_)}}var B_=D_;function R_(e){return e}var Rr=R_;function L_(e){return function(t){return t==null?void 0:t[e]}}var F_=L_,W_=nc;function z_(e){return function(t){return W_(t,e)}}var U_=z_,q_=F_,H_=U_,G_=Yu,K_=da;function X_(e){return G_(e)?q_(K_(e)):H_(e)}var V_=X_,Y_=p_,Z_=B_,J_=Rr,Q_=Ne,eA=V_;function tA(e){return typeof e=="function"?e:e==null?J_:typeof e=="object"?Q_(e)?Z_(e[0],e[1]):Y_(e):eA(e)}var Mt=tA;function rA(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var Zh=rA;function nA(e){return e!==e}var iA=nA;function aA(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var oA=aA,uA=Zh,cA=iA,lA=oA;function sA(e,t,r){return t===t?lA(e,t,r):uA(e,cA,r)}var fA=sA,pA=fA;function hA(e,t){var r=e==null?0:e.length;return!!r&&pA(e,t,0)>-1}var dA=hA;function vA(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var yA=vA;function gA(){}var mA=gA,oo=Gh,bA=mA,xA=hc,wA=1/0,OA=oo&&1/xA(new oo([,-0]))[1]==wA?function(e){return new oo(e)}:bA,SA=OA,_A=Dh,AA=dA,PA=yA,$A=Rh,TA=SA,EA=hc,jA=200;function MA(e,t,r){var n=-1,i=AA,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=PA;else if(a>=jA){var l=t?null:TA(e);if(l)return EA(l);o=!1,i=$A,c=new _A}else c=t?[]:u;e:for(;++n<a;){var f=e[n],s=t?t(f):f;if(f=r||f!==0?f:0,o&&s===s){for(var p=c.length;p--;)if(c[p]===s)continue e;t&&c.push(s),u.push(f)}else i(c,s,r)||(c!==u&&c.push(s),u.push(f))}return u}var CA=MA,IA=Mt,NA=CA;function kA(e,t){return e&&e.length?NA(e,IA(t)):[]}var DA=kA;const Yl=ue(DA);function Jh(e,t,r){return t===!0?Yl(e,r):Z(t)?Yl(e,t):e}function pr(e){"@babel/helpers - typeof";return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pr(e)}var BA=["ref"];function Zl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zl(Object(r),!0).forEach(function(n){Ma(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ed(n.key),n)}}function LA(e,t,r){return t&&Jl(e.prototype,t),r&&Jl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function FA(e,t,r){return t=_i(t),WA(e,Qh()?Reflect.construct(t,r||[],_i(e).constructor):t.apply(e,r))}function WA(e,t){if(t&&(pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zA(e)}function zA(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qh=function(){return!!e})()}function _i(e){return _i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_i(e)}function UA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xo(e,t)}function Xo(e,t){return Xo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xo(e,t)}function Ma(e,t,r){return t=ed(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ed(e){var t=qA(e,"string");return pr(t)=="symbol"?t:t+""}function qA(e,t){if(pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function HA(e,t){if(e==null)return{};var r=GA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function GA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function KA(e){return e.value}function XA(e,t){if(A.isValidElement(e))return A.cloneElement(e,t);if(typeof e=="function")return A.createElement(e,t);t.ref;var r=HA(t,BA);return A.createElement(pc,r)}var Ql=1,ur=function(e){function t(){var r;RA(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=FA(this,t,[].concat(i)),Ma(r,"lastBoundingBox",{width:-1,height:-1}),r}return UA(t,e),LA(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Ql||Math.abs(i.height-this.lastBoundingBox.height)>Ql)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?lt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,l=i.chartWidth,f=i.chartHeight,s,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();s={left:((l||0)-h.width)/2}}else s=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();p={top:((f||0)-y.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return lt(lt({},s),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,l=i.payloadUniqBy,f=i.payload,s=lt(lt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return A.createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(h){n.wrapperNode=h}},XA(a,lt(lt({},this.props),{},{payload:Jh(f,l,KA)})))}}],[{key:"getWithHeight",value:function(n,i){var a=lt(lt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&R(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(B.PureComponent);Ma(ur,"displayName","Legend");Ma(ur,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var es=Un,VA=dc,YA=Ne,ts=es?es.isConcatSpreadable:void 0;function ZA(e){return YA(e)||VA(e)||!!(ts&&e&&e[ts])}var JA=ZA,QA=Fh,eP=JA;function td(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=eP),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?td(u,t-1,r,n,i):QA(i,u):n||(i[i.length]=u)}return i}var rd=td;function tP(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var rP=tP,nP=rP,iP=nP(),aP=iP,oP=aP,uP=ja;function cP(e,t){return e&&oP(e,t,uP)}var nd=cP,lP=Gn;function sP(e,t){return function(r,n){if(r==null)return r;if(!lP(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var fP=sP,pP=nd,hP=fP,dP=hP(pP),mc=dP,vP=mc,yP=Gn;function gP(e,t){var r=-1,n=yP(e)?Array(e.length):[];return vP(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var id=gP;function mP(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var bP=mP,rs=Mr;function xP(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=rs(e),o=t!==void 0,u=t===null,c=t===t,l=rs(t);if(!u&&!l&&!a&&e>t||a&&o&&c&&!u&&!l||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!l&&e<t||l&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var wP=xP,OP=wP;function SP(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=OP(i[n],a[n]);if(c){if(n>=u)return c;var l=r[n];return c*(l=="desc"?-1:1)}}return e.index-t.index}var _P=SP,uo=rc,AP=nc,PP=Mt,$P=id,TP=bP,EP=Uh,jP=_P,MP=Rr,CP=Ne;function IP(e,t,r){t.length?t=uo(t,function(a){return CP(a)?function(o){return AP(o,a.length===1?a[0]:a)}:a}):t=[MP];var n=-1;t=uo(t,EP(PP));var i=$P(e,function(a,o,u){var c=uo(t,function(l){return l(a)});return{criteria:c,index:++n,value:a}});return TP(i,function(a,o){return jP(a,o,r)})}var NP=IP;function kP(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var DP=kP,BP=DP,ns=Math.max;function RP(e,t,r){return t=ns(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=ns(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),BP(e,this,u)}}var LP=RP;function FP(e){return function(){return e}}var WP=FP,zP=Zt,UP=function(){try{var e=zP(Object,"defineProperty");return e({},"",{}),e}catch{}}(),ad=UP,qP=WP,is=ad,HP=Rr,GP=is?function(e,t){return is(e,"toString",{configurable:!0,enumerable:!1,value:qP(t),writable:!0})}:HP,KP=GP,XP=800,VP=16,YP=Date.now;function ZP(e){var t=0,r=0;return function(){var n=YP(),i=VP-(n-r);if(r=n,i>0){if(++t>=XP)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var JP=ZP,QP=KP,e$=JP,t$=e$(QP),r$=t$,n$=Rr,i$=LP,a$=r$;function o$(e,t){return a$(i$(e,t,n$),e+"")}var u$=o$,c$=Ju,l$=Gn,s$=vc,f$=jt;function p$(e,t,r){if(!f$(r))return!1;var n=typeof t;return(n=="number"?l$(r)&&s$(t,r.length):n=="string"&&t in r)?c$(r[t],e):!1}var Ca=p$,h$=rd,d$=NP,v$=u$,as=Ca,y$=v$(function(e,t){if(e==null)return[];var r=t.length;return r>1&&as(e,t[0],t[1])?t=[]:r>2&&as(t[0],t[1],t[2])&&(t=[t[0]]),d$(e,h$(t,1),[])}),g$=y$;const bc=ue(g$);function sn(e){"@babel/helpers - typeof";return sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sn(e)}function Vo(){return Vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vo.apply(this,arguments)}function m$(e,t){return O$(e)||w$(e,t)||x$(e,t)||b$()}function b$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function x$(e,t){if(e){if(typeof e=="string")return os(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return os(e,t)}}function os(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function w$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function O$(e){if(Array.isArray(e))return e}function us(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function co(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?us(Object(r),!0).forEach(function(n){S$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):us(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function S$(e,t,r){return t=_$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _$(e){var t=A$(e,"string");return sn(t)=="symbol"?t:t+""}function A$(e,t){if(sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function P$(e){return Array.isArray(e)&&be(e[0])&&be(e[1])?e.join(" ~ "):e}var $$=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,l=c===void 0?{}:c,f=t.payload,s=t.formatter,p=t.itemSorter,h=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,w=t.accessibilityLayer,b=w===void 0?!1:w,x=function(){if(f&&f.length){var $={padding:0,margin:0},C=(p?bc(f,p):f).map(function(M,N){if(M.type==="none")return null;var k=co({display:"block",paddingTop:4,paddingBottom:4,color:M.color||"#000"},u),D=M.formatter||s||P$,L=M.value,z=M.name,G=L,q=z;if(D&&G!=null&&q!=null){var U=D(L,z,M,N,f);if(Array.isArray(U)){var K=m$(U,2);G=K[0],q=K[1]}else G=U}return A.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(N),style:k},be(q)?A.createElement("span",{className:"recharts-tooltip-item-name"},q):null,be(q)?A.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,A.createElement("span",{className:"recharts-tooltip-item-value"},G),A.createElement("span",{className:"recharts-tooltip-item-unit"},M.unit||""))});return A.createElement("ul",{className:"recharts-tooltip-item-list",style:$},C)}return null},m=co({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),g=co({margin:0},l),O=!te(v),S=O?v:"",_=re("recharts-default-tooltip",h),T=re("recharts-tooltip-label",y);O&&d&&f!==void 0&&f!==null&&(S=d(v,f));var j=b?{role:"status","aria-live":"assertive"}:{};return A.createElement("div",Vo({className:_,style:m},j),A.createElement("p",{className:T,style:g},A.isValidElement(S)?S:"".concat(S)),x())};function fn(e){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(e)}function ti(e,t,r){return t=T$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T$(e){var t=E$(e,"string");return fn(t)=="symbol"?t:t+""}function E$(e,t){if(fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Gr="recharts-tooltip-wrapper",j$={visibility:"hidden"};function M$(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return re(Gr,ti(ti(ti(ti({},"".concat(Gr,"-right"),R(r)&&t&&R(t.x)&&r>=t.x),"".concat(Gr,"-left"),R(r)&&t&&R(t.x)&&r<t.x),"".concat(Gr,"-bottom"),R(n)&&t&&R(t.y)&&n>=t.y),"".concat(Gr,"-top"),R(n)&&t&&R(t.y)&&n<t.y))}function cs(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,l=e.viewBoxDimension;if(a&&R(a[n]))return a[n];var f=r[n]-u-i,s=r[n]+i;if(t[n])return o[n]?f:s;if(o[n]){var p=f,h=c[n];return p<h?Math.max(s,c[n]):Math.max(f,c[n])}var y=s+u,v=c[n]+l;return y>v?Math.max(f,c[n]):Math.max(s,c[n])}function C$(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function I$(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,l,f,s;return o.height>0&&o.width>0&&r?(f=cs({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),s=cs({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),l=C$({translateX:f,translateY:s,useTranslate3d:u})):l=j$,{cssProperties:l,cssClasses:M$({translateX:f,translateY:s,coordinate:r})}}function hr(e){"@babel/helpers - typeof";return hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hr(e)}function ls(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ss(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ls(Object(r),!0).forEach(function(n){Zo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ls(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function N$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function k$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ud(n.key),n)}}function D$(e,t,r){return t&&k$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function B$(e,t,r){return t=Ai(t),R$(e,od()?Reflect.construct(t,r||[],Ai(e).constructor):t.apply(e,r))}function R$(e,t){if(t&&(hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return L$(e)}function L$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function od(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(od=function(){return!!e})()}function Ai(e){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ai(e)}function F$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yo(e,t)}function Yo(e,t){return Yo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yo(e,t)}function Zo(e,t,r){return t=ud(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ud(e){var t=W$(e,"string");return hr(t)=="symbol"?t:t+""}function W$(e,t){if(hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fs=1,z$=function(e){function t(){var r;N$(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=B$(this,t,[].concat(i)),Zo(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Zo(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,l,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(l=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&l!==void 0?l:0}})}}),r}return F$(t,e),D$(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>fs||Math.abs(n.height-this.state.lastBoundingBox.height)>fs)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.children,f=i.coordinate,s=i.hasPayload,p=i.isAnimationActive,h=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,w=i.viewBox,b=i.wrapperStyle,x=I$({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:w}),m=x.cssClasses,g=x.cssProperties,O=ss(ss({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&s?"visible":"hidden",position:"absolute",top:0,left:0},b);return A.createElement("div",{tabIndex:-1,className:m,style:O,ref:function(_){n.wrapperNode=_}},l)}}])}(B.PureComponent),U$=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},$t={isSsr:U$(),get:function(t){return $t[t]},set:function(t,r){if(typeof t=="string")$t[t]=r;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(i){$t[i]=t[i]})}}};function dr(e){"@babel/helpers - typeof";return dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr(e)}function ps(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ps(Object(r),!0).forEach(function(n){xc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ps(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function q$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function H$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ld(n.key),n)}}function G$(e,t,r){return t&&H$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function K$(e,t,r){return t=Pi(t),X$(e,cd()?Reflect.construct(t,r||[],Pi(e).constructor):t.apply(e,r))}function X$(e,t){if(t&&(dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V$(e)}function V$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cd=function(){return!!e})()}function Pi(e){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pi(e)}function Y$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jo(e,t)}function Jo(e,t){return Jo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Jo(e,t)}function xc(e,t,r){return t=ld(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ld(e){var t=Z$(e,"string");return dr(t)=="symbol"?t:t+""}function Z$(e,t){if(dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function J$(e){return e.dataKey}function Q$(e,t){return A.isValidElement(e)?A.cloneElement(e,t):typeof e=="function"?A.createElement(e,t):A.createElement($$,t)}var st=function(e){function t(){return q$(this,t),K$(this,t,arguments)}return Y$(t,e),G$(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.content,f=i.coordinate,s=i.filterNull,p=i.isAnimationActive,h=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,w=i.reverseDirection,b=i.useTranslate3d,x=i.viewBox,m=i.wrapperStyle,g=y??[];s&&g.length&&(g=Jh(y.filter(function(S){return S.value!=null&&(S.hide!==!0||n.props.includeHidden)}),v,J$));var O=g.length>0;return A.createElement(z$,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:O,offset:h,position:d,reverseDirection:w,useTranslate3d:b,viewBox:x,wrapperStyle:m},Q$(l,hs(hs({},this.props),{},{payload:g})))}}])}(B.PureComponent);xc(st,"displayName","Tooltip");xc(st,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!$t.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var eT=at,tT=function(){return eT.Date.now()},rT=tT,nT=/\s/;function iT(e){for(var t=e.length;t--&&nT.test(e.charAt(t)););return t}var aT=iT,oT=aT,uT=/^\s+/;function cT(e){return e&&e.slice(0,oT(e)+1).replace(uT,"")}var lT=cT,sT=lT,ds=jt,fT=Mr,vs=NaN,pT=/^[-+]0x[0-9a-f]+$/i,hT=/^0b[01]+$/i,dT=/^0o[0-7]+$/i,vT=parseInt;function yT(e){if(typeof e=="number")return e;if(fT(e))return vs;if(ds(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=ds(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=sT(e);var r=hT.test(e);return r||dT.test(e)?vT(e.slice(2),r?2:8):pT.test(e)?vs:+e}var sd=yT,gT=jt,lo=rT,ys=sd,mT="Expected a function",bT=Math.max,xT=Math.min;function wT(e,t,r){var n,i,a,o,u,c,l=0,f=!1,s=!1,p=!0;if(typeof e!="function")throw new TypeError(mT);t=ys(t)||0,gT(r)&&(f=!!r.leading,s="maxWait"in r,a=s?bT(ys(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function h(O){var S=n,_=i;return n=i=void 0,l=O,o=e.apply(_,S),o}function y(O){return l=O,u=setTimeout(w,t),f?h(O):o}function v(O){var S=O-c,_=O-l,T=t-S;return s?xT(T,a-_):T}function d(O){var S=O-c,_=O-l;return c===void 0||S>=t||S<0||s&&_>=a}function w(){var O=lo();if(d(O))return b(O);u=setTimeout(w,v(O))}function b(O){return u=void 0,p&&n?h(O):(n=i=void 0,o)}function x(){u!==void 0&&clearTimeout(u),l=0,n=c=i=u=void 0}function m(){return u===void 0?o:b(lo())}function g(){var O=lo(),S=d(O);if(n=arguments,i=this,c=O,S){if(u===void 0)return y(c);if(s)return clearTimeout(u),u=setTimeout(w,t),h(c)}return u===void 0&&(u=setTimeout(w,t)),o}return g.cancel=x,g.flush=m,g}var OT=wT,ST=OT,_T=jt,AT="Expected a function";function PT(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(AT);return _T(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),ST(e,t,{leading:n,maxWait:t,trailing:i})}var $T=PT;const fd=ue($T);function pn(e){"@babel/helpers - typeof";return pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pn(e)}function gs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ri(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gs(Object(r),!0).forEach(function(n){TT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TT(e,t,r){return t=ET(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ET(e){var t=jT(e,"string");return pn(t)=="symbol"?t:t+""}function jT(e,t){if(pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function MT(e,t){return kT(e)||NT(e,t)||IT(e,t)||CT()}function CT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IT(e,t){if(e){if(typeof e=="string")return ms(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ms(e,t)}}function ms(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function NT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function kT(e){if(Array.isArray(e))return e}var HF=B.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,l=e.minWidth,f=l===void 0?0:l,s=e.minHeight,p=e.maxHeight,h=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,w=e.className,b=e.onResize,x=e.style,m=x===void 0?{}:x,g=B.useRef(null),O=B.useRef();O.current=b,B.useImperativeHandle(t,function(){return Object.defineProperty(g.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),g.current},configurable:!0})});var S=B.useState({containerWidth:i.width,containerHeight:i.height}),_=MT(S,2),T=_[0],j=_[1],P=B.useCallback(function(C,M){j(function(N){var k=Math.round(C),D=Math.round(M);return N.containerWidth===k&&N.containerHeight===D?N:{containerWidth:k,containerHeight:D}})},[]);B.useEffect(function(){var C=function(z){var G,q=z[0].contentRect,U=q.width,K=q.height;P(U,K),(G=O.current)===null||G===void 0||G.call(O,U,K)};v>0&&(C=fd(C,v,{trailing:!0,leading:!1}));var M=new ResizeObserver(C),N=g.current.getBoundingClientRect(),k=N.width,D=N.height;return P(k,D),M.observe(g.current),function(){M.disconnect()}},[P,v]);var $=B.useMemo(function(){var C=T.containerWidth,M=T.containerHeight;if(C<0||M<0)return null;yt(Wt(o)||Wt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),yt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var N=Wt(o)?C:o,k=Wt(c)?M:c;r&&r>0&&(N?k=N/r:k&&(N=k*r),p&&k>p&&(k=p)),yt(N>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,N,k,o,c,f,s,r);var D=!Array.isArray(h)&&vt(h.type).endsWith("Chart");return A.Children.map(h,function(L){return uh.isElement(L)?B.cloneElement(L,ri({width:N,height:k},D?{style:ri({height:"100%",width:"100%",maxHeight:k,maxWidth:N},L.props.style)}:{})):L})},[r,h,c,p,s,f,T,o]);return A.createElement("div",{id:d?"".concat(d):void 0,className:re("recharts-responsive-container",w),style:ri(ri({},m),{},{width:o,height:c,minWidth:f,minHeight:s,maxHeight:p}),ref:g},$)}),pd=function(t){return null};pd.displayName="Cell";function hn(e){"@babel/helpers - typeof";return hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hn(e)}function bs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bs(Object(r),!0).forEach(function(n){DT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DT(e,t,r){return t=BT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function BT(e){var t=RT(e,"string");return hn(t)=="symbol"?t:t+""}function RT(e,t){if(hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var tr={widthCache:{},cacheCount:0},LT=2e3,FT={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},xs="recharts_measurement_span";function WT(e){var t=Qo({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var rn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||$t.isSsr)return{width:0,height:0};var n=WT(r),i=JSON.stringify({text:t,copyStyle:n});if(tr.widthCache[i])return tr.widthCache[i];try{var a=document.getElementById(xs);a||(a=document.createElement("span"),a.setAttribute("id",xs),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Qo(Qo({},FT),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return tr.widthCache[i]=c,++tr.cacheCount>LT&&(tr.cacheCount=0,tr.widthCache={}),c}catch{return{width:0,height:0}}},zT=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}function $i(e,t){return GT(e)||HT(e,t)||qT(e,t)||UT()}function UT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qT(e,t){if(e){if(typeof e=="string")return ws(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ws(e,t)}}function ws(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function HT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function GT(e){if(Array.isArray(e))return e}function KT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Os(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,VT(n.key),n)}}function XT(e,t,r){return t&&Os(e.prototype,t),r&&Os(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function VT(e){var t=YT(e,"string");return dn(t)=="symbol"?t:t+""}function YT(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ss=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,_s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,ZT=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,JT=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,hd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},QT=Object.keys(hd),ir="NaN";function eE(e,t){return e*hd[t]}var ni=function(){function e(t,r){KT(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!ZT.test(r)&&(this.num=NaN,this.unit=""),QT.includes(r)&&(this.num=eE(t,r),this.unit="px")}return XT(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=JT.exec(r))!==null&&n!==void 0?n:[],a=$i(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function dd(e){if(e.includes(ir))return ir;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Ss.exec(t))!==null&&r!==void 0?r:[],i=$i(n,4),a=i[1],o=i[2],u=i[3],c=ni.parse(a??""),l=ni.parse(u??""),f=o==="*"?c.multiply(l):c.divide(l);if(f.isNaN())return ir;t=t.replace(Ss,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,p=(s=_s.exec(t))!==null&&s!==void 0?s:[],h=$i(p,4),y=h[1],v=h[2],d=h[3],w=ni.parse(y??""),b=ni.parse(d??""),x=v==="+"?w.add(b):w.subtract(b);if(x.isNaN())return ir;t=t.replace(_s,x.toString())}return t}var As=/\(([^()]*)\)/;function tE(e){for(var t=e;t.includes("(");){var r=As.exec(t),n=$i(r,2),i=n[1];t=t.replace(As,dd(i))}return t}function rE(e){var t=e.replace(/\s+/g,"");return t=tE(t),t=dd(t),t}function nE(e){try{return rE(e)}catch{return ir}}function so(e){var t=nE(e.slice(5,-1));return t===ir?"":t}var iE=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],aE=["dx","dy","angle","className","breakAll"];function eu(){return eu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},eu.apply(this,arguments)}function Ps(e,t){if(e==null)return{};var r=oE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function oE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function $s(e,t){return sE(e)||lE(e,t)||cE(e,t)||uE()}function uE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cE(e,t){if(e){if(typeof e=="string")return Ts(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ts(e,t)}}function Ts(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function sE(e){if(Array.isArray(e))return e}var vd=/[ \f\n\r\t\v\u2028\u2029]+/,yd=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];te(r)||(n?a=r.toString().split(""):a=r.toString().split(vd));var o=a.map(function(c){return{word:c,width:rn(c,i).width}}),u=n?0:rn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},fE=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,l=t.breakAll,f=R(o),s=u,p=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return N.reduce(function(k,D){var L=D.word,z=D.width,G=k[k.length-1];if(G&&(i==null||a||G.width+z+n<Number(i)))G.words.push(L),G.width+=z+n;else{var q={words:[L],width:z};k.push(q)}return k},[])},h=p(r),y=function(N){return N.reduce(function(k,D){return k.width>D.width?k:D})};if(!f)return h;for(var v="…",d=function(N){var k=s.slice(0,N),D=yd({breakAll:l,style:c,children:k+v}).wordsWithComputedWidth,L=p(D),z=L.length>o||y(L).width>Number(i);return[z,L]},w=0,b=s.length-1,x=0,m;w<=b&&x<=s.length-1;){var g=Math.floor((w+b)/2),O=g-1,S=d(O),_=$s(S,2),T=_[0],j=_[1],P=d(g),$=$s(P,1),C=$[0];if(!T&&!C&&(w=g+1),T&&C&&(b=g-1),!T&&C){m=j;break}x++}return m||h},Es=function(t){var r=te(t)?[]:t.toString().split(vd);return[{words:r}]},pE=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!$t.isSsr){var c,l,f=yd({breakAll:o,children:i,style:a});if(f){var s=f.wordsWithComputedWidth,p=f.spaceWidth;c=s,l=p}else return Es(i);return fE({breakAll:o,children:i,maxLines:u,style:a},c,l,r,n)}return Es(i)},js="#808080",Ti=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,l=c===void 0?"0.71em":c,f=t.scaleToFit,s=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,w=d===void 0?js:d,b=Ps(t,iE),x=B.useMemo(function(){return pE({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:s,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,s,b.style,b.width]),m=b.dx,g=b.dy,O=b.angle,S=b.className,_=b.breakAll,T=Ps(b,aE);if(!be(n)||!be(a))return null;var j=n+(R(m)?m:0),P=a+(R(g)?g:0),$;switch(v){case"start":$=so("calc(".concat(l,")"));break;case"middle":$=so("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(l," / 2))"));break;default:$=so("calc(".concat(x.length-1," * -").concat(u,")"));break}var C=[];if(s){var M=x[0].width,N=b.width;C.push("scale(".concat((R(N)?N/M:1)/M,")"))}return O&&C.push("rotate(".concat(O,", ").concat(j,", ").concat(P,")")),C.length&&(T.transform=C.join(" ")),A.createElement("text",eu({},ee(T,!0),{x:j,y:P,className:re("recharts-text",S),textAnchor:h,fill:w.includes("url")?js:w}),x.map(function(k,D){var L=k.words.join(_?"":" ");return A.createElement("tspan",{x:j,dy:D===0?$:u,key:"".concat(L,"-").concat(D)},L)}))};function Tt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function hE(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function wc(e){let t,r,n;e.length!==2?(t=Tt,r=(u,c)=>Tt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Tt||e===hE?e:dE,r=e,n=e);function i(u,c,l=0,f=u.length){if(l<f){if(t(c,c)!==0)return f;do{const s=l+f>>>1;r(u[s],c)<0?l=s+1:f=s}while(l<f)}return l}function a(u,c,l=0,f=u.length){if(l<f){if(t(c,c)!==0)return f;do{const s=l+f>>>1;r(u[s],c)<=0?l=s+1:f=s}while(l<f)}return l}function o(u,c,l=0,f=u.length){const s=i(u,c,l,f-1);return s>l&&n(u[s-1],c)>-n(u[s],c)?s-1:s}return{left:i,center:o,right:a}}function dE(){return 0}function gd(e){return e===null?NaN:+e}function*vE(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const yE=wc(Tt),Kn=yE.right;wc(gd).center;class Ms extends Map{constructor(t,r=bE){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Cs(this,t))}has(t){return super.has(Cs(this,t))}set(t,r){return super.set(gE(this,t),r)}delete(t){return super.delete(mE(this,t))}}function Cs({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function gE({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function mE({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function bE(e){return e!==null&&typeof e=="object"?e.valueOf():e}function xE(e=Tt){if(e===Tt)return md;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function md(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const wE=Math.sqrt(50),OE=Math.sqrt(10),SE=Math.sqrt(2);function Ei(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=wE?10:a>=OE?5:a>=SE?2:1;let u,c,l;return i<0?(l=Math.pow(10,-i)/o,u=Math.round(e*l),c=Math.round(t*l),u/l<e&&++u,c/l>t&&--c,l=-l):(l=Math.pow(10,i)*o,u=Math.round(e/l),c=Math.round(t/l),u*l<e&&++u,c*l>t&&--c),c<u&&.5<=r&&r<2?Ei(e,t,r*2):[u,c,l]}function tu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Ei(t,e,r):Ei(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let l=0;l<u;++l)c[l]=(a-l)/-o;else for(let l=0;l<u;++l)c[l]=(a-l)*o;else if(o<0)for(let l=0;l<u;++l)c[l]=(i+l)/-o;else for(let l=0;l<u;++l)c[l]=(i+l)*o;return c}function ru(e,t,r){return t=+t,e=+e,r=+r,Ei(e,t,r)[2]}function nu(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?ru(t,e,r):ru(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Is(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Ns(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function bd(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?md:xE(i);n>r;){if(n-r>600){const c=n-r+1,l=t-r+1,f=Math.log(c),s=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*s*(c-s)/c)*(l-c/2<0?-1:1),h=Math.max(r,Math.floor(t-l*s/c+p)),y=Math.min(n,Math.floor(t+(c-l)*s/c+p));bd(e,t,h,y,i)}const a=e[t];let o=r,u=n;for(Kr(e,r,t),i(e[n],a)>0&&Kr(e,r,n);o<u;){for(Kr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Kr(e,r,u):(++u,Kr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Kr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function _E(e,t,r){if(e=Float64Array.from(vE(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Ns(e);if(t>=1)return Is(e);var n,i=(n-1)*t,a=Math.floor(i),o=Is(bd(e,a).subarray(0,a+1)),u=Ns(e.subarray(a+1));return o+(u-o)*(i-a)}}function AE(e,t,r=gd){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function PE(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Ge(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function St(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const iu=Symbol("implicit");function Oc(){var e=new Ms,t=[],r=[],n=iu;function i(a){let o=e.get(a);if(o===void 0){if(n!==iu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Ms;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Oc(t,r).unknown(n)},Ge.apply(i,arguments),i}function vn(){var e=Oc().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,l=0,f=.5;delete e.unknown;function s(){var p=t().length,h=i<n,y=h?i:n,v=h?n:i;a=(v-y)/Math.max(1,p-c+l*2),u&&(a=Math.floor(a)),y+=(v-y-a*(p-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var d=PE(p).map(function(w){return y+a*w});return r(h?d.reverse():d)}return e.domain=function(p){return arguments.length?(t(p),s()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,s()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,s()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,s()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,l=+p),s()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),s()):c},e.paddingOuter=function(p){return arguments.length?(l=+p,s()):l},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),s()):f},e.copy=function(){return vn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(l).align(f)},Ge.apply(s(),arguments)}function xd(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return xd(t())},e}function nn(){return xd(vn.apply(null,arguments).paddingInner(1))}function Sc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function wd(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Xn(){}var yn=.7,ji=1/yn,cr="\\s*([+-]?\\d+)\\s*",gn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",$E=/^#([0-9a-f]{3,8})$/,TE=new RegExp(`^rgb\\(${cr},${cr},${cr}\\)$`),EE=new RegExp(`^rgb\\(${tt},${tt},${tt}\\)$`),jE=new RegExp(`^rgba\\(${cr},${cr},${cr},${gn}\\)$`),ME=new RegExp(`^rgba\\(${tt},${tt},${tt},${gn}\\)$`),CE=new RegExp(`^hsl\\(${gn},${tt},${tt}\\)$`),IE=new RegExp(`^hsla\\(${gn},${tt},${tt},${gn}\\)$`),ks={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Sc(Xn,mn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ds,formatHex:Ds,formatHex8:NE,formatHsl:kE,formatRgb:Bs,toString:Bs});function Ds(){return this.rgb().formatHex()}function NE(){return this.rgb().formatHex8()}function kE(){return Od(this).formatHsl()}function Bs(){return this.rgb().formatRgb()}function mn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=$E.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Rs(t):r===3?new Ie(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?ii(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?ii(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=TE.exec(e))?new Ie(t[1],t[2],t[3],1):(t=EE.exec(e))?new Ie(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=jE.exec(e))?ii(t[1],t[2],t[3],t[4]):(t=ME.exec(e))?ii(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=CE.exec(e))?Ws(t[1],t[2]/100,t[3]/100,1):(t=IE.exec(e))?Ws(t[1],t[2]/100,t[3]/100,t[4]):ks.hasOwnProperty(e)?Rs(ks[e]):e==="transparent"?new Ie(NaN,NaN,NaN,0):null}function Rs(e){return new Ie(e>>16&255,e>>8&255,e&255,1)}function ii(e,t,r,n){return n<=0&&(e=t=r=NaN),new Ie(e,t,r,n)}function DE(e){return e instanceof Xn||(e=mn(e)),e?(e=e.rgb(),new Ie(e.r,e.g,e.b,e.opacity)):new Ie}function au(e,t,r,n){return arguments.length===1?DE(e):new Ie(e,t,r,n??1)}function Ie(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Sc(Ie,au,wd(Xn,{brighter(e){return e=e==null?ji:Math.pow(ji,e),new Ie(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?yn:Math.pow(yn,e),new Ie(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ie(Ht(this.r),Ht(this.g),Ht(this.b),Mi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ls,formatHex:Ls,formatHex8:BE,formatRgb:Fs,toString:Fs}));function Ls(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}`}function BE(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}${zt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Fs(){const e=Mi(this.opacity);return`${e===1?"rgb(":"rgba("}${Ht(this.r)}, ${Ht(this.g)}, ${Ht(this.b)}${e===1?")":`, ${e})`}`}function Mi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ht(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function zt(e){return e=Ht(e),(e<16?"0":"")+e.toString(16)}function Ws(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Ve(e,t,r,n)}function Od(e){if(e instanceof Ve)return new Ve(e.h,e.s,e.l,e.opacity);if(e instanceof Xn||(e=mn(e)),!e)return new Ve;if(e instanceof Ve)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Ve(o,u,c,e.opacity)}function RE(e,t,r,n){return arguments.length===1?Od(e):new Ve(e,t,r,n??1)}function Ve(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Sc(Ve,RE,wd(Xn,{brighter(e){return e=e==null?ji:Math.pow(ji,e),new Ve(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?yn:Math.pow(yn,e),new Ve(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Ie(fo(e>=240?e-240:e+120,i,n),fo(e,i,n),fo(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Ve(zs(this.h),ai(this.s),ai(this.l),Mi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Mi(this.opacity);return`${e===1?"hsl(":"hsla("}${zs(this.h)}, ${ai(this.s)*100}%, ${ai(this.l)*100}%${e===1?")":`, ${e})`}`}}));function zs(e){return e=(e||0)%360,e<0?e+360:e}function ai(e){return Math.max(0,Math.min(1,e||0))}function fo(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const _c=e=>()=>e;function LE(e,t){return function(r){return e+r*t}}function FE(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function WE(e){return(e=+e)==1?Sd:function(t,r){return r-t?FE(t,r,e):_c(isNaN(t)?r:t)}}function Sd(e,t){var r=t-e;return r?LE(e,r):_c(isNaN(e)?t:e)}const Us=function e(t){var r=WE(t);function n(i,a){var o=r((i=au(i)).r,(a=au(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),l=Sd(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=l(f),i+""}}return n.gamma=e,n}(1);function zE(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function UE(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function qE(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Lr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function HE(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Ci(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function GE(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Lr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var ou=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,po=new RegExp(ou.source,"g");function KE(e){return function(){return e}}function XE(e){return function(t){return e(t)+""}}function VE(e,t){var r=ou.lastIndex=po.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=ou.exec(e))&&(i=po.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Ci(n,i)})),r=po.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?XE(c[0].x):KE(t):(t=c.length,function(l){for(var f=0,s;f<t;++f)u[(s=c[f]).i]=s.x(l);return u.join("")})}function Lr(e,t){var r=typeof t,n;return t==null||r==="boolean"?_c(t):(r==="number"?Ci:r==="string"?(n=mn(t))?(t=n,Us):VE:t instanceof mn?Us:t instanceof Date?HE:UE(t)?zE:Array.isArray(t)?qE:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?GE:Ci)(e,t)}function Ac(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function YE(e,t){t===void 0&&(t=e,e=Lr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function ZE(e){return function(){return e}}function Ii(e){return+e}var qs=[0,1];function Ce(e){return e}function uu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:ZE(isNaN(t)?NaN:.5)}function JE(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function QE(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=uu(i,n),a=r(o,a)):(n=uu(n,i),a=r(a,o)),function(u){return a(n(u))}}function ej(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=uu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Kn(e,u,1,n)-1;return a[c](i[c](u))}}function Vn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ia(){var e=qs,t=qs,r=Lr,n,i,a,o=Ce,u,c,l;function f(){var p=Math.min(e.length,t.length);return o!==Ce&&(o=JE(e[0],e[p-1])),u=p>2?ej:QE,c=l=null,s}function s(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return s.invert=function(p){return o(i((l||(l=u(t,e.map(n),Ci)))(p)))},s.domain=function(p){return arguments.length?(e=Array.from(p,Ii),f()):e.slice()},s.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},s.rangeRound=function(p){return t=Array.from(p),r=Ac,f()},s.clamp=function(p){return arguments.length?(o=p?!0:Ce,f()):o!==Ce},s.interpolate=function(p){return arguments.length?(r=p,f()):r},s.unknown=function(p){return arguments.length?(a=p,s):a},function(p,h){return n=p,i=h,f()}}function Pc(){return Ia()(Ce,Ce)}function tj(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Ni(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function vr(e){return e=Ni(Math.abs(e)),e?e[1]:NaN}function rj(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function nj(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var ij=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function bn(e){if(!(t=ij.exec(e)))throw new Error("invalid format: "+e);var t;return new $c({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}bn.prototype=$c.prototype;function $c(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}$c.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function aj(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var _d;function oj(e,t){var r=Ni(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(_d=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Ni(e,Math.max(0,t+a-1))[0]}function Hs(e,t){var r=Ni(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Gs={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:tj,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Hs(e*100,t),r:Hs,s:oj,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Ks(e){return e}var Xs=Array.prototype.map,Vs=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function uj(e){var t=e.grouping===void 0||e.thousands===void 0?Ks:rj(Xs.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Ks:nj(Xs.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function l(s){s=bn(s);var p=s.fill,h=s.align,y=s.sign,v=s.symbol,d=s.zero,w=s.width,b=s.comma,x=s.precision,m=s.trim,g=s.type;g==="n"?(b=!0,g="g"):Gs[g]||(x===void 0&&(x=12),m=!0,g="g"),(d||p==="0"&&h==="=")&&(d=!0,p="0",h="=");var O=v==="$"?r:v==="#"&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",S=v==="$"?n:/[%p]/.test(g)?o:"",_=Gs[g],T=/[defgprs%]/.test(g);x=x===void 0?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function j(P){var $=O,C=S,M,N,k;if(g==="c")C=_(P)+C,P="";else{P=+P;var D=P<0||1/P<0;if(P=isNaN(P)?c:_(Math.abs(P),x),m&&(P=aj(P)),D&&+P==0&&y!=="+"&&(D=!1),$=(D?y==="("?y:u:y==="-"||y==="("?"":y)+$,C=(g==="s"?Vs[8+_d/3]:"")+C+(D&&y==="("?")":""),T){for(M=-1,N=P.length;++M<N;)if(k=P.charCodeAt(M),48>k||k>57){C=(k===46?i+P.slice(M+1):P.slice(M))+C,P=P.slice(0,M);break}}}b&&!d&&(P=t(P,1/0));var L=$.length+P.length+C.length,z=L<w?new Array(w-L+1).join(p):"";switch(b&&d&&(P=t(z+P,z.length?w-C.length:1/0),z=""),h){case"<":P=$+P+C+z;break;case"=":P=$+z+P+C;break;case"^":P=z.slice(0,L=z.length>>1)+$+P+C+z.slice(L);break;default:P=z+$+P+C;break}return a(P)}return j.toString=function(){return s+""},j}function f(s,p){var h=l((s=bn(s),s.type="f",s)),y=Math.max(-8,Math.min(8,Math.floor(vr(p)/3)))*3,v=Math.pow(10,-y),d=Vs[8+y/3];return function(w){return h(v*w)+d}}return{format:l,formatPrefix:f}}var oi,Tc,Ad;cj({thousands:",",grouping:[3],currency:["$",""]});function cj(e){return oi=uj(e),Tc=oi.format,Ad=oi.formatPrefix,oi}function lj(e){return Math.max(0,-vr(Math.abs(e)))}function sj(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(vr(t)/3)))*3-vr(Math.abs(e)))}function fj(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,vr(t)-vr(e))+1}function Pd(e,t,r,n){var i=nu(e,t,r),a;switch(n=bn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=sj(i,o))&&(n.precision=a),Ad(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=fj(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=lj(i))&&(n.precision=a-(n.type==="%")*2);break}}return Tc(n)}function Ct(e){var t=e.domain;return e.ticks=function(r){var n=t();return tu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Pd(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,l,f=10;for(u<o&&(l=o,o=u,u=l,l=i,i=a,a=l);f-- >0;){if(l=ru(o,u,r),l===c)return n[i]=o,n[a]=u,t(n);if(l>0)o=Math.floor(o/l)*l,u=Math.ceil(u/l)*l;else if(l<0)o=Math.ceil(o*l)/l,u=Math.floor(u*l)/l;else break;c=l}return e},e}function ki(){var e=Pc();return e.copy=function(){return Vn(e,ki())},Ge.apply(e,arguments),Ct(e)}function $d(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ii),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return $d(e).unknown(t)},e=arguments.length?Array.from(e,Ii):[0,1],Ct(r)}function Td(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Ys(e){return Math.log(e)}function Zs(e){return Math.exp(e)}function pj(e){return-Math.log(-e)}function hj(e){return-Math.exp(-e)}function dj(e){return isFinite(e)?+("1e"+e):e<0?0:e}function vj(e){return e===10?dj:e===Math.E?Math.exp:t=>Math.pow(e,t)}function yj(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Js(e){return(t,r)=>-e(-t,r)}function Ec(e){const t=e(Ys,Zs),r=t.domain;let n=10,i,a;function o(){return i=yj(n),a=vj(n),r()[0]<0?(i=Js(i),a=Js(a),e(pj,hj)):e(Ys,Zs),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let l=c[0],f=c[c.length-1];const s=f<l;s&&([l,f]=[f,l]);let p=i(l),h=i(f),y,v;const d=u==null?10:+u;let w=[];if(!(n%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),l>0){for(;p<=h;++p)for(y=1;y<n;++y)if(v=p<0?y/a(-p):y*a(p),!(v<l)){if(v>f)break;w.push(v)}}else for(;p<=h;++p)for(y=n-1;y>=1;--y)if(v=p>0?y/a(-p):y*a(p),!(v<l)){if(v>f)break;w.push(v)}w.length*2<d&&(w=tu(l,f,d))}else w=tu(p,h,Math.min(h-p,d)).map(a);return s?w.reverse():w},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=bn(c)).precision==null&&(c.trim=!0),c=Tc(c)),u===1/0)return c;const l=Math.max(1,n*u/t.ticks().length);return f=>{let s=f/a(Math.round(i(f)));return s*n<n-.5&&(s*=n),s<=l?c(f):""}},t.nice=()=>r(Td(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function Ed(){const e=Ec(Ia()).domain([1,10]);return e.copy=()=>Vn(e,Ed()).base(e.base()),Ge.apply(e,arguments),e}function Qs(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ef(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function jc(e){var t=1,r=e(Qs(t),ef(t));return r.constant=function(n){return arguments.length?e(Qs(t=+n),ef(t)):t},Ct(r)}function jd(){var e=jc(Ia());return e.copy=function(){return Vn(e,jd()).constant(e.constant())},Ge.apply(e,arguments)}function tf(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function gj(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function mj(e){return e<0?-e*e:e*e}function Mc(e){var t=e(Ce,Ce),r=1;function n(){return r===1?e(Ce,Ce):r===.5?e(gj,mj):e(tf(r),tf(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Ct(t)}function Cc(){var e=Mc(Ia());return e.copy=function(){return Vn(e,Cc()).exponent(e.exponent())},Ge.apply(e,arguments),e}function bj(){return Cc.apply(null,arguments).exponent(.5)}function rf(e){return Math.sign(e)*e*e}function xj(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Md(){var e=Pc(),t=[0,1],r=!1,n;function i(a){var o=xj(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(rf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ii)).map(rf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Md(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Ge.apply(i,arguments),Ct(i)}function Cd(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=AE(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Kn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Tt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Cd().domain(e).range(t).unknown(n)},Ge.apply(a,arguments)}function Id(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Kn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var l=i.indexOf(c);return l<0?[NaN,NaN]:l<1?[e,n[0]]:l>=r?[n[r-1],t]:[n[l-1],n[l]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Id().domain([e,t]).range(i).unknown(a)},Ge.apply(Ct(o),arguments)}function Nd(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Kn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Nd().domain(e).range(t).unknown(r)},Ge.apply(i,arguments)}const ho=new Date,vo=new Date;function xe(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let l;do c.push(l=new Date(+a)),t(a,u),e(a);while(l<a&&a<o);return c},i.filter=a=>xe(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(ho.setTime(+a),vo.setTime(+o),e(ho),e(vo),Math.floor(r(ho,vo))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Di=xe(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Di.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?xe(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Di);Di.range;const pt=1e3,ze=pt*60,ht=ze*60,mt=ht*24,Ic=mt*7,nf=mt*30,yo=mt*365,Ut=xe(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*pt)},(e,t)=>(t-e)/pt,e=>e.getUTCSeconds());Ut.range;const Nc=xe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*pt)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getMinutes());Nc.range;const kc=xe(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getUTCMinutes());kc.range;const Dc=xe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*pt-e.getMinutes()*ze)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getHours());Dc.range;const Bc=xe(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getUTCHours());Bc.range;const Yn=xe(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*ze)/mt,e=>e.getDate()-1);Yn.range;const Na=xe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>e.getUTCDate()-1);Na.range;const kd=xe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>Math.floor(e/mt));kd.range;function Jt(e){return xe(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*ze)/Ic)}const ka=Jt(0),Bi=Jt(1),wj=Jt(2),Oj=Jt(3),yr=Jt(4),Sj=Jt(5),_j=Jt(6);ka.range;Bi.range;wj.range;Oj.range;yr.range;Sj.range;_j.range;function Qt(e){return xe(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Ic)}const Da=Qt(0),Ri=Qt(1),Aj=Qt(2),Pj=Qt(3),gr=Qt(4),$j=Qt(5),Tj=Qt(6);Da.range;Ri.range;Aj.range;Pj.range;gr.range;$j.range;Tj.range;const Rc=xe(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Rc.range;const Lc=xe(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Lc.range;const bt=xe(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());bt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:xe(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});bt.range;const xt=xe(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());xt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:xe(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});xt.range;function Dd(e,t,r,n,i,a){const o=[[Ut,1,pt],[Ut,5,5*pt],[Ut,15,15*pt],[Ut,30,30*pt],[a,1,ze],[a,5,5*ze],[a,15,15*ze],[a,30,30*ze],[i,1,ht],[i,3,3*ht],[i,6,6*ht],[i,12,12*ht],[n,1,mt],[n,2,2*mt],[r,1,Ic],[t,1,nf],[t,3,3*nf],[e,1,yo]];function u(l,f,s){const p=f<l;p&&([l,f]=[f,l]);const h=s&&typeof s.range=="function"?s:c(l,f,s),y=h?h.range(l,+f+1):[];return p?y.reverse():y}function c(l,f,s){const p=Math.abs(f-l)/s,h=wc(([,,d])=>d).right(o,p);if(h===o.length)return e.every(nu(l/yo,f/yo,s));if(h===0)return Di.every(Math.max(nu(l,f,s),1));const[y,v]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return y.every(v)}return[u,c]}const[Ej,jj]=Dd(xt,Lc,Da,kd,Bc,kc),[Mj,Cj]=Dd(bt,Rc,ka,Yn,Dc,Nc);function go(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function mo(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Xr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function Ij(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,l=Vr(i),f=Yr(i),s=Vr(a),p=Yr(a),h=Vr(o),y=Yr(o),v=Vr(u),d=Yr(u),w=Vr(c),b=Yr(c),x={a:D,A:L,b:z,B:G,c:null,d:sf,e:sf,f:rM,g:pM,G:dM,H:Qj,I:eM,j:tM,L:Bd,m:nM,M:iM,p:q,q:U,Q:hf,s:df,S:aM,u:oM,U:uM,V:cM,w:lM,W:sM,x:null,X:null,y:fM,Y:hM,Z:vM,"%":pf},m={a:K,A:ce,b:_e,B:ot,c:null,d:ff,e:ff,f:bM,g:EM,G:MM,H:yM,I:gM,j:mM,L:Ld,m:xM,M:wM,p:$e,q:ut,Q:hf,s:df,S:OM,u:SM,U:_M,V:AM,w:PM,W:$M,x:null,X:null,y:TM,Y:jM,Z:CM,"%":pf},g={a:j,A:P,b:$,B:C,c:M,d:cf,e:cf,f:Vj,g:uf,G:of,H:lf,I:lf,j:Hj,L:Xj,m:qj,M:Gj,p:T,q:Uj,Q:Zj,s:Jj,S:Kj,u:Rj,U:Lj,V:Fj,w:Bj,W:Wj,x:N,X:k,y:uf,Y:of,Z:zj,"%":Yj};x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),m.x=O(r,m),m.X=O(n,m),m.c=O(t,m);function O(F,X){return function(Y){var I=[],he=-1,J=0,ge=F.length,de,ke,ct;for(Y instanceof Date||(Y=new Date(+Y));++he<ge;)F.charCodeAt(he)===37&&(I.push(F.slice(J,he)),(ke=af[de=F.charAt(++he)])!=null?de=F.charAt(++he):ke=de==="e"?" ":"0",(ct=X[de])&&(de=ct(Y,ke)),I.push(de),J=he+1);return I.push(F.slice(J,he)),I.join("")}}function S(F,X){return function(Y){var I=Xr(1900,void 0,1),he=_(I,F,Y+="",0),J,ge;if(he!=Y.length)return null;if("Q"in I)return new Date(I.Q);if("s"in I)return new Date(I.s*1e3+("L"in I?I.L:0));if(X&&!("Z"in I)&&(I.Z=0),"p"in I&&(I.H=I.H%12+I.p*12),I.m===void 0&&(I.m="q"in I?I.q:0),"V"in I){if(I.V<1||I.V>53)return null;"w"in I||(I.w=1),"Z"in I?(J=mo(Xr(I.y,0,1)),ge=J.getUTCDay(),J=ge>4||ge===0?Ri.ceil(J):Ri(J),J=Na.offset(J,(I.V-1)*7),I.y=J.getUTCFullYear(),I.m=J.getUTCMonth(),I.d=J.getUTCDate()+(I.w+6)%7):(J=go(Xr(I.y,0,1)),ge=J.getDay(),J=ge>4||ge===0?Bi.ceil(J):Bi(J),J=Yn.offset(J,(I.V-1)*7),I.y=J.getFullYear(),I.m=J.getMonth(),I.d=J.getDate()+(I.w+6)%7)}else("W"in I||"U"in I)&&("w"in I||(I.w="u"in I?I.u%7:"W"in I?1:0),ge="Z"in I?mo(Xr(I.y,0,1)).getUTCDay():go(Xr(I.y,0,1)).getDay(),I.m=0,I.d="W"in I?(I.w+6)%7+I.W*7-(ge+5)%7:I.w+I.U*7-(ge+6)%7);return"Z"in I?(I.H+=I.Z/100|0,I.M+=I.Z%100,mo(I)):go(I)}}function _(F,X,Y,I){for(var he=0,J=X.length,ge=Y.length,de,ke;he<J;){if(I>=ge)return-1;if(de=X.charCodeAt(he++),de===37){if(de=X.charAt(he++),ke=g[de in af?X.charAt(he++):de],!ke||(I=ke(F,Y,I))<0)return-1}else if(de!=Y.charCodeAt(I++))return-1}return I}function T(F,X,Y){var I=l.exec(X.slice(Y));return I?(F.p=f.get(I[0].toLowerCase()),Y+I[0].length):-1}function j(F,X,Y){var I=h.exec(X.slice(Y));return I?(F.w=y.get(I[0].toLowerCase()),Y+I[0].length):-1}function P(F,X,Y){var I=s.exec(X.slice(Y));return I?(F.w=p.get(I[0].toLowerCase()),Y+I[0].length):-1}function $(F,X,Y){var I=w.exec(X.slice(Y));return I?(F.m=b.get(I[0].toLowerCase()),Y+I[0].length):-1}function C(F,X,Y){var I=v.exec(X.slice(Y));return I?(F.m=d.get(I[0].toLowerCase()),Y+I[0].length):-1}function M(F,X,Y){return _(F,t,X,Y)}function N(F,X,Y){return _(F,r,X,Y)}function k(F,X,Y){return _(F,n,X,Y)}function D(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function z(F){return c[F.getMonth()]}function G(F){return u[F.getMonth()]}function q(F){return i[+(F.getHours()>=12)]}function U(F){return 1+~~(F.getMonth()/3)}function K(F){return o[F.getUTCDay()]}function ce(F){return a[F.getUTCDay()]}function _e(F){return c[F.getUTCMonth()]}function ot(F){return u[F.getUTCMonth()]}function $e(F){return i[+(F.getUTCHours()>=12)]}function ut(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var X=O(F+="",x);return X.toString=function(){return F},X},parse:function(F){var X=S(F+="",!1);return X.toString=function(){return F},X},utcFormat:function(F){var X=O(F+="",m);return X.toString=function(){return F},X},utcParse:function(F){var X=S(F+="",!0);return X.toString=function(){return F},X}}}var af={"-":"",_:" ",0:"0"},Se=/^\s*\d+/,Nj=/^%/,kj=/[\\^$*+?|[\]().{}]/g;function Q(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Dj(e){return e.replace(kj,"\\$&")}function Vr(e){return new RegExp("^(?:"+e.map(Dj).join("|")+")","i")}function Yr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function Bj(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Rj(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function Lj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Fj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function Wj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function of(e,t,r){var n=Se.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function uf(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function zj(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Uj(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function qj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function cf(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function Hj(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function lf(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function Gj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function Kj(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function Xj(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function Vj(e,t,r){var n=Se.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Yj(e,t,r){var n=Nj.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function Zj(e,t,r){var n=Se.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function Jj(e,t,r){var n=Se.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function sf(e,t){return Q(e.getDate(),t,2)}function Qj(e,t){return Q(e.getHours(),t,2)}function eM(e,t){return Q(e.getHours()%12||12,t,2)}function tM(e,t){return Q(1+Yn.count(bt(e),e),t,3)}function Bd(e,t){return Q(e.getMilliseconds(),t,3)}function rM(e,t){return Bd(e,t)+"000"}function nM(e,t){return Q(e.getMonth()+1,t,2)}function iM(e,t){return Q(e.getMinutes(),t,2)}function aM(e,t){return Q(e.getSeconds(),t,2)}function oM(e){var t=e.getDay();return t===0?7:t}function uM(e,t){return Q(ka.count(bt(e)-1,e),t,2)}function Rd(e){var t=e.getDay();return t>=4||t===0?yr(e):yr.ceil(e)}function cM(e,t){return e=Rd(e),Q(yr.count(bt(e),e)+(bt(e).getDay()===4),t,2)}function lM(e){return e.getDay()}function sM(e,t){return Q(Bi.count(bt(e)-1,e),t,2)}function fM(e,t){return Q(e.getFullYear()%100,t,2)}function pM(e,t){return e=Rd(e),Q(e.getFullYear()%100,t,2)}function hM(e,t){return Q(e.getFullYear()%1e4,t,4)}function dM(e,t){var r=e.getDay();return e=r>=4||r===0?yr(e):yr.ceil(e),Q(e.getFullYear()%1e4,t,4)}function vM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Q(t/60|0,"0",2)+Q(t%60,"0",2)}function ff(e,t){return Q(e.getUTCDate(),t,2)}function yM(e,t){return Q(e.getUTCHours(),t,2)}function gM(e,t){return Q(e.getUTCHours()%12||12,t,2)}function mM(e,t){return Q(1+Na.count(xt(e),e),t,3)}function Ld(e,t){return Q(e.getUTCMilliseconds(),t,3)}function bM(e,t){return Ld(e,t)+"000"}function xM(e,t){return Q(e.getUTCMonth()+1,t,2)}function wM(e,t){return Q(e.getUTCMinutes(),t,2)}function OM(e,t){return Q(e.getUTCSeconds(),t,2)}function SM(e){var t=e.getUTCDay();return t===0?7:t}function _M(e,t){return Q(Da.count(xt(e)-1,e),t,2)}function Fd(e){var t=e.getUTCDay();return t>=4||t===0?gr(e):gr.ceil(e)}function AM(e,t){return e=Fd(e),Q(gr.count(xt(e),e)+(xt(e).getUTCDay()===4),t,2)}function PM(e){return e.getUTCDay()}function $M(e,t){return Q(Ri.count(xt(e)-1,e),t,2)}function TM(e,t){return Q(e.getUTCFullYear()%100,t,2)}function EM(e,t){return e=Fd(e),Q(e.getUTCFullYear()%100,t,2)}function jM(e,t){return Q(e.getUTCFullYear()%1e4,t,4)}function MM(e,t){var r=e.getUTCDay();return e=r>=4||r===0?gr(e):gr.ceil(e),Q(e.getUTCFullYear()%1e4,t,4)}function CM(){return"+0000"}function pf(){return"%"}function hf(e){return+e}function df(e){return Math.floor(+e/1e3)}var rr,Wd,zd;IM({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function IM(e){return rr=Ij(e),Wd=rr.format,rr.parse,zd=rr.utcFormat,rr.utcParse,rr}function NM(e){return new Date(e)}function kM(e){return e instanceof Date?+e:+new Date(+e)}function Fc(e,t,r,n,i,a,o,u,c,l){var f=Pc(),s=f.invert,p=f.domain,h=l(".%L"),y=l(":%S"),v=l("%I:%M"),d=l("%I %p"),w=l("%a %d"),b=l("%b %d"),x=l("%B"),m=l("%Y");function g(O){return(c(O)<O?h:u(O)<O?y:o(O)<O?v:a(O)<O?d:n(O)<O?i(O)<O?w:b:r(O)<O?x:m)(O)}return f.invert=function(O){return new Date(s(O))},f.domain=function(O){return arguments.length?p(Array.from(O,kM)):p().map(NM)},f.ticks=function(O){var S=p();return e(S[0],S[S.length-1],O??10)},f.tickFormat=function(O,S){return S==null?g:l(S)},f.nice=function(O){var S=p();return(!O||typeof O.range!="function")&&(O=t(S[0],S[S.length-1],O??10)),O?p(Td(S,O)):f},f.copy=function(){return Vn(f,Fc(e,t,r,n,i,a,o,u,c,l))},f}function DM(){return Ge.apply(Fc(Mj,Cj,bt,Rc,ka,Yn,Dc,Nc,Ut,Wd).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function BM(){return Ge.apply(Fc(Ej,jj,xt,Lc,Da,Na,Bc,kc,Ut,zd).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ba(){var e=0,t=1,r,n,i,a,o=Ce,u=!1,c;function l(s){return s==null||isNaN(s=+s)?c:o(i===0?.5:(s=(a(s)-r)*i,u?Math.max(0,Math.min(1,s)):s))}l.domain=function(s){return arguments.length?([e,t]=s,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),l):[e,t]},l.clamp=function(s){return arguments.length?(u=!!s,l):u},l.interpolator=function(s){return arguments.length?(o=s,l):o};function f(s){return function(p){var h,y;return arguments.length?([h,y]=p,o=s(h,y),l):[o(0),o(1)]}}return l.range=f(Lr),l.rangeRound=f(Ac),l.unknown=function(s){return arguments.length?(c=s,l):c},function(s){return a=s,r=s(e),n=s(t),i=r===n?0:1/(n-r),l}}function It(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Ud(){var e=Ct(Ba()(Ce));return e.copy=function(){return It(e,Ud())},St.apply(e,arguments)}function qd(){var e=Ec(Ba()).domain([1,10]);return e.copy=function(){return It(e,qd()).base(e.base())},St.apply(e,arguments)}function Hd(){var e=jc(Ba());return e.copy=function(){return It(e,Hd()).constant(e.constant())},St.apply(e,arguments)}function Wc(){var e=Mc(Ba());return e.copy=function(){return It(e,Wc()).exponent(e.exponent())},St.apply(e,arguments)}function RM(){return Wc.apply(null,arguments).exponent(.5)}function Gd(){var e=[],t=Ce;function r(n){if(n!=null&&!isNaN(n=+n))return t((Kn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Tt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>_E(e,a/n))},r.copy=function(){return Gd(t).domain(e)},St.apply(r,arguments)}function Ra(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,l=Ce,f,s=!1,p;function h(v){return isNaN(v=+v)?p:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),l(s?Math.max(0,Math.min(1,v)):v))}h.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(v){return arguments.length?(s=!!v,h):s},h.interpolator=function(v){return arguments.length?(l=v,h):l};function y(v){return function(d){var w,b,x;return arguments.length?([w,b,x]=d,l=YE(v,[w,b,x]),h):[l(0),l(.5),l(1)]}}return h.range=y(Lr),h.rangeRound=y(Ac),h.unknown=function(v){return arguments.length?(p=v,h):p},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function Kd(){var e=Ct(Ra()(Ce));return e.copy=function(){return It(e,Kd())},St.apply(e,arguments)}function Xd(){var e=Ec(Ra()).domain([.1,1,10]);return e.copy=function(){return It(e,Xd()).base(e.base())},St.apply(e,arguments)}function Vd(){var e=jc(Ra());return e.copy=function(){return It(e,Vd()).constant(e.constant())},St.apply(e,arguments)}function zc(){var e=Mc(Ra());return e.copy=function(){return It(e,zc()).exponent(e.exponent())},St.apply(e,arguments)}function LM(){return zc.apply(null,arguments).exponent(.5)}const vf=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:vn,scaleDiverging:Kd,scaleDivergingLog:Xd,scaleDivergingPow:zc,scaleDivergingSqrt:LM,scaleDivergingSymlog:Vd,scaleIdentity:$d,scaleImplicit:iu,scaleLinear:ki,scaleLog:Ed,scaleOrdinal:Oc,scalePoint:nn,scalePow:Cc,scaleQuantile:Cd,scaleQuantize:Id,scaleRadial:Md,scaleSequential:Ud,scaleSequentialLog:qd,scaleSequentialPow:Wc,scaleSequentialQuantile:Gd,scaleSequentialSqrt:RM,scaleSequentialSymlog:Hd,scaleSqrt:bj,scaleSymlog:jd,scaleThreshold:Nd,scaleTime:DM,scaleUtc:BM,tickFormat:Pd},Symbol.toStringTag,{value:"Module"}));var FM=Mr;function WM(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!FM(o):r(o,u)))var u=o,c=a}return c}var Yd=WM;function zM(e,t){return e>t}var UM=zM,qM=Yd,HM=UM,GM=Rr;function KM(e){return e&&e.length?qM(e,GM,HM):void 0}var XM=KM;const La=ue(XM);function VM(e,t){return e<t}var YM=VM,ZM=Yd,JM=YM,QM=Rr;function eC(e){return e&&e.length?ZM(e,QM,JM):void 0}var tC=eC;const Fa=ue(tC);var rC=rc,nC=Mt,iC=id,aC=Ne;function oC(e,t){var r=aC(e)?rC:iC;return r(e,nC(t))}var uC=oC,cC=rd,lC=uC;function sC(e,t){return cC(lC(e,t),1)}var fC=sC;const pC=ue(fC);var hC=gc;function dC(e,t){return hC(e,t)}var vC=dC;const Uc=ue(vC);var Fr=1e9,yC={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Hc,fe=!0,qe="[DecimalError] ",Gt=qe+"Invalid argument: ",qc=qe+"Exponent out of range: ",Wr=Math.floor,Ft=Math.pow,gC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Re,we=1e7,se=7,Zd=9007199254740991,Li=Wr(Zd/se),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*se;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return gt(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return ae(gt(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return ye(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Re))throw Error(qe+"NaN");if(r.s<1)throw Error(qe+(r.s?"NaN":"-Infinity"));return r.eq(Re)?new n(0):(fe=!1,t=gt(xn(r,a),xn(e,a),a),fe=!0,ae(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?ev(t,e):Jd(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(qe+"NaN");return r.s?(fe=!1,t=gt(r,e,0,1).times(e),fe=!0,r.minus(t)):ae(new n(r),i)};W.naturalExponential=W.exp=function(){return Qd(this)};W.naturalLogarithm=W.ln=function(){return xn(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Jd(t,e):ev(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Gt+e);if(t=ye(i)+1,n=i.d.length-1,r=n*se+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(qe+"NaN")}for(e=ye(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=et(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Wr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(gt(u,a,o+2)).times(.5),et(a.d).slice(0,o)===(t=et(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,ae(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,c,l,f=this,s=f.constructor,p=f.d,h=(e=new s(e)).d;if(!f.s||!e.s)return new s(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,l=h.length,c<l&&(a=p,p=h,h=a,o=c,c=l,l=o),a=[],o=c+l,n=o;n--;)a.push(0);for(n=l;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%we|0,t=u/we|0;a[i]=(a[i]+t)%we|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?ae(e,s.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(nt(e,0,Fr),t===void 0?t=n.rounding:nt(t,0,8),ae(r,e+ye(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Xt(n,!0):(nt(e,0,Fr),t===void 0?t=i.rounding:nt(t,0,8),n=ae(new i(n),e+1,t),r=Xt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Xt(i):(nt(e,0,Fr),t===void 0?t=a.rounding:nt(t,0,8),n=ae(new a(i),e+ye(i)+1,t),r=Xt(n.abs(),!1,e+ye(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return ae(new t(e),ye(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,l=12,f=+(e=new c(e));if(!e.s)return new c(Re);if(u=new c(u),!u.s){if(e.s<1)throw Error(qe+"Infinity");return u}if(u.eq(Re))return u;if(n=c.precision,e.eq(Re))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Zd){for(i=new c(Re),t=Math.ceil(n/se+4),fe=!1;r%2&&(i=i.times(u),gf(i.d,t)),r=Wr(r/2),r!==0;)u=u.times(u),gf(u.d,t);return fe=!0,e.s<0?new c(Re).div(i):ae(i,n)}}else if(a<0)throw Error(qe+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(xn(u,n+l)),fe=!0,i=Qd(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ye(i),n=Xt(i,r<=a.toExpNeg||r>=a.toExpPos)):(nt(e,1,Fr),t===void 0?t=a.rounding:nt(t,0,8),i=ae(new a(i),e,t),r=ye(i),n=Xt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(nt(e,1,Fr),t===void 0?t=n.rounding:nt(t,0,8)),ae(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ye(e),r=e.constructor;return Xt(e,t<=r.toExpNeg||t>=r.toExpPos)};function Jd(e,t){var r,n,i,a,o,u,c,l,f=e.constructor,s=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?ae(t,s):t;if(c=e.d,l=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=l.length):(n=l,i=o,u=c.length),o=Math.ceil(s/se),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=l.length,u-a<0&&(a=u,n=l,l=c,c=n),r=0;a;)r=(c[--a]=c[a]+l[a]+r)/we|0,c[a]%=we;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,fe?ae(t,s):t}function nt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Gt+e)}function et(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=se-n.length,r&&(a+=_t(r)),a+=n;o=e[t],n=o+"",r=se-n.length,r&&(a+=_t(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var gt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%we|0,o=a/we|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*we+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,l,f,s,p,h,y,v,d,w,b,x,m,g,O,S,_,T=n.constructor,j=n.s==i.s?1:-1,P=n.d,$=i.d;if(!n.s)return new T(n);if(!i.s)throw Error(qe+"Division by zero");for(c=n.e-i.e,S=$.length,g=P.length,h=new T(j),y=h.d=[],l=0;$[l]==(P[l]||0);)++l;if($[l]>(P[l]||0)&&--c,a==null?b=a=T.precision:o?b=a+(ye(n)-ye(i))+1:b=a,b<0)return new T(0);if(b=b/se+2|0,l=0,S==1)for(f=0,$=$[0],b++;(l<g||f)&&b--;l++)x=f*we+(P[l]||0),y[l]=x/$|0,f=x%$|0;else{for(f=we/($[0]+1)|0,f>1&&($=e($,f),P=e(P,f),S=$.length,g=P.length),m=S,v=P.slice(0,S),d=v.length;d<S;)v[d++]=0;_=$.slice(),_.unshift(0),O=$[0],$[1]>=we/2&&++O;do f=0,u=t($,v,S,d),u<0?(w=v[0],S!=d&&(w=w*we+(v[1]||0)),f=w/O|0,f>1?(f>=we&&(f=we-1),s=e($,f),p=s.length,d=v.length,u=t(s,v,p,d),u==1&&(f--,r(s,S<p?_:$,p))):(f==0&&(u=f=1),s=$.slice()),p=s.length,p<d&&s.unshift(0),r(v,s,d),u==-1&&(d=v.length,u=t($,v,S,d),u<1&&(f++,r(v,S<d?_:$,d))),d=v.length):u===0&&(f++,v=[0]),y[l++]=f,u&&v[0]?v[d++]=P[m]||0:(v=[P[m]],d=1);while((m++<g||v[0]!==void 0)&&b--)}return y[0]||y.shift(),h.e=c,ae(h,o?a+ye(h)+1:a)}}();function Qd(e,t){var r,n,i,a,o,u,c=0,l=0,f=e.constructor,s=f.precision;if(ye(e)>16)throw Error(qc+ye(e));if(!e.s)return new f(Re);for(t==null?(fe=!1,u=s):u=t,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),l+=5;for(n=Math.log(Ft(2,l))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Re),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(gt(i,r,u)),et(o.d).slice(0,u)===et(a.d).slice(0,u)){for(;l--;)a=ae(a.times(a),u);return f.precision=s,t==null?(fe=!0,ae(a,s)):a}a=o}}function ye(e){for(var t=e.e*se,r=e.d[0];r>=10;r/=10)t++;return t}function bo(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(qe+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function _t(e){for(var t="";e--;)t+="0";return t}function xn(e,t){var r,n,i,a,o,u,c,l,f,s=1,p=10,h=e,y=h.d,v=h.constructor,d=v.precision;if(h.s<1)throw Error(qe+(h.s?"NaN":"-Infinity"));if(h.eq(Re))return new v(0);if(t==null?(fe=!1,l=d):l=t,h.eq(10))return t==null&&(fe=!0),bo(v,l);if(l+=p,v.precision=l,r=et(y),n=r.charAt(0),a=ye(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=et(h.d),n=r.charAt(0),s++;a=ye(h),n>1?(h=new v("0."+r),a++):h=new v(n+"."+r.slice(1))}else return c=bo(v,l+2,d).times(a+""),h=xn(new v(n+"."+r.slice(1)),l-p).plus(c),v.precision=d,t==null?(fe=!0,ae(h,d)):h;for(u=o=h=gt(h.minus(Re),h.plus(Re),l),f=ae(h.times(h),l),i=3;;){if(o=ae(o.times(f),l),c=u.plus(gt(o,new v(i),l)),et(c.d).slice(0,l)===et(u.d).slice(0,l))return u=u.times(2),a!==0&&(u=u.plus(bo(v,l+2,d).times(a+""))),u=gt(u,new v(s),l),v.precision=d,t==null?(fe=!0,ae(u,d)):u;u=c,i+=2}}function yf(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Wr(r/se),e.d=[],n=(r+1)%se,r<0&&(n+=se),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=se;n<i;)e.d.push(+t.slice(n,n+=se));t=t.slice(n),n=se-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Li||e.e<-Li))throw Error(qc+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,l,f,s=e.d;for(o=1,a=s[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=se,i=t,l=s[f=0];else{if(f=Math.ceil((n+1)/se),a=s.length,f>=a)return e;for(l=a=s[f],o=1;a>=10;a/=10)o++;n%=se,i=n-se+o}if(r!==void 0&&(a=Ft(10,o-i-1),u=l/a%10|0,c=t<0||s[f+1]!==void 0||l%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?l/Ft(10,o-i):0:s[f-1])%10&1||r==(e.s<0?8:7))),t<1||!s[0])return c?(a=ye(e),s.length=1,t=t-a-1,s[0]=Ft(10,(se-t%se)%se),e.e=Wr(-t/se)||0):(s.length=1,s[0]=e.e=e.s=0),e;if(n==0?(s.length=f,a=1,f--):(s.length=f+1,a=Ft(10,se-n),s[f]=i>0?(l/Ft(10,o-i)%Ft(10,i)|0)*a:0),c)for(;;)if(f==0){(s[0]+=a)==we&&(s[0]=1,++e.e);break}else{if(s[f]+=a,s[f]!=we)break;s[f--]=0,a=1}for(n=s.length;s[--n]===0;)s.pop();if(fe&&(e.e>Li||e.e<-Li))throw Error(qc+ye(e));return e}function ev(e,t){var r,n,i,a,o,u,c,l,f,s,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),fe?ae(t,h):t;if(c=e.d,s=t.d,n=t.e,l=e.e,c=c.slice(),o=l-n,o){for(f=o<0,f?(r=c,o=-o,u=s.length):(r=s,n=l,u=c.length),i=Math.max(Math.ceil(h/se),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=s.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=s[i]){f=c[i]<s[i];break}o=0}for(f&&(r=c,c=s,s=r,t.s=-t.s),u=c.length,i=s.length-u;i>0;--i)c[u++]=0;for(i=s.length;i>o;){if(c[--i]<s[i]){for(a=i;a&&c[--a]===0;)c[a]=we-1;--c[a],c[i]+=we}c[i]-=s[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,fe?ae(t,h):t):new p(0)}function Xt(e,t,r){var n,i=ye(e),a=et(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+_t(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+_t(-i-1)+a,r&&(n=r-o)>0&&(a+=_t(n))):i>=o?(a+=_t(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+_t(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=_t(n))),e.s<0?"-"+a:a}function gf(e,t){if(e.length>t)return e.length=t,!0}function tv(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Gt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return yf(o,a.toString())}else if(typeof a!="string")throw Error(Gt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,gC.test(a))yf(o,a);else throw Error(Gt+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=tv,i.config=i.set=mC,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function mC(e){if(!e||typeof e!="object")throw Error(qe+"Object expected");var t,r,n,i=["precision",1,Fr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Wr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Gt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Gt+r+": "+n);return this}var Hc=tv(yC);Re=new Hc(1);const ie=Hc;function bC(e){return SC(e)||OC(e)||wC(e)||xC()}function xC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wC(e,t){if(e){if(typeof e=="string")return cu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cu(e,t)}}function OC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function SC(e){if(Array.isArray(e))return cu(e)}function cu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var _C=function(t){return t},rv={"@@functional/placeholder":!0},nv=function(t){return t===rv},mf=function(t){return function r(){return arguments.length===0||arguments.length===1&&nv(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},AC=function e(t,r){return t===1?r:mf(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==rv}).length;return o>=t?r.apply(void 0,i):e(t-o,mf(function(){for(var u=arguments.length,c=new Array(u),l=0;l<u;l++)c[l]=arguments[l];var f=i.map(function(s){return nv(s)?c.shift():s});return r.apply(void 0,bC(f).concat(c))}))})},Wa=function(t){return AC(t.length,t)},lu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},PC=Wa(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),$C=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return _C;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},su=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},iv=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function TC(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function EC(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var jC=Wa(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),MC=Wa(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),CC=Wa(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const za={rangeStep:EC,getDigitCount:TC,interpolateNumber:jC,uninterpolateNumber:MC,uninterpolateTruncation:CC};function fu(e){return kC(e)||NC(e)||av(e)||IC()}function IC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function NC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function kC(e){if(Array.isArray(e))return pu(e)}function wn(e,t){return RC(e)||BC(e,t)||av(e,t)||DC()}function DC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function av(e,t){if(e){if(typeof e=="string")return pu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pu(e,t)}}function pu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BC(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function RC(e){if(Array.isArray(e))return e}function ov(e){var t=wn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function uv(e,t,r){if(e.lte(0))return new ie(0);var n=za.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function LC(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(za.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=$C(PC(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),lu);return u(0,t)}function cv(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=uv(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),l=u+c+1;return l>r?cv(e,t,r,n,i+1):(l<r&&(c=t>0?c+(r-l):c,u=t>0?u:u+(r-l)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function FC(e){var t=wn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=ov([r,n]),c=wn(u,2),l=c[0],f=c[1];if(l===-1/0||f===1/0){var s=f===1/0?[l].concat(fu(lu(0,i-1).map(function(){return 1/0}))):[].concat(fu(lu(0,i-1).map(function(){return-1/0})),[f]);return r>n?su(s):s}if(l===f)return LC(l,i,a);var p=cv(l,f,o,a),h=p.step,y=p.tickMin,v=p.tickMax,d=za.rangeStep(y,v.add(new ie(.1).mul(h)),h);return r>n?su(d):d}function WC(e,t){var r=wn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=ov([n,i]),u=wn(o,2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[n,i];if(c===l)return[c];var f=Math.max(t,2),s=uv(new ie(l).sub(c).div(f-1),a,0),p=[].concat(fu(za.rangeStep(new ie(c),new ie(l).sub(new ie(.99).mul(s)),s)),[l]);return n>i?su(p):p}var zC=iv(FC),UC=iv(WC),qC="Invariant failed";function Vt(e,t){throw new Error(qC)}var HC=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function mr(e){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(e)}function Fi(){return Fi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fi.apply(this,arguments)}function GC(e,t){return YC(e)||VC(e,t)||XC(e,t)||KC()}function KC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XC(e,t){if(e){if(typeof e=="string")return bf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bf(e,t)}}function bf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function VC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function YC(e){if(Array.isArray(e))return e}function ZC(e,t){if(e==null)return{};var r=JC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function QC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function eI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fv(n.key),n)}}function tI(e,t,r){return t&&eI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function rI(e,t,r){return t=Wi(t),nI(e,lv()?Reflect.construct(t,r||[],Wi(e).constructor):t.apply(e,r))}function nI(e,t){if(t&&(mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iI(e)}function iI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(lv=function(){return!!e})()}function Wi(e){return Wi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wi(e)}function aI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hu(e,t)}function hu(e,t){return hu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hu(e,t)}function sv(e,t,r){return t=fv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fv(e){var t=oI(e,"string");return mr(t)=="symbol"?t:t+""}function oI(e,t){if(mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ua=function(e){function t(){return QC(this,t),rI(this,t,arguments)}return aI(t,e),tI(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,l=n.dataPointFormatter,f=n.xAxis,s=n.yAxis,p=ZC(n,HC),h=ee(p,!1);this.props.direction==="x"&&f.type!=="number"&&Vt();var y=c.map(function(v){var d=l(v,u),w=d.x,b=d.y,x=d.value,m=d.errorVal;if(!m)return null;var g=[],O,S;if(Array.isArray(m)){var _=GC(m,2);O=_[0],S=_[1]}else O=S=m;if(a==="vertical"){var T=f.scale,j=b+i,P=j+o,$=j-o,C=T(x-O),M=T(x+S);g.push({x1:M,y1:P,x2:M,y2:$}),g.push({x1:C,y1:j,x2:M,y2:j}),g.push({x1:C,y1:P,x2:C,y2:$})}else if(a==="horizontal"){var N=s.scale,k=w+i,D=k-o,L=k+o,z=N(x-O),G=N(x+S);g.push({x1:D,y1:G,x2:L,y2:G}),g.push({x1:k,y1:z,x2:k,y2:G}),g.push({x1:D,y1:z,x2:L,y2:z})}return A.createElement(Oe,Fi({className:"recharts-errorBar",key:"bar-".concat(g.map(function(q){return"".concat(q.x1,"-").concat(q.x2,"-").concat(q.y1,"-").concat(q.y2)}))},h),g.map(function(q){return A.createElement("line",Fi({},q,{key:"line-".concat(q.x1,"-").concat(q.x2,"-").concat(q.y1,"-").concat(q.y2)}))}))});return A.createElement(Oe,{className:"recharts-errorBars"},y)}}])}(A.Component);sv(Ua,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});sv(Ua,"displayName","ErrorBar");function On(e){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(e)}function xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xf(Object(r),!0).forEach(function(n){uI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uI(e,t,r){return t=cI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cI(e){var t=lI(e,"string");return On(t)=="symbol"?t:t+""}function lI(e,t){if(On(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(On(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pv=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Be(r,ur);if(!o)return null;var u=ur.defaultProps,c=u!==void 0?Bt(Bt({},u),o.props):{},l;return o.props&&o.props.payload?l=o.props&&o.props.payload:a==="children"?l=(n||[]).reduce(function(f,s){var p=s.item,h=s.props,y=h.sectors||h.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||p.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):l=(n||[]).map(function(f){var s=f.item,p=s.type.defaultProps,h=p!==void 0?Bt(Bt({},p),s.props):{},y=h.dataKey,v=h.name,d=h.legendType,w=h.hide;return{inactive:w,dataKey:y,type:c.iconType||d||"square",color:Gc(s),value:v||y,payload:h}}),Bt(Bt(Bt({},c),ur.getWithHeight(o,i)),{},{payload:l,item:o})};function Sn(e){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(e)}function wf(e){return hI(e)||pI(e)||fI(e)||sI()}function sI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fI(e,t){if(e){if(typeof e=="string")return du(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return du(e,t)}}function pI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function hI(e){if(Array.isArray(e))return du(e)}function du(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Of(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Of(Object(r),!0).forEach(function(n){lr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Of(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lr(e,t,r){return t=dI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dI(e){var t=vI(e,"string");return Sn(t)=="symbol"?t:t+""}function vI(e,t){if(Sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function it(e,t,r){return te(e)||te(t)?r:be(t)?Ue(e,t,r):Z(t)?t(e):r}function an(e,t,r,n){var i=pC(e,function(u){return it(u,t)});if(r==="number"){var a=i.filter(function(u){return R(u)||parseFloat(u)});return a.length?[Fa(a),La(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!te(u)}):i;return o.map(function(u){return be(u)||u instanceof Date?u:""})}var yI=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,l=0;l<u;l++){var f=l>0?i[l-1].coordinate:i[u-1].coordinate,s=i[l].coordinate,p=l>=u-1?i[0].coordinate:i[l+1].coordinate,h=void 0;if(Ye(s-f)!==Ye(p-s)){var y=[];if(Ye(p-s)===Ye(c[1]-c[0])){h=p;var v=s+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{h=f;var d=p+c[1]-c[0];y[0]=Math.min(s,(d+s)/2),y[1]=Math.max(s,(d+s)/2)}var w=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(t>w[0]&&t<=w[1]||t>=y[0]&&t<=y[1]){o=i[l].index;break}}else{var b=Math.min(f,p),x=Math.max(f,p);if(t>(b+s)/2&&t<=(x+s)/2){o=i[l].index;break}}}else for(var m=0;m<u;m++)if(m===0&&t<=(n[m].coordinate+n[m+1].coordinate)/2||m>0&&m<u-1&&t>(n[m].coordinate+n[m-1].coordinate)/2&&t<=(n[m].coordinate+n[m+1].coordinate)/2||m===u-1&&t>(n[m].coordinate+n[m-1].coordinate)/2){o=n[m].index;break}return o},Gc=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},gI=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,l=u.length;c<l;c++)for(var f=a[u[c]].stackGroups,s=Object.keys(f),p=0,h=s.length;p<h;p++){var y=f[s[p]],v=y.items,d=y.cateAxisId,w=v.filter(function(S){return vt(S.type).indexOf("Bar")>=0});if(w&&w.length){var b=w[0].type.defaultProps,x=b!==void 0?pe(pe({},b),w[0].props):w[0].props,m=x.barSize,g=x[d];o[g]||(o[g]=[]);var O=te(m)?r:m;o[g].push({item:w[0],stackList:w.slice(1),barSize:te(O)?void 0:Ze(O,n,0)})}}return o},mI=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var l=Ze(r,i,0,!0),f,s=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,y=o.reduce(function(m,g){return m+g.barSize||0},0);y+=(c-1)*l,y>=i&&(y-=(c-1)*l,l=0),y>=i&&h>0&&(p=!0,h*=.9,y=c*h);var v=(i-y)/2>>0,d={offset:v-l,size:0};f=o.reduce(function(m,g){var O={item:g.item,position:{offset:d.offset+d.size+l,size:p?h:g.barSize}},S=[].concat(wf(m),[O]);return d=S[S.length-1].position,g.stackList&&g.stackList.length&&g.stackList.forEach(function(_){S.push({item:_,position:d})}),S},s)}else{var w=Ze(n,i,0,!0);i-2*w-(c-1)*l<=0&&(l=0);var b=(i-2*w-(c-1)*l)/c;b>1&&(b>>=0);var x=u===+u?Math.min(b,u):b;f=o.reduce(function(m,g,O){var S=[].concat(wf(m),[{item:g.item,position:{offset:w+(b+l)*O+(b-x)/2,size:x}}]);return g.stackList&&g.stackList.length&&g.stackList.forEach(function(_){S.push({item:_,position:S[S.length-1].position})}),S},s)}return f},bI=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),l=pv({children:a,legendWidth:c});if(l){var f=i||{},s=f.width,p=f.height,h=l.align,y=l.verticalAlign,v=l.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&h!=="center"&&R(t[h]))return pe(pe({},t),{},lr({},h,t[h]+(s||0)));if((v==="horizontal"||v==="vertical"&&h==="center")&&y!=="middle"&&R(t[y]))return pe(pe({},t),{},lr({},y,t[y]+(p||0)))}return t},xI=function(t,r,n){return te(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},hv=function(t,r,n,i,a){var o=r.props.children,u=Je(o,Ua).filter(function(l){return xI(i,a,l.props.direction)});if(u&&u.length){var c=u.map(function(l){return l.props.dataKey});return t.reduce(function(l,f){var s=it(f,n);if(te(s))return l;var p=Array.isArray(s)?[Fa(s),La(s)]:[s,s],h=c.reduce(function(y,v){var d=it(f,v,0),w=p[0]-Math.abs(Array.isArray(d)?d[0]:d),b=p[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(w,y[0]),Math.max(b,y[1])]},[1/0,-1/0]);return[Math.min(h[0],l[0]),Math.max(h[1],l[1])]},[1/0,-1/0])}return null},wI=function(t,r,n,i,a){var o=r.map(function(u){return hv(t,u,n,a,i)}).filter(function(u){return!te(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},dv=function(t,r,n,i,a){var o=r.map(function(c){var l=c.props.dataKey;return n==="number"&&l&&hv(t,c,l,i)||an(t,l,n,a)});if(n==="number")return o.reduce(function(c,l){return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,l){for(var f=0,s=l.length;f<s;f++)u[l[f]]||(u[l[f]]=!0,c.push(l[f]));return c},[])},vv=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},yv=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},dt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,l=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(l=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ye(u[0]-u[1])*2*l:l,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(s){var p=a?a.indexOf(s):s;return{coordinate:i(p)+l,value:s,offset:l}});return f.filter(function(s){return!Hn(s.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(s,p){return{coordinate:i(s)+l,value:s,index:p,offset:l}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(s){return{coordinate:i(s)+l,value:s,offset:l}}):i.domain().map(function(s,p){return{coordinate:i(s)+l,value:a?a[s]:s,index:p,offset:l}})},xo=new WeakMap,ui=function(t,r){if(typeof r!="function")return t;xo.has(t)||xo.set(t,new WeakMap);var n=xo.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},gv=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:vn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:ki(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:nn(),realScaleType:"point"}:a==="category"?{scale:vn(),realScaleType:"band"}:{scale:ki(),realScaleType:"linear"};if(qn(i)){var c="scale".concat(Pa(i));return{scale:(vf[c]||nn)(),realScaleType:vf[c]?c:"point"}}return Z(i)?{scale:i}:{scale:nn(),realScaleType:"point"}},Sf=1e-4,mv=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Sf,o=Math.max(i[0],i[1])+Sf,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},OI=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},SI=function(t,r){if(!r||r.length!==2||!R(r[0])||!R(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!R(t[0])||t[0]<n)&&(a[0]=n),(!R(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},_I=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=Hn(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},AI=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Hn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},PI={sign:_I,expand:Ux,none:sr,silhouette:qx,wiggle:Hx,positive:AI},$I=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=PI[n],o=zx().keys(i).value(function(u,c){return+it(u,c,0)}).order(Lo).offset(a);return o(t)},TI=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},l=u.reduce(function(s,p){var h,y=(h=p.type)!==null&&h!==void 0&&h.defaultProps?pe(pe({},p.type.defaultProps),p.props):p.props,v=y.stackId,d=y.hide;if(d)return s;var w=y[n],b=s[w]||{hasStack:!1,stackGroups:{}};if(be(v)){var x=b.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),b.hasStack=!0,b.stackGroups[v]=x}else b.stackGroups[Aa("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return pe(pe({},s),{},lr({},w,b))},c),f={};return Object.keys(l).reduce(function(s,p){var h=l[p];if(h.hasStack){var y={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(v,d){var w=h.stackGroups[d];return pe(pe({},v),{},lr({},d,{numericAxisId:n,cateAxisId:i,items:w.items,stackedData:$I(t,w.items,a)}))},y)}return pe(pe({},s),{},lr({},p,h))},f)},bv=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var l=t.domain();if(!l.length)return null;var f=zC(l,a,u);return t.domain([Fa(f),La(f)]),{niceTicks:f}}if(a&&i==="number"){var s=t.domain(),p=UC(s,a,u);return{niceTicks:p}}return null},_f=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=it(o,r.dataKey,r.domain[u]);return te(c)?null:r.scale(c)-a/2+i},EI=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},jI=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(be(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},MI=function(t){return t.reduce(function(r,n){return[Fa(n.concat([r[0]]).filter(R)),La(n.concat([r[1]]).filter(R))]},[1/0,-1/0])},xv=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(l,f){var s=MI(f.slice(r,n+1));return[Math.min(l[0],s[0]),Math.max(l[1],s[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Af=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Pf=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,vu=function(t,r,n){if(Z(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(R(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Af.test(t[0])){var a=+Af.exec(t[0])[1];i[0]=r[0]-a}else Z(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(R(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Pf.test(t[1])){var o=+Pf.exec(t[1])[1];i[1]=r[1]+o}else Z(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},zi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=bc(r,function(s){return s.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var l=a[u],f=a[u-1];o=Math.min((l.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},$f=function(t,r,n){return!t||!t.length||Uc(t,Ue(n,"type.defaultProps.domain"))?r:t},wv=function(t,r){var n=t.type.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,l=n.chartType,f=n.hide;return pe(pe({},ee(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Gc(t),value:it(r,i),type:c,payload:r,chartType:l,hide:f})};function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}function Tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ft(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tf(Object(r),!0).forEach(function(n){Ov(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ov(e,t,r){return t=CI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function CI(e){var t=II(e,"string");return _n(t)=="symbol"?t:t+""}function II(e,t){if(_n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function NI(e,t){return RI(e)||BI(e,t)||DI(e,t)||kI()}function kI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DI(e,t){if(e){if(typeof e=="string")return Ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ef(e,t)}}function Ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function RI(e){if(Array.isArray(e))return e}var Ui=Math.PI/180,LI=function(t){return t*180/Math.PI},Pe=function(t,r,n,i){return{x:t+Math.cos(-Ui*i)*n,y:r+Math.sin(-Ui*i)*n}},FI=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},GF=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,l=t.endAngle,f=Ze(t.cx,o,o/2),s=Ze(t.cy,u,u/2),p=FI(o,u,n),h=Ze(t.innerRadius,p,0),y=Ze(t.outerRadius,p,p*.8),v=Object.keys(r);return v.reduce(function(d,w){var b=r[w],x=b.domain,m=b.reversed,g;if(te(b.range))i==="angleAxis"?g=[c,l]:i==="radiusAxis"&&(g=[h,y]),m&&(g=[g[1],g[0]]);else{g=b.range;var O=g,S=NI(O,2);c=S[0],l=S[1]}var _=gv(b,a),T=_.realScaleType,j=_.scale;j.domain(x).range(g),mv(j);var P=bv(j,ft(ft({},b),{},{realScaleType:T})),$=ft(ft(ft({},b),P),{},{range:g,radius:y,realScaleType:T,scale:j,cx:f,cy:s,innerRadius:h,outerRadius:y,startAngle:c,endAngle:l});return ft(ft({},d),{},Ov({},w,$))},{})},WI=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},zI=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=WI({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,l=Math.acos(c);return i>o&&(l=2*Math.PI-l),{radius:u,angle:LI(l),angleInRadian:l}},UI=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},qI=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},jf=function(t,r){var n=t.x,i=t.y,a=zI({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,l=r.outerRadius;if(o<c||o>l)return!1;if(o===0)return!0;var f=UI(r),s=f.startAngle,p=f.endAngle,h=u,y;if(s<=p){for(;h>p;)h-=360;for(;h<s;)h+=360;y=h>=s&&h<=p}else{for(;h>s;)h-=360;for(;h<p;)h+=360;y=h>=p&&h<=s}return y?ft(ft({},r),{},{radius:o,angle:qI(h,r)}):null},KF=function(t){return!B.isValidElement(t)&&!Z(t)&&typeof t!="boolean"?t.className:""};function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(e)}var HI=["offset"];function GI(e){return YI(e)||VI(e)||XI(e)||KI()}function KI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XI(e,t){if(e){if(typeof e=="string")return yu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yu(e,t)}}function VI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function YI(e){if(Array.isArray(e))return yu(e)}function yu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZI(e,t){if(e==null)return{};var r=JI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mf(Object(r),!0).forEach(function(n){QI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QI(e,t,r){return t=e2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e2(e){var t=t2(e,"string");return An(t)=="symbol"?t:t+""}function t2(e,t){if(An(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(An(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pn(){return Pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pn.apply(this,arguments)}var r2=function(t){var r=t.value,n=t.formatter,i=te(t.children)?r:t.children;return Z(n)?n(i):i},n2=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),360);return n*i},i2=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,l=c.cx,f=c.cy,s=c.innerRadius,p=c.outerRadius,h=c.startAngle,y=c.endAngle,v=c.clockWise,d=(s+p)/2,w=n2(h,y),b=w>=0?1:-1,x,m;i==="insideStart"?(x=h+b*o,m=v):i==="insideEnd"?(x=y-b*o,m=!v):i==="end"&&(x=y+b*o,m=v),m=w<=0?m:!m;var g=Pe(l,f,d,x),O=Pe(l,f,d,x+(m?1:-1)*359),S="M".concat(g.x,",").concat(g.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(m?0:1,`,
    `).concat(O.x,",").concat(O.y),_=te(t.id)?Aa("recharts-radial-line-"):t.id;return A.createElement("text",Pn({},n,{dominantBaseline:"central",className:re("recharts-radial-bar-label",u)}),A.createElement("defs",null,A.createElement("path",{id:_,d:S})),A.createElement("textPath",{xlinkHref:"#".concat(_)},r))},a2=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,l=a.outerRadius,f=a.startAngle,s=a.endAngle,p=(f+s)/2;if(i==="outside"){var h=Pe(o,u,l+n,p),y=h.x,v=h.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(c+l)/2,w=Pe(o,u,d,p),b=w.x,x=w.y;return{x:b,y:x,textAnchor:"middle",verticalAnchor:"middle"}},o2=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,l=o.width,f=o.height,s=f>=0?1:-1,p=s*i,h=s>0?"end":"start",y=s>0?"start":"end",v=l>=0?1:-1,d=v*i,w=v>0?"end":"start",b=v>0?"start":"end";if(a==="top"){var x={x:u+l/2,y:c-s*i,textAnchor:"middle",verticalAnchor:h};return me(me({},x),n?{height:Math.max(c-n.y,0),width:l}:{})}if(a==="bottom"){var m={x:u+l/2,y:c+f+p,textAnchor:"middle",verticalAnchor:y};return me(me({},m),n?{height:Math.max(n.y+n.height-(c+f),0),width:l}:{})}if(a==="left"){var g={x:u-d,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return me(me({},g),n?{width:Math.max(g.x-n.x,0),height:f}:{})}if(a==="right"){var O={x:u+l+d,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return me(me({},O),n?{width:Math.max(n.x+n.width-O.x,0),height:f}:{})}var S=n?{width:l,height:f}:{};return a==="insideLeft"?me({x:u+d,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},S):a==="insideRight"?me({x:u+l-d,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},S):a==="insideTop"?me({x:u+l/2,y:c+p,textAnchor:"middle",verticalAnchor:y},S):a==="insideBottom"?me({x:u+l/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},S):a==="insideTopLeft"?me({x:u+d,y:c+p,textAnchor:b,verticalAnchor:y},S):a==="insideTopRight"?me({x:u+l-d,y:c+p,textAnchor:w,verticalAnchor:y},S):a==="insideBottomLeft"?me({x:u+d,y:c+f-p,textAnchor:b,verticalAnchor:h},S):a==="insideBottomRight"?me({x:u+l-d,y:c+f-p,textAnchor:w,verticalAnchor:h},S):Cr(a)&&(R(a.x)||Wt(a.x))&&(R(a.y)||Wt(a.y))?me({x:u+Ze(a.x,l),y:c+Ze(a.y,f),textAnchor:"end",verticalAnchor:"end"},S):me({x:u+l/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},S)},u2=function(t){return"cx"in t&&R(t.cx)};function Ee(e){var t=e.offset,r=t===void 0?5:t,n=ZI(e,HI),i=me({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,l=i.content,f=i.className,s=f===void 0?"":f,p=i.textBreakAll;if(!a||te(u)&&te(c)&&!B.isValidElement(l)&&!Z(l))return null;if(B.isValidElement(l))return B.cloneElement(l,i);var h;if(Z(l)){if(h=B.createElement(l,i),B.isValidElement(h))return h}else h=r2(i);var y=u2(a),v=ee(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return i2(i,h,v);var d=y?a2(i):o2(i);return A.createElement(Ti,Pn({className:re("recharts-label",s)},v,d,{breakAll:p}),h)}Ee.displayName="Label";var Sv=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,l=t.innerRadius,f=t.outerRadius,s=t.x,p=t.y,h=t.top,y=t.left,v=t.width,d=t.height,w=t.clockWise,b=t.labelViewBox;if(b)return b;if(R(v)&&R(d)){if(R(s)&&R(p))return{x:s,y:p,width:v,height:d};if(R(h)&&R(y))return{x:h,y,width:v,height:d}}return R(s)&&R(p)?{x:s,y:p,width:0,height:0}:R(r)&&R(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:l||0,outerRadius:f||c||u||0,clockWise:w}:t.viewBox?t.viewBox:{}},c2=function(t,r){return t?t===!0?A.createElement(Ee,{key:"label-implicit",viewBox:r}):be(t)?A.createElement(Ee,{key:"label-implicit",viewBox:r,value:t}):B.isValidElement(t)?t.type===Ee?B.cloneElement(t,{key:"label-implicit",viewBox:r}):A.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):Z(t)?A.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):Cr(t)?A.createElement(Ee,Pn({viewBox:r},t,{key:"label-implicit"})):null:null},l2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=Sv(t),o=Je(i,Ee).map(function(c,l){return B.cloneElement(c,{viewBox:r||a,key:"label-".concat(l)})});if(!n)return o;var u=c2(t.label,r||a);return[u].concat(GI(o))};Ee.parseViewBox=Sv;Ee.renderCallByParent=l2;function s2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var f2=s2;const p2=ue(f2);function $n(e){"@babel/helpers - typeof";return $n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$n(e)}var h2=["valueAccessor"],d2=["data","dataKey","clockWise","id","textBreakAll"];function v2(e){return b2(e)||m2(e)||g2(e)||y2()}function y2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function g2(e,t){if(e){if(typeof e=="string")return gu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gu(e,t)}}function m2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function b2(e){if(Array.isArray(e))return gu(e)}function gu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qi(){return qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qi.apply(this,arguments)}function Cf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function If(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cf(Object(r),!0).forEach(function(n){x2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function x2(e,t,r){return t=w2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w2(e){var t=O2(e,"string");return $n(t)=="symbol"?t:t+""}function O2(e,t){if($n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if($n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Nf(e,t){if(e==null)return{};var r=S2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function S2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var _2=function(t){return Array.isArray(t.value)?p2(t.value):t.value};function Kt(e){var t=e.valueAccessor,r=t===void 0?_2:t,n=Nf(e,h2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,l=Nf(n,d2);return!i||!i.length?null:A.createElement(Oe,{className:"recharts-label-list"},i.map(function(f,s){var p=te(a)?r(f,s):it(f&&f.payload,a),h=te(u)?{}:{id:"".concat(u,"-").concat(s)};return A.createElement(Ee,qi({},ee(f,!0),l,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:Ee.parseViewBox(te(o)?f:If(If({},f),{},{clockWise:o})),key:"label-".concat(s),index:s}))}))}Kt.displayName="LabelList";function A2(e,t){return e?e===!0?A.createElement(Kt,{key:"labelList-implicit",data:t}):A.isValidElement(e)||Z(e)?A.createElement(Kt,{key:"labelList-implicit",data:t,content:e}):Cr(e)?A.createElement(Kt,qi({data:t},e,{key:"labelList-implicit"})):null:null}function P2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Je(n,Kt).map(function(o,u){return B.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=A2(e.label,t);return[a].concat(v2(i))}Kt.renderCallByParent=P2;function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function mu(){return mu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mu.apply(this,arguments)}function kf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Df(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kf(Object(r),!0).forEach(function(n){$2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $2(e,t,r){return t=T2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T2(e){var t=E2(e,"string");return Tn(t)=="symbol"?t:t+""}function E2(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var j2=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},ci=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,f=c*(u?1:-1)+i,s=Math.asin(c/f)/Ui,p=l?a:a+o*s,h=Pe(r,n,f,p),y=Pe(r,n,i,p),v=l?a-o*s:a,d=Pe(r,n,f*Math.cos(s*Ui),v);return{center:h,circleTangency:y,lineTangency:d,theta:s}},_v=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=j2(o,u),l=o+c,f=Pe(r,n,a,o),s=Pe(r,n,a,l),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>l),`,
    `).concat(s.x,",").concat(s.y,`
  `);if(i>0){var h=Pe(r,n,i,o),y=Pe(r,n,i,l);p+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=l),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},M2=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,f=t.endAngle,s=Ye(f-l),p=ci({cx:r,cy:n,radius:a,angle:l,sign:s,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,y=p.lineTangency,v=p.theta,d=ci({cx:r,cy:n,radius:a,angle:f,sign:-s,cornerRadius:o,cornerIsExternal:c}),w=d.circleTangency,b=d.lineTangency,x=d.theta,m=c?Math.abs(l-f):Math.abs(l-f)-v-x;if(m<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):_v({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:l,endAngle:f});var g="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(m>180),",").concat(+(s<0),",").concat(w.x,",").concat(w.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(b.x,",").concat(b.y,`
  `);if(i>0){var O=ci({cx:r,cy:n,radius:i,angle:l,sign:s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),S=O.circleTangency,_=O.lineTangency,T=O.theta,j=ci({cx:r,cy:n,radius:i,angle:f,sign:-s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=j.circleTangency,$=j.lineTangency,C=j.theta,M=c?Math.abs(l-f):Math.abs(l-f)-T-C;if(M<0&&o===0)return"".concat(g,"L").concat(r,",").concat(n,"Z");g+="L".concat($.x,",").concat($.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(S.x,",").concat(S.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(_.x,",").concat(_.y,"Z")}else g+="L".concat(r,",").concat(n,"Z");return g},C2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Av=function(t){var r=Df(Df({},C2),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,s=r.endAngle,p=r.className;if(o<a||f===s)return null;var h=re("recharts-sector",p),y=o-a,v=Ze(u,y,0,!0),d;return v>0&&Math.abs(f-s)<360?d=M2({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:l,startAngle:f,endAngle:s}):d=_v({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:s}),A.createElement("path",mu({},ee(r,!0),{className:h,d,role:"img"}))};function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function bu(){return bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bu.apply(this,arguments)}function Bf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bf(Object(r),!0).forEach(function(n){I2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function I2(e,t,r){return t=N2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N2(e){var t=k2(e,"string");return En(t)=="symbol"?t:t+""}function k2(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Lf={curveBasisClosed:Mx,curveBasisOpen:Cx,curveBasis:jx,curveBumpX:yx,curveBumpY:gx,curveLinearClosed:Ix,curveLinear:Ta,curveMonotoneX:Nx,curveMonotoneY:kx,curveNatural:Dx,curveStep:Bx,curveStepAfter:Lx,curveStepBefore:Rx},li=function(t){return t.x===+t.x&&t.y===+t.y},Zr=function(t){return t.x},Jr=function(t){return t.y},D2=function(t,r){if(Z(t))return t;var n="curve".concat(Pa(t));return(n==="curveMonotone"||n==="curveBump")&&r?Lf["".concat(n).concat(r==="vertical"?"Y":"X")]:Lf[n]||Ta},B2=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,l=c===void 0?!1:c,f=D2(n,u),s=l?a.filter(function(v){return li(v)}):a,p;if(Array.isArray(o)){var h=l?o.filter(function(v){return li(v)}):o,y=s.map(function(v,d){return Rf(Rf({},v),{},{base:h[d]})});return u==="vertical"?p=Qn().y(Jr).x1(Zr).x0(function(v){return v.base.x}):p=Qn().x(Zr).y1(Jr).y0(function(v){return v.base.y}),p.defined(li).curve(f),p(y)}return u==="vertical"&&R(o)?p=Qn().y(Jr).x1(Zr).x0(o):R(o)?p=Qn().x(Zr).y1(Jr).y0(o):p=wh().x(Zr).y(Jr),p.defined(li).curve(f),p(s)},Ff=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?B2(t):i;return A.createElement("path",bu({},ee(t,!1),hi(t),{className:re("recharts-curve",r),d:o,ref:a}))},Pv={exports:{}},R2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",L2=R2,F2=L2;function $v(){}function Tv(){}Tv.resetWarningCache=$v;var W2=function(){function e(n,i,a,o,u,c){if(c!==F2){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Tv,resetWarningCache:$v};return r.PropTypes=r,r};Pv.exports=W2();var z2=Pv.exports;const V=ue(z2);var U2=Object.getOwnPropertyNames,q2=Object.getOwnPropertySymbols,H2=Object.prototype.hasOwnProperty;function Wf(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function si(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function zf(e){return U2(e).concat(q2(e))}var Ev=Object.hasOwn||function(e,t){return H2.call(e,t)};function zr(e,t){return e||t?e===t:e===t||e!==e&&t!==t}var jv="_owner",Uf=Object.getOwnPropertyDescriptor,qf=Object.keys;function G2(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function K2(e,t){return zr(e.getTime(),t.getTime())}function Hf(e,t,r){if(e.size!==t.size)return!1;for(var n={},i=e.entries(),a=0,o,u;(o=i.next())&&!o.done;){for(var c=t.entries(),l=!1,f=0;(u=c.next())&&!u.done;){var s=o.value,p=s[0],h=s[1],y=u.value,v=y[0],d=y[1];!l&&!n[f]&&(l=r.equals(p,v,a,f,e,t,r)&&r.equals(h,d,p,v,e,t,r))&&(n[f]=!0),f++}if(!l)return!1;a++}return!0}function X2(e,t,r){var n=qf(e),i=n.length;if(qf(t).length!==i)return!1;for(var a;i-- >0;)if(a=n[i],a===jv&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof||!Ev(t,a)||!r.equals(e[a],t[a],a,a,e,t,r))return!1;return!0}function Qr(e,t,r){var n=zf(e),i=n.length;if(zf(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],a===jv&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof||!Ev(t,a)||!r.equals(e[a],t[a],a,a,e,t,r)||(o=Uf(e,a),u=Uf(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function V2(e,t){return zr(e.valueOf(),t.valueOf())}function Y2(e,t){return e.source===t.source&&e.flags===t.flags}function Gf(e,t,r){if(e.size!==t.size)return!1;for(var n={},i=e.values(),a,o;(a=i.next())&&!a.done;){for(var u=t.values(),c=!1,l=0;(o=u.next())&&!o.done;)!c&&!n[l]&&(c=r.equals(a.value,o.value,a.value,o.value,e,t,r))&&(n[l]=!0),l++;if(!c)return!1}return!0}function Z2(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}var J2="[object Arguments]",Q2="[object Boolean]",eN="[object Date]",tN="[object Map]",rN="[object Number]",nN="[object Object]",iN="[object RegExp]",aN="[object Set]",oN="[object String]",uN=Array.isArray,Kf=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Xf=Object.assign,cN=Object.prototype.toString.call.bind(Object.prototype.toString);function lN(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areMapsEqual,i=e.areObjectsEqual,a=e.arePrimitiveWrappersEqual,o=e.areRegExpsEqual,u=e.areSetsEqual,c=e.areTypedArraysEqual;return function(f,s,p){if(f===s)return!0;if(f==null||s==null||typeof f!="object"||typeof s!="object")return f!==f&&s!==s;var h=f.constructor;if(h!==s.constructor)return!1;if(h===Object)return i(f,s,p);if(uN(f))return t(f,s,p);if(Kf!=null&&Kf(f))return c(f,s,p);if(h===Date)return r(f,s,p);if(h===RegExp)return o(f,s,p);if(h===Map)return n(f,s,p);if(h===Set)return u(f,s,p);var y=cN(f);return y===eN?r(f,s,p):y===iN?o(f,s,p):y===tN?n(f,s,p):y===aN?u(f,s,p):y===nN?typeof f.then!="function"&&typeof s.then!="function"&&i(f,s,p):y===J2?i(f,s,p):y===Q2||y===rN||y===oN?a(f,s,p):!1}}function sN(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Qr:G2,areDatesEqual:K2,areMapsEqual:n?Wf(Hf,Qr):Hf,areObjectsEqual:n?Qr:X2,arePrimitiveWrappersEqual:V2,areRegExpsEqual:Y2,areSetsEqual:n?Wf(Gf,Qr):Gf,areTypedArraysEqual:n?Qr:Z2};if(r&&(i=Xf({},i,r(i))),t){var a=si(i.areArraysEqual),o=si(i.areMapsEqual),u=si(i.areObjectsEqual),c=si(i.areSetsEqual);i=Xf({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function fN(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function pN(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,l){var f=n(),s=f.cache,p=s===void 0?t?new WeakMap:void 0:s,h=f.meta;return r(c,l,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,l){return r(c,l,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,l){return r(c,l,o)}}var hN=Nt();Nt({strict:!0});Nt({circular:!0});Nt({circular:!0,strict:!0});Nt({createInternalComparator:function(){return zr}});Nt({strict:!0,createInternalComparator:function(){return zr}});Nt({circular:!0,createInternalComparator:function(){return zr}});Nt({circular:!0,createInternalComparator:function(){return zr},strict:!0});function Nt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=sN(e),c=lN(u),l=n?n(c):fN(c);return pN({circular:r,comparator:c,createState:i,equals:l,strict:o})}function dN(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Vf(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):dN(i)};requestAnimationFrame(n)}function xu(e){"@babel/helpers - typeof";return xu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xu(e)}function vN(e){return bN(e)||mN(e)||gN(e)||yN()}function yN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gN(e,t){if(e){if(typeof e=="string")return Yf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yf(e,t)}}function Yf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function mN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function bN(e){if(Array.isArray(e))return e}function xN(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=vN(o),c=u[0],l=u.slice(1);if(typeof c=="number"){Vf(i.bind(null,l),c);return}i(c),Vf(i.bind(null,l));return}xu(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function Zf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Jf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zf(Object(r),!0).forEach(function(n){Mv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mv(e,t,r){return t=wN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wN(e){var t=ON(e,"string");return jn(t)==="symbol"?t:String(t)}function ON(e,t){if(jn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(jn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var SN=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},_N=function(t){return t},AN=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},on=function(t,r){return Object.keys(r).reduce(function(n,i){return Jf(Jf({},n),{},Mv({},i,t(i,r[i])))},{})},Qf=function(t,r,n){return t.map(function(i){return"".concat(AN(i)," ").concat(r,"ms ").concat(n)}).join(",")};function PN(e,t){return EN(e)||TN(e,t)||Cv(e,t)||$N()}function $N(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function TN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function EN(e){if(Array.isArray(e))return e}function jN(e){return IN(e)||CN(e)||Cv(e)||MN()}function MN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cv(e,t){if(e){if(typeof e=="string")return wu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wu(e,t)}}function CN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function IN(e){if(Array.isArray(e))return wu(e)}function wu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Hi=1e-4,Iv=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Nv=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},ep=function(t,r){return function(n){var i=Iv(t,r);return Nv(i,n)}},NN=function(t,r){return function(n){var i=Iv(t,r),a=[].concat(jN(i.map(function(o,u){return o*u}).slice(1)),[0]);return Nv(a,n)}},tp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var l=c[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=PN(l,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var s=ep(i,o),p=ep(a,u),h=NN(i,o),y=function(w){return w>1?1:w<0?0:w},v=function(w){for(var b=w>1?1:w,x=b,m=0;m<8;++m){var g=s(x)-b,O=h(x);if(Math.abs(g-b)<Hi||O<Hi)return p(x);x=y(x-g/O)}return p(x)};return v.isStepper=!1,v},kN=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,s,p){var h=-(f-s)*n,y=p*a,v=p+(h-y)*u/1e3,d=p*u/1e3+f;return Math.abs(d-s)<Hi&&Math.abs(v)<Hi?[s,0]:[d,v]};return c.isStepper=!0,c.dt=u,c},DN=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return tp(i);case"spring":return kN();default:if(i.split("(")[0]==="cubic-bezier")return tp(i)}return typeof i=="function"?i:null};function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}function rp(e){return LN(e)||RN(e)||kv(e)||BN()}function BN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function RN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function LN(e){if(Array.isArray(e))return Su(e)}function np(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?np(Object(r),!0).forEach(function(n){Ou(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):np(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ou(e,t,r){return t=FN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FN(e){var t=WN(e,"string");return Mn(t)==="symbol"?t:String(t)}function WN(e,t){if(Mn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Mn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function zN(e,t){return HN(e)||qN(e,t)||kv(e,t)||UN()}function UN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kv(e,t){if(e){if(typeof e=="string")return Su(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Su(e,t)}}function Su(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function HN(e){if(Array.isArray(e))return e}var Gi=function(t,r,n){return t+(r-t)*n},_u=function(t){var r=t.from,n=t.to;return r!==n},GN=function e(t,r,n){var i=on(function(a,o){if(_u(o)){var u=t(o.from,o.to,o.velocity),c=zN(u,2),l=c[0],f=c[1];return Ae(Ae({},o),{},{from:l,velocity:f})}return o},r);return n<1?on(function(a,o){return _u(o)?Ae(Ae({},o),{},{velocity:Gi(o.velocity,i[a].velocity,n),from:Gi(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const KN=function(e,t,r,n,i){var a=SN(e,t),o=a.reduce(function(d,w){return Ae(Ae({},d),{},Ou({},w,[e[w],t[w]]))},{}),u=a.reduce(function(d,w){return Ae(Ae({},d),{},Ou({},w,{from:e[w],velocity:0,to:t[w]}))},{}),c=-1,l,f,s=function(){return null},p=function(){return on(function(w,b){return b.from},u)},h=function(){return!Object.values(u).filter(_u).length},y=function(w){l||(l=w);var b=w-l,x=b/r.dt;u=GN(r,u,x),i(Ae(Ae(Ae({},e),t),p())),l=w,h()||(c=requestAnimationFrame(s))},v=function(w){f||(f=w);var b=(w-f)/n,x=on(function(g,O){return Gi.apply(void 0,rp(O).concat([r(b)]))},o);if(i(Ae(Ae(Ae({},e),t),x)),b<1)c=requestAnimationFrame(s);else{var m=on(function(g,O){return Gi.apply(void 0,rp(O).concat([r(1)]))},o);i(Ae(Ae(Ae({},e),t),m))}};return s=r.isStepper?y:v,function(){return requestAnimationFrame(s),function(){cancelAnimationFrame(c)}}};function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}var XN=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function VN(e,t){if(e==null)return{};var r=YN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YN(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function wo(e){return ek(e)||QN(e)||JN(e)||ZN()}function ZN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function JN(e,t){if(e){if(typeof e=="string")return Au(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Au(e,t)}}function QN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ek(e){if(Array.isArray(e))return Au(e)}function Au(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ip(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ip(Object(r),!0).forEach(function(n){tn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ip(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tn(e,t,r){return t=Dv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Dv(n.key),n)}}function nk(e,t,r){return t&&rk(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Dv(e){var t=ik(e,"string");return br(t)==="symbol"?t:String(t)}function ik(e,t){if(br(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(br(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ak(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pu(e,t)}function Pu(e,t){return Pu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pu(e,t)}function ok(e){var t=uk();return function(){var n=Ki(e),i;if(t){var a=Ki(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return $u(this,i)}}function $u(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Tu(e)}function Tu(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function uk(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Ki(e){return Ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ki(e)}var Et=function(e){ak(r,e);var t=ok(r);function r(n,i){var a;tk(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,l=o.from,f=o.to,s=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Tu(a)),a.changeStyle=a.changeStyle.bind(Tu(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),$u(a);if(s&&s.length)a.state={style:s[0].style};else if(l){if(typeof p=="function")return a.state={style:l},$u(a);a.state={style:c?tn({},c,l):l}}else a.state={style:{}};return a}return nk(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,l=a.shouldReAnimate,f=a.to,s=a.from,p=this.state.style;if(u){if(!o){var h={style:c?tn({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(hN(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||l?s:i.to;if(this.state&&p){var d={style:c?tn({},c,v):v};(c&&p[c]!==v||!c&&p!==v)&&this.setState(d)}this.runAnimation(Ke(Ke({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,l=i.easing,f=i.begin,s=i.onAnimationEnd,p=i.onAnimationStart,h=KN(o,u,DN(l),c,this.changeStyle),y=function(){a.stopJSAnimation=h()};this.manager.start([p,f,y,c,s])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,l=o[0],f=l.style,s=l.duration,p=s===void 0?0:s,h=function(v,d,w){if(w===0)return v;var b=d.duration,x=d.easing,m=x===void 0?"ease":x,g=d.style,O=d.properties,S=d.onAnimationEnd,_=w>0?o[w-1]:d,T=O||Object.keys(g);if(typeof m=="function"||m==="spring")return[].concat(wo(v),[a.runJSAnimation.bind(a,{from:_.style,to:g,duration:b,easing:m}),b]);var j=Qf(T,b,m),P=Ke(Ke(Ke({},_.style),g),{},{transition:j});return[].concat(wo(v),[P,b,S]).filter(_N)};return this.manager.start([c].concat(wo(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=xN());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,l=i.easing,f=i.onAnimationStart,s=i.onAnimationEnd,p=i.steps,h=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof l=="function"||typeof h=="function"||l==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var v=u?tn({},u,c):c,d=Qf(Object.keys(v),o,l);y.start([f,a,Ke(Ke({},v),{},{transition:d}),o,s])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=VN(i,XN),l=B.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||l===0||o<=0)return a;var s=function(h){var y=h.props,v=y.style,d=v===void 0?{}:v,w=y.className,b=B.cloneElement(h,Ke(Ke({},c),{},{style:Ke(Ke({},d),f),className:w}));return b};return l===1?s(B.Children.only(a)):A.createElement("div",null,B.Children.map(a,function(p){return s(p)}))}}]),r}(B.PureComponent);Et.displayName="Animate";Et.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};Et.propTypes={from:V.oneOfType([V.object,V.string]),to:V.oneOfType([V.object,V.string]),attributeName:V.string,duration:V.number,begin:V.number,easing:V.oneOfType([V.string,V.func]),steps:V.arrayOf(V.shape({duration:V.number.isRequired,style:V.object.isRequired,easing:V.oneOfType([V.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),V.func]),properties:V.arrayOf("string"),onAnimationEnd:V.func})),children:V.oneOfType([V.node,V.func]),isActive:V.bool,canBegin:V.bool,onAnimationEnd:V.func,shouldReAnimate:V.bool,onAnimationStart:V.func,onAnimationReStart:V.func};V.object,V.object,V.object,V.element;V.object,V.object,V.object,V.oneOfType([V.array,V.element]),V.any;function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xi.apply(this,arguments)}function ck(e,t){return pk(e)||fk(e,t)||sk(e,t)||lk()}function lk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sk(e,t){if(e){if(typeof e=="string")return ap(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ap(e,t)}}function ap(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function fk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function pk(e){if(Array.isArray(e))return e}function op(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function up(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?op(Object(r),!0).forEach(function(n){hk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):op(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hk(e,t,r){return t=dk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dk(e){var t=vk(e,"string");return Cn(t)=="symbol"?t:t+""}function vk(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var cp=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,l=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var s=[0,0,0,0],p=0,h=4;p<h;p++)s[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*s[0]),s[0]>0&&(f+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(r)),f+="L ".concat(t+n-c*s[1],",").concat(r),s[1]>0&&(f+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,`,
        `).concat(t+n,",").concat(r+u*s[1])),f+="L ".concat(t+n,",").concat(r+i-u*s[2]),s[2]>0&&(f+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,`,
        `).concat(t+n-c*s[2],",").concat(r+i)),f+="L ".concat(t+c*s[3],",").concat(r+i),s[3]>0&&(f+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,`,
        `).concat(t,",").concat(r+i-u*s[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(l,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(l,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(l,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(l,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},yk=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var l=Math.min(a,a+u),f=Math.max(a,a+u),s=Math.min(o,o+c),p=Math.max(o,o+c);return n>=l&&n<=f&&i>=s&&i<=p}return!1},gk={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Kc=function(t){var r=up(up({},gk),t),n=B.useRef(),i=B.useState(-1),a=ck(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var m=n.current.getTotalLength();m&&u(m)}catch{}},[]);var c=r.x,l=r.y,f=r.width,s=r.height,p=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,w=r.isAnimationActive,b=r.isUpdateAnimationActive;if(c!==+c||l!==+l||f!==+f||s!==+s||f===0||s===0)return null;var x=re("recharts-rectangle",h);return b?A.createElement(Et,{canBegin:o>0,from:{width:f,height:s,x:c,y:l},to:{width:f,height:s,x:c,y:l},duration:v,animationEasing:y,isActive:b},function(m){var g=m.width,O=m.height,S=m.x,_=m.y;return A.createElement(Et,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:w,easing:y},A.createElement("path",Xi({},ee(r,!0),{className:x,d:cp(S,_,g,O,p),ref:n})))}):A.createElement("path",Xi({},ee(r,!0),{className:x,d:cp(c,l,f,s,p)}))};function Eu(){return Eu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Eu.apply(this,arguments)}var Bv=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=re("recharts-dot",a);return r===+r&&n===+n&&i===+i?A.createElement("circle",Eu({},ee(t,!1),hi(t),{className:o,cx:r,cy:n,r:i})):null};function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}var mk=["x","y","top","left","width","height","className"];function ju(){return ju=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ju.apply(this,arguments)}function lp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bk(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lp(Object(r),!0).forEach(function(n){xk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xk(e,t,r){return t=wk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wk(e){var t=Ok(e,"string");return In(t)=="symbol"?t:t+""}function Ok(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Sk(e,t){if(e==null)return{};var r=_k(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function _k(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Ak=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},Pk=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,l=c===void 0?0:c,f=t.width,s=f===void 0?0:f,p=t.height,h=p===void 0?0:p,y=t.className,v=Sk(t,mk),d=bk({x:n,y:a,top:u,left:l,width:s,height:h},v);return!R(n)||!R(a)||!R(s)||!R(h)||!R(u)||!R(l)?null:A.createElement("path",ju({},ee(d,!0),{className:re("recharts-cross",y),d:Ak(n,a,s,h,u,l)}))},$k=Hh,Tk=$k(Object.getPrototypeOf,Object),Ek=Tk,jk=wt,Mk=Ek,Ck=Ot,Ik="[object Object]",Nk=Function.prototype,kk=Object.prototype,Rv=Nk.toString,Dk=kk.hasOwnProperty,Bk=Rv.call(Object);function Rk(e){if(!Ck(e)||jk(e)!=Ik)return!1;var t=Mk(e);if(t===null)return!0;var r=Dk.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Rv.call(r)==Bk}var Lk=Rk;const Fk=ue(Lk);var Wk=wt,zk=Ot,Uk="[object Boolean]";function qk(e){return e===!0||e===!1||zk(e)&&Wk(e)==Uk}var Hk=qk;const Gk=ue(Hk);function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Vi(){return Vi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vi.apply(this,arguments)}function Kk(e,t){return Zk(e)||Yk(e,t)||Vk(e,t)||Xk()}function Xk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Vk(e,t){if(e){if(typeof e=="string")return sp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sp(e,t)}}function sp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Yk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Zk(e){if(Array.isArray(e))return e}function fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fp(Object(r),!0).forEach(function(n){Jk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Jk(e,t,r){return t=Qk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qk(e){var t=eD(e,"string");return Nn(t)=="symbol"?t:t+""}function eD(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hp=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},tD={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},rD=function(t){var r=pp(pp({},tD),t),n=B.useRef(),i=B.useState(-1),a=Kk(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,l=r.y,f=r.upperWidth,s=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,w=r.isUpdateAnimationActive;if(c!==+c||l!==+l||f!==+f||s!==+s||p!==+p||f===0&&s===0||p===0)return null;var b=re("recharts-trapezoid",h);return w?A.createElement(Et,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:f,lowerWidth:s,height:p,x:c,y:l},duration:v,animationEasing:y,isActive:w},function(x){var m=x.upperWidth,g=x.lowerWidth,O=x.height,S=x.x,_=x.y;return A.createElement(Et,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},A.createElement("path",Vi({},ee(r,!0),{className:b,d:hp(S,_,m,g,O),ref:n})))}):A.createElement("g",null,A.createElement("path",Vi({},ee(r,!0),{className:b,d:hp(c,l,f,s,p)})))},nD=["option","shapeType","propTransformer","activeClassName","isActive"];function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function iD(e,t){if(e==null)return{};var r=aD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function dp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dp(Object(r),!0).forEach(function(n){oD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oD(e,t,r){return t=uD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uD(e){var t=cD(e,"string");return kn(t)=="symbol"?t:t+""}function cD(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function lD(e,t){return Yi(Yi({},t),e)}function sD(e,t){return e==="symbols"}function vp(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return A.createElement(Kc,r);case"trapezoid":return A.createElement(rD,r);case"sector":return A.createElement(Av,r);case"symbols":if(sD(t))return A.createElement(fc,r);break;default:return null}}function fD(e){return B.isValidElement(e)?e.props:e}function pD(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?lD:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=iD(e,nD),l;if(B.isValidElement(t))l=B.cloneElement(t,Yi(Yi({},c),fD(t)));else if(Z(t))l=t(c);else if(Fk(t)&&!Gk(t)){var f=i(t,c);l=A.createElement(vp,{shapeType:r,elementProps:f})}else{var s=c;l=A.createElement(vp,{shapeType:r,elementProps:s})}return u?A.createElement(Oe,{className:o},l):l}function qa(e,t){return t!=null&&"trapezoids"in e.props}function Ha(e,t){return t!=null&&"sectors"in e.props}function Dn(e,t){return t!=null&&"points"in e.props}function hD(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function dD(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function vD(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function yD(e,t){var r;return qa(e,t)?r=hD:Ha(e,t)?r=dD:Dn(e,t)&&(r=vD),r}function gD(e,t){var r;return qa(e,t)?r="trapezoids":Ha(e,t)?r="sectors":Dn(e,t)&&(r="points"),r}function mD(e,t){if(qa(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Ha(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Dn(e,t)?t.payload:{}}function bD(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=gD(r,t),a=mD(r,t),o=n.filter(function(c,l){var f=Uc(a,c),s=r.props[i].filter(function(y){var v=yD(r,t);return v(y,t)}),p=r.props[i].indexOf(s[s.length-1]),h=l===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var xD=Math.ceil,wD=Math.max;function OD(e,t,r,n){for(var i=-1,a=wD(xD((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var SD=OD,_D=sd,yp=1/0,AD=17976931348623157e292;function PD(e){if(!e)return e===0?e:0;if(e=_D(e),e===yp||e===-yp){var t=e<0?-1:1;return t*AD}return e===e?e:0}var Lv=PD,$D=SD,TD=Ca,Oo=Lv;function ED(e){return function(t,r,n){return n&&typeof n!="number"&&TD(t,r,n)&&(r=n=void 0),t=Oo(t),r===void 0?(r=t,t=0):r=Oo(r),n=n===void 0?t<r?1:-1:Oo(n),$D(t,r,n,e)}}var jD=ED,MD=jD,CD=MD(),ID=CD;const Zi=ue(ID);function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function gp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gp(Object(r),!0).forEach(function(n){Fv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fv(e,t,r){return t=ND(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ND(e){var t=kD(e,"string");return Bn(t)=="symbol"?t:t+""}function kD(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var DD=["Webkit","Moz","O","ms"],BD=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=DD.reduce(function(a,o){return mp(mp({},a),{},Fv({},o+n,r))},{});return i[t]=r,i};function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function bp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function So(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bp(Object(r),!0).forEach(function(n){De(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zv(n.key),n)}}function LD(e,t,r){return t&&xp(e.prototype,t),r&&xp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function FD(e,t,r){return t=Qi(t),WD(e,Wv()?Reflect.construct(t,r||[],Qi(e).constructor):t.apply(e,r))}function WD(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zD(e)}function zD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Wv=function(){return!!e})()}function Qi(e){return Qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Qi(e)}function UD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mu(e,t)}function Mu(e,t){return Mu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mu(e,t)}function De(e,t,r){return t=zv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zv(e){var t=qD(e,"string");return xr(t)=="symbol"?t:t+""}function qD(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var HD=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,l=nn().domain(Zi(0,c)).range([a,a+o-u]),f=l.domain().map(function(s){return l(s)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(n),endX:l(i),scale:l,scaleValues:f}},wp=function(t){return t.changedTouches&&!!t.changedTouches.length},wr=function(e){function t(r){var n;return RD(this,t),n=FD(this,t,[r]),De(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),De(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),De(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),De(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),De(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),De(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),De(n,"handleSlideDragStart",function(i){var a=wp(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return UD(t,e),LD(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,l=u.data,f=l.length-1,s=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,s),y=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=it(a[n],u,n);return Z(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,l=c.x,f=c.width,s=c.travellerWidth,p=c.startIndex,h=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,l+f-s-u,l+f-s-o):v<0&&(v=Math.max(v,l-o,l-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==p||d.endIndex!==h)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=wp(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,l=this.state[o],f=this.props,s=f.x,p=f.width,h=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,w={startX:this.state.startX,endX:this.state.endX},b=n.pageX-a;b>0?b=Math.min(b,s+p-h-l):b<0&&(b=Math.max(b,s-l)),w[o]=l+b;var x=this.getIndex(w),m=x.startIndex,g=x.endIndex,O=function(){var _=d.length-1;return o==="startX"&&(u>c?m%v===0:g%v===0)||u<c&&g===_||o==="endX"&&(u>c?g%v===0:m%v===0)||u>c&&g===_};this.setState(De(De({},o,l+b),"brushMoveStartX",n.pageX),function(){y&&O()&&y(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,l=o.endX,f=this.state[i],s=u.indexOf(f);if(s!==-1){var p=s+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=l||i==="endX"&&h<=c||this.setState(De({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,l=n.stroke;return A.createElement("rect",{stroke:l,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,l=n.children,f=n.padding,s=B.Children.only(l);return s?A.cloneElement(s,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,l=c.y,f=c.travellerWidth,s=c.height,p=c.traveller,h=c.ariaLabel,y=c.data,v=c.startIndex,d=c.endIndex,w=Math.max(n,this.props.x),b=So(So({},ee(this.props,!1)),{},{x:w,y:l,width:f,height:s}),x=h||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return A.createElement(Oe,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(g){["ArrowLeft","ArrowRight"].includes(g.key)&&(g.preventDefault(),g.stopPropagation(),u.handleTravellerMoveKeyboard(g.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,b))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,l=a.travellerWidth,f=Math.min(n,i)+l,s=Math.max(Math.abs(i-n)-l,0);return A.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:s,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,l=n.stroke,f=this.state,s=f.startX,p=f.endX,h=5,y={pointerEvents:"none",fill:l};return A.createElement(Oe,{className:"recharts-brush-texts"},A.createElement(Ti,Ji({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,p)-h,y:o+u/2},y),this.getTextOfTick(i)),A.createElement(Ti,Ji({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,p)+c+h,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,l=n.width,f=n.height,s=n.alwaysShowText,p=this.state,h=p.startX,y=p.endX,v=p.isTextActive,d=p.isSlideMoving,w=p.isTravellerMoving,b=p.isTravellerFocused;if(!i||!i.length||!R(u)||!R(c)||!R(l)||!R(f)||l<=0||f<=0)return null;var x=re("recharts-brush",a),m=A.Children.count(o)===1,g=BD("userSelect","none");return A.createElement(Oe,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),m&&this.renderPanorama(),this.renderSlide(h,y),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||w||b||s)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,l=Math.floor(a+u/2)-1;return A.createElement(A.Fragment,null,A.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),A.createElement("line",{x1:i+1,y1:l,x2:i+o-1,y2:l,fill:"none",stroke:"#fff"}),A.createElement("line",{x1:i+1,y1:l+2,x2:i+o-1,y2:l+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return A.isValidElement(n)?a=A.cloneElement(n,i):Z(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,l=n.updateId,f=n.startIndex,s=n.endIndex;if(a!==i.prevData||l!==i.prevUpdateId)return So({prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o},a&&a.length?HD({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:s}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(B.PureComponent);De(wr,"displayName","Brush");De(wr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var GD=mc;function KD(e,t){var r;return GD(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var XD=KD,VD=Bh,YD=Mt,ZD=XD,JD=Ne,QD=Ca;function eB(e,t,r){var n=JD(e)?VD:ZD;return r&&QD(e,t,r)&&(t=void 0),n(e,YD(t))}var tB=eB;const rB=ue(tB);var rt=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Op=ad;function nB(e,t,r){t=="__proto__"&&Op?Op(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var iB=nB,aB=iB,oB=nd,uB=Mt;function cB(e,t){var r={};return t=uB(t),oB(e,function(n,i,a){aB(r,i,t(n,i,a))}),r}var lB=cB;const sB=ue(lB);function fB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var pB=fB,hB=mc;function dB(e,t){var r=!0;return hB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var vB=dB,yB=pB,gB=vB,mB=Mt,bB=Ne,xB=Ca;function wB(e,t,r){var n=bB(e)?yB:gB;return r&&xB(e,t,r)&&(t=void 0),n(e,mB(t))}var OB=wB;const Uv=ue(OB);var SB=["x","y"];function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Cu(){return Cu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cu.apply(this,arguments)}function Sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function en(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sp(Object(r),!0).forEach(function(n){_B(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _B(e,t,r){return t=AB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AB(e){var t=PB(e,"string");return Rn(t)=="symbol"?t:t+""}function PB(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $B(e,t){if(e==null)return{};var r=TB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function TB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function EB(e,t){var r=e.x,n=e.y,i=$B(e,SB),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),l="".concat(t.height||i.height),f=parseInt(l,10),s="".concat(t.width||i.width),p=parseInt(s,10);return en(en(en(en(en({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function _p(e){return A.createElement(pD,Cu({shapeType:"rectangle",propTransformer:EB,activeClassName:"recharts-active-bar"},e))}var jB=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Vt(),r)}},MB=["value","background"],qv;function Or(e){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(e)}function CB(e,t){if(e==null)return{};var r=IB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function IB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ea(){return ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ea.apply(this,arguments)}function Ap(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ap(Object(r),!0).forEach(function(n){Pt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ap(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function NB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Gv(n.key),n)}}function kB(e,t,r){return t&&Pp(e.prototype,t),r&&Pp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function DB(e,t,r){return t=ta(t),BB(e,Hv()?Reflect.construct(t,r||[],ta(e).constructor):t.apply(e,r))}function BB(e,t){if(t&&(Or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return RB(e)}function RB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Hv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hv=function(){return!!e})()}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}function LB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Iu(e,t)}function Iu(e,t){return Iu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Iu(e,t)}function Pt(e,t,r){return t=Gv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gv(e){var t=FB(e,"string");return Or(t)=="symbol"?t:t+""}function FB(e,t){if(Or(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Or(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ur=function(e){function t(){var r;NB(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=DB(this,t,[].concat(i)),Pt(r,"state",{isAnimationFinished:!1}),Pt(r,"id",Aa("recharts-bar-")),Pt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Pt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return LB(t,e),kB(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,l=a.activeBar,f=ee(this.props,!1);return n&&n.map(function(s,p){var h=p===c,y=h?l:o,v=ve(ve(ve({},f),s),{},{isActive:h,option:y,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return A.createElement(Oe,ea({className:"recharts-bar-rectangle"},di(i.props,s,p),{key:"rectangle-".concat(s==null?void 0:s.x,"-").concat(s==null?void 0:s.y,"-").concat(s==null?void 0:s.value)}),A.createElement(_p,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,f=i.animationEasing,s=i.animationId,p=this.state.prevData;return A.createElement(Et,{begin:c,duration:l,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var y=h.t,v=a.map(function(d,w){var b=p&&p[w];if(b){var x=er(b.x,d.x),m=er(b.y,d.y),g=er(b.width,d.width),O=er(b.height,d.height);return ve(ve({},d),{},{x:x(y),y:m(y),width:g(y),height:O(y)})}if(o==="horizontal"){var S=er(0,d.height),_=S(y);return ve(ve({},d),{},{y:d.y+d.height-_,height:_})}var T=er(0,d.width),j=T(y);return ve(ve({},d),{},{width:j})});return A.createElement(Oe,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Uc(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=ee(this.props.background,!1);return a.map(function(l,f){l.value;var s=l.background,p=CB(l,MB);if(!s)return null;var h=ve(ve(ve(ve(ve({},p),{},{fill:"#eee"},s),c),di(n.props,l,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return A.createElement(_p,ea({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,l=a.layout,f=a.children,s=Je(f,Ua);if(!s)return null;var p=l==="vertical"?o[0].height/2:o[0].width/2,h=function(d,w){var b=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:b,errorVal:it(d,w)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(Oe,y,s.map(function(v){return A.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:l,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,l=n.left,f=n.top,s=n.width,p=n.height,h=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,w=re("recharts-bar",o),b=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,m=b||x,g=te(v)?this.id:v;return A.createElement(Oe,{className:w},b||x?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(g)},A.createElement("rect",{x:b?l:l-s/2,y:x?f:f-p/2,width:b?s:s*2,height:x?p:p*2}))):null,A.createElement(Oe,{className:"recharts-bar-rectangles",clipPath:m?"url(#clipPath-".concat(g,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(m,g),(!h||d)&&Kt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(B.PureComponent);qv=Ur;Pt(Ur,"displayName","Bar");Pt(Ur,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!$t.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Pt(Ur,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,f=e.dataStartIndex,s=e.displayedData,p=e.offset,h=OI(n,r);if(!h)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?ve(ve({},v),r.props):r.props,w=d.dataKey,b=d.children,x=d.minPointSize,m=y==="horizontal"?o:a,g=l?m.scale.domain():null,O=EI({numericAxis:m}),S=Je(b,pd),_=s.map(function(T,j){var P,$,C,M,N,k;l?P=SI(l[f+j],g):(P=it(T,w),Array.isArray(P)||(P=[O,P]));var D=jB(x,qv.defaultProps.minPointSize)(P[1],j);if(y==="horizontal"){var L,z=[o.scale(P[0]),o.scale(P[1])],G=z[0],q=z[1];$=_f({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:T,index:j}),C=(L=q??G)!==null&&L!==void 0?L:void 0,M=h.size;var U=G-q;if(N=Number.isNaN(U)?0:U,k={x:$,y:o.y,width:M,height:o.height},Math.abs(D)>0&&Math.abs(N)<Math.abs(D)){var K=Ye(N||D)*(Math.abs(D)-Math.abs(N));C-=K,N+=K}}else{var ce=[a.scale(P[0]),a.scale(P[1])],_e=ce[0],ot=ce[1];if($=_e,C=_f({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:T,index:j}),M=ot-_e,N=h.size,k={x:a.x,y:C,width:a.width,height:N},Math.abs(D)>0&&Math.abs(M)<Math.abs(D)){var $e=Ye(M||D)*(Math.abs(D)-Math.abs(M));M+=$e}}return ve(ve(ve({},T),{},{x:$,y:C,width:M,height:N,value:l?P:P[1],payload:T,background:k},S&&S[j]&&S[j].props),{},{tooltipPayload:[wv(r,T)],tooltipPosition:{x:$+M/2,y:C+N/2}})});return ve({data:_,layout:y},p)});function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function WB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kv(n.key),n)}}function zB(e,t,r){return t&&$p(e.prototype,t),r&&$p(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Tp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tp(Object(r),!0).forEach(function(n){Ga(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ga(e,t,r){return t=Kv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kv(e){var t=UB(e,"string");return Ln(t)=="symbol"?t:t+""}function UB(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qB=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,l=t.children,f=Object.keys(r),s={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!Be(l,Ur);return f.reduce(function(h,y){var v=r[y],d=v.orientation,w=v.domain,b=v.padding,x=b===void 0?{}:b,m=v.mirror,g=v.reversed,O="".concat(d).concat(m?"Mirror":""),S,_,T,j,P;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var $=w[1]-w[0],C=1/0,M=v.categoricalDomain.sort();if(M.forEach(function(ce,_e){_e>0&&(C=Math.min((ce||0)-(M[_e-1]||0),C))}),Number.isFinite(C)){var N=C/$,k=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(S=N*k/2),v.padding==="no-gap"){var D=Ze(t.barCategoryGap,N*k),L=N*k/2;S=L-D-(L-D)/k*D}}}i==="xAxis"?_=[n.left+(x.left||0)+(S||0),n.left+n.width-(x.right||0)-(S||0)]:i==="yAxis"?_=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(S||0),n.top+n.height-(x.bottom||0)-(S||0)]:_=v.range,g&&(_=[_[1],_[0]]);var z=gv(v,a,p),G=z.scale,q=z.realScaleType;G.domain(w).range(_),mv(G);var U=bv(G,Xe(Xe({},v),{},{realScaleType:q}));i==="xAxis"?(P=d==="top"&&!m||d==="bottom"&&m,T=n.left,j=s[O]-P*v.height):i==="yAxis"&&(P=d==="left"&&!m||d==="right"&&m,T=s[O]-P*v.width,j=n.top);var K=Xe(Xe(Xe({},v),U),{},{realScaleType:q,x:T,y:j,scale:G,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return K.bandSize=zi(K,U),!v.hide&&i==="xAxis"?s[O]+=(P?-1:1)*K.height:v.hide||(s[O]+=(P?-1:1)*K.width),Xe(Xe({},h),{},Ga({},y,K))},{})},Xv=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},HB=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Xv({x:r,y:n},{x:i,y:a})},Vv=function(){function e(t){WB(this,e),this.scale=t}return zB(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Ga(Vv,"EPS",1e-4);var Xc=function(t){var r=Object.keys(t).reduce(function(n,i){return Xe(Xe({},n),{},Ga({},i,Vv.create(t[i])))},{});return Xe(Xe({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return sB(i,function(c,l){return r[l].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Uv(i,function(a,o){return r[o].isInRange(a)})}})};function GB(e){return(e%180+180)%180}var KB=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=GB(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},XB=Mt,VB=Gn,YB=ja;function ZB(e){return function(t,r,n){var i=Object(t);if(!VB(t)){var a=XB(r);t=YB(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var JB=ZB,QB=Lv;function eR(e){var t=QB(e),r=t%1;return t===t?r?t-r:t:0}var tR=eR,rR=Zh,nR=Mt,iR=tR,aR=Math.max;function oR(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:iR(r);return i<0&&(i=aR(n+i,0)),rR(e,nR(t),i)}var uR=oR,cR=JB,lR=uR,sR=cR(lR),fR=sR;const pR=ue(fR);var hR=lb(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Vc=B.createContext(void 0),Yc=B.createContext(void 0),Yv=B.createContext(void 0),Zv=B.createContext({}),Jv=B.createContext(void 0),Qv=B.createContext(0),ey=B.createContext(0),Ep=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,l=t.height,f=hR(a);return A.createElement(Vc.Provider,{value:n},A.createElement(Yc.Provider,{value:i},A.createElement(Zv.Provider,{value:a},A.createElement(Yv.Provider,{value:f},A.createElement(Jv.Provider,{value:o},A.createElement(Qv.Provider,{value:l},A.createElement(ey.Provider,{value:c},u)))))))},dR=function(){return B.useContext(Jv)},ty=function(t){var r=B.useContext(Vc);r==null&&Vt();var n=r[t];return n==null&&Vt(),n},vR=function(){var t=B.useContext(Vc);return At(t)},yR=function(){var t=B.useContext(Yc),r=pR(t,function(n){return Uv(n.domain,Number.isFinite)});return r||At(t)},ry=function(t){var r=B.useContext(Yc);r==null&&Vt();var n=r[t];return n==null&&Vt(),n},gR=function(){var t=B.useContext(Yv);return t},mR=function(){return B.useContext(Zv)},Zc=function(){return B.useContext(ey)},Jc=function(){return B.useContext(Qv)};function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function bR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,iy(n.key),n)}}function wR(e,t,r){return t&&xR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function OR(e,t,r){return t=ra(t),SR(e,ny()?Reflect.construct(t,r||[],ra(e).constructor):t.apply(e,r))}function SR(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _R(e)}function _R(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ny(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ny=function(){return!!e})()}function ra(e){return ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ra(e)}function AR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nu(e,t)}function Nu(e,t){return Nu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Nu(e,t)}function jp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jp(Object(r),!0).forEach(function(n){Qc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Qc(e,t,r){return t=iy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iy(e){var t=PR(e,"string");return Sr(t)=="symbol"?t:t+""}function PR(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $R(e,t){return MR(e)||jR(e,t)||ER(e,t)||TR()}function TR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ER(e,t){if(e){if(typeof e=="string")return Cp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cp(e,t)}}function Cp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jR(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function MR(e){if(Array.isArray(e))return e}function ku(){return ku=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ku.apply(this,arguments)}var CR=function(t,r){var n;return A.isValidElement(t)?n=A.cloneElement(t,r):Z(t)?n=t(r):n=A.createElement("line",ku({},r,{className:"recharts-reference-line-line"})),n},IR=function(t,r,n,i,a,o,u,c,l){var f=a.x,s=a.y,p=a.width,h=a.height;if(n){var y=l.y,v=t.y.apply(y,{position:o});if(rt(l,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+p,y:v},{x:f,y:v}];return c==="left"?d.reverse():d}if(r){var w=l.x,b=t.x.apply(w,{position:o});if(rt(l,"discard")&&!t.x.isInRange(b))return null;var x=[{x:b,y:s+h},{x:b,y:s}];return u==="top"?x.reverse():x}if(i){var m=l.segment,g=m.map(function(O){return t.apply(O,{position:o})});return rt(l,"discard")&&rB(g,function(O){return!t.isInRange(O)})?null:g}return null};function NR(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,l=dR(),f=ty(i),s=ry(a),p=gR();if(!l||!p)return null;yt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=Xc({x:f.scale,y:s.scale}),y=be(t),v=be(r),d=n&&n.length===2,w=IR(h,y,v,d,p,e.position,f.orientation,s.orientation,e);if(!w)return null;var b=$R(w,2),x=b[0],m=x.x,g=x.y,O=b[1],S=O.x,_=O.y,T=rt(e,"hidden")?"url(#".concat(l,")"):void 0,j=Mp(Mp({clipPath:T},ee(e,!0)),{},{x1:m,y1:g,x2:S,y2:_});return A.createElement(Oe,{className:re("recharts-reference-line",u)},CR(o,j),Ee.renderCallByParent(e,HB({x1:m,y1:g,x2:S,y2:_})))}var el=function(e){function t(){return bR(this,t),OR(this,t,arguments)}return AR(t,e),wR(t,[{key:"render",value:function(){return A.createElement(NR,this.props)}}])}(A.Component);Qc(el,"displayName","ReferenceLine");Qc(el,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Du(){return Du=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Du.apply(this,arguments)}function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function Ip(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Np(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ip(Object(r),!0).forEach(function(n){Ka(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ip(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function DR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,oy(n.key),n)}}function BR(e,t,r){return t&&DR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function RR(e,t,r){return t=na(t),LR(e,ay()?Reflect.construct(t,r||[],na(e).constructor):t.apply(e,r))}function LR(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return FR(e)}function FR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ay(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ay=function(){return!!e})()}function na(e){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},na(e)}function WR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bu(e,t)}function Bu(e,t){return Bu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bu(e,t)}function Ka(e,t,r){return t=oy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oy(e){var t=zR(e,"string");return _r(t)=="symbol"?t:t+""}function zR(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var UR=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Xc({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return rt(t,"discard")&&!o.isInRange(u)?null:u},Xa=function(e){function t(){return kR(this,t),RR(this,t,arguments)}return WR(t,e),BR(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,l=be(i),f=be(a);if(yt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!f)return null;var s=UR(this.props);if(!s)return null;var p=s.x,h=s.y,y=this.props,v=y.shape,d=y.className,w=rt(this.props,"hidden")?"url(#".concat(c,")"):void 0,b=Np(Np({clipPath:w},ee(this.props,!0)),{},{cx:p,cy:h});return A.createElement(Oe,{className:re("recharts-reference-dot",d)},t.renderDot(v,b),Ee.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(A.Component);Ka(Xa,"displayName","ReferenceDot");Ka(Xa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Ka(Xa,"renderDot",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):Z(e)?r=e(t):r=A.createElement(Bv,Du({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Ru(){return Ru=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ru.apply(this,arguments)}function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}function kp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Dp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kp(Object(r),!0).forEach(function(n){Va(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function HR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cy(n.key),n)}}function GR(e,t,r){return t&&HR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function KR(e,t,r){return t=ia(t),XR(e,uy()?Reflect.construct(t,r||[],ia(e).constructor):t.apply(e,r))}function XR(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VR(e)}function VR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function uy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(uy=function(){return!!e})()}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function YR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Lu(e,t)}function Lu(e,t){return Lu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Lu(e,t)}function Va(e,t,r){return t=cy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cy(e){var t=ZR(e,"string");return Ar(t)=="symbol"?t:t+""}function ZR(e,t){if(Ar(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ar(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var JR=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,l=a.y2,f=a.xAxis,s=a.yAxis;if(!f||!s)return null;var p=Xc({x:f.scale,y:s.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},y={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(l,{position:"end"}):p.y.rangeMax};return rt(a,"discard")&&(!p.isInRange(h)||!p.isInRange(y))?null:Xv(h,y)},Ya=function(e){function t(){return qR(this,t),KR(this,t,arguments)}return YR(t,e),GR(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,l=n.alwaysShow,f=n.clipPathId;yt(l===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=be(i),p=be(a),h=be(o),y=be(u),v=this.props.shape;if(!s&&!p&&!h&&!y&&!v)return null;var d=JR(s,p,h,y,this.props);if(!d&&!v)return null;var w=rt(this.props,"hidden")?"url(#".concat(f,")"):void 0;return A.createElement(Oe,{className:re("recharts-reference-area",c)},t.renderRect(v,Dp(Dp({clipPath:w},ee(this.props,!0)),d)),Ee.renderCallByParent(this.props,d))}}])}(A.Component);Va(Ya,"displayName","ReferenceArea");Va(Ya,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Va(Ya,"renderRect",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):Z(e)?r=e(t):r=A.createElement(Kc,Ru({},t,{className:"recharts-reference-area-rect"})),r});function ly(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function QR(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return KB(n,r)}function eL(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function aa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function tL(e,t){return ly(e,t+1)}function rL(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,l=1,f=o,s=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:ly(n,l)};var v=c,d,w=function(){return d===void 0&&(d=r(y,v)),d},b=y.coordinate,x=c===0||aa(e,b,w,f,u);x||(c=0,f=o,l+=1),x&&(f=b+e*(w()/2+i),c+=l)},p;l<=a.length;)if(p=s(),p)return p.v;return[]}function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Bp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bp(Object(r),!0).forEach(function(n){nL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nL(e,t,r){return t=iL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iL(e){var t=aL(e,"string");return Fn(t)=="symbol"?t:t+""}function aL(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function oL(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,l=function(p){var h=a[p],y,v=function(){return y===void 0&&(y=r(h,p)),y};if(p===o-1){var d=e*(h.coordinate+e*v()/2-c);a[p]=h=Te(Te({},h),{},{tickCoord:d>0?h.coordinate-d*e:h.coordinate})}else a[p]=h=Te(Te({},h),{},{tickCoord:h.coordinate});var w=aa(e,h.tickCoord,v,u,c);w&&(c=h.tickCoord-e*(v()/2+i),a[p]=Te(Te({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)l(f);return a}function uL(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,l=t.end;if(a){var f=n[u-1],s=r(f,u-1),p=e*(f.coordinate+e*s/2-l);o[u-1]=f=Te(Te({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=aa(e,f.tickCoord,function(){return s},c,l);h&&(l=f.tickCoord-e*(s/2+i),o[u-1]=Te(Te({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(b){var x=o[b],m,g=function(){return m===void 0&&(m=r(x,b)),m};if(b===0){var O=e*(x.coordinate-e*g()/2-c);o[b]=x=Te(Te({},x),{},{tickCoord:O<0?x.coordinate-O*e:x.coordinate})}else o[b]=x=Te(Te({},x),{},{tickCoord:x.coordinate});var S=aa(e,x.tickCoord,g,c,l);S&&(c=x.tickCoord+e*(g()/2+i),o[b]=Te(Te({},x),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function tl(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,l=e.tickFormatter,f=e.unit,s=e.angle;if(!i||!i.length||!n)return[];if(R(c)||$t.isSsr)return tL(i,typeof c=="number"&&R(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",y=f&&h==="width"?rn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(x,m){var g=Z(l)?l(x.value,m):x.value;return h==="width"?QR(rn(g,{fontSize:t,letterSpacing:r}),y,s):rn(g,{fontSize:t,letterSpacing:r})[h]},d=i.length>=2?Ye(i[1].coordinate-i[0].coordinate):1,w=eL(a,d,h);return c==="equidistantPreserveStart"?rL(d,w,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=uL(d,w,v,i,o,c==="preserveStartEnd"):p=oL(d,w,v,i,o),p.filter(function(b){return b.isShow}))}var cL=["viewBox"],lL=["viewBox"],sL=["ticks"];function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}function ar(){return ar=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ar.apply(this,arguments)}function Rp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rp(Object(r),!0).forEach(function(n){rl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _o(e,t){if(e==null)return{};var r=fL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function pL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fy(n.key),n)}}function hL(e,t,r){return t&&Lp(e.prototype,t),r&&Lp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function dL(e,t,r){return t=oa(t),vL(e,sy()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function vL(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return yL(e)}function yL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function sy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(sy=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function gL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fu(e,t)}function Fu(e,t){return Fu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Fu(e,t)}function rl(e,t,r){return t=fy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fy(e){var t=mL(e,"string");return Pr(t)=="symbol"?t:t+""}function mL(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qr=function(e){function t(r){var n;return pL(this,t),n=dL(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return gL(t,e),hL(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=_o(n,cL),u=this.props,c=u.viewBox,l=_o(u,lL);return!or(a,c)||!or(o,l)||!or(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,l=i.orientation,f=i.tickSize,s=i.mirror,p=i.tickMargin,h,y,v,d,w,b,x=s?-1:1,m=n.tickSize||f,g=R(n.tickCoord)?n.tickCoord:n.coordinate;switch(l){case"top":h=y=n.coordinate,d=o+ +!s*c,v=d-x*m,b=v-x*p,w=g;break;case"left":v=d=n.coordinate,y=a+ +!s*u,h=y-x*m,w=h-x*p,b=g;break;case"right":v=d=n.coordinate,y=a+ +s*u,h=y+x*m,w=h+x*p,b=g;break;default:h=y=n.coordinate,d=o+ +s*c,v=d+x*m,b=v+x*p,w=g;break}return{line:{x1:h,y1:v,x2:y,y2:d},tick:{x:w,y:b}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,l=n.mirror,f=n.axisLine,s=Me(Me(Me({},ee(this.props,!1)),ee(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!l||c==="bottom"&&l);s=Me(Me({},s),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!l||c==="right"&&l);s=Me(Me({},s),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return A.createElement("line",ar({},s,{className:re("recharts-cartesian-axis-line",Ue(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,l=u.stroke,f=u.tick,s=u.tickFormatter,p=u.unit,h=tl(Me(Me({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=ee(this.props,!1),w=ee(f,!1),b=Me(Me({},d),{},{fill:"none"},ee(c,!1)),x=h.map(function(m,g){var O=o.getTickLineCoord(m),S=O.line,_=O.tick,T=Me(Me(Me(Me({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:l},w),_),{},{index:g,payload:m,visibleTicksCount:h.length,tickFormatter:s});return A.createElement(Oe,ar({className:"recharts-cartesian-axis-tick",key:"tick-".concat(m.value,"-").concat(m.coordinate,"-").concat(m.tickCoord)},di(o.props,m,g)),c&&A.createElement("line",ar({},b,S,{className:re("recharts-cartesian-axis-tick-line",Ue(c,"className"))})),f&&t.renderTickItem(f,T,"".concat(Z(s)?s(m.value,g):m.value).concat(p||"")))});return A.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,l=i.className,f=i.hide;if(f)return null;var s=this.props,p=s.ticks,h=_o(s,sL),y=p;return Z(c)&&(y=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!y||!y.length?null:A.createElement(Oe,{className:re("recharts-cartesian-axis",l),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Ee.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):Z(n)?o=n(i):o=A.createElement(Ti,ar({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(B.Component);rl(qr,"displayName","CartesianAxis");rl(qr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var bL=["x1","y1","x2","y2","key"],xL=["offset"];function Yt(e){"@babel/helpers - typeof";return Yt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yt(e)}function Fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fp(Object(r),!0).forEach(function(n){wL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wL(e,t,r){return t=OL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function OL(e){var t=SL(e,"string");return Yt(t)=="symbol"?t:t+""}function SL(e,t){if(Yt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Yt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qt(){return qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qt.apply(this,arguments)}function Wp(e,t){if(e==null)return{};var r=_L(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function _L(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var AL=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return A.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function py(e,t){var r;if(A.isValidElement(e))r=A.cloneElement(e,t);else if(Z(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Wp(t,bL),l=ee(c,!1);l.offset;var f=Wp(l,xL);r=A.createElement("line",qt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function PL(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=je(je({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return py(i,l)});return A.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function $L(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=je(je({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return py(i,l)});return A.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function TL(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,l=c===void 0?!0:c;if(!l||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var s=f.map(function(p,h){var y=!f[h+1],v=y?i+o-p:f[h+1]-p;if(v<=0)return null;var d=h%t.length;return A.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function EL(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,l=e.verticalPoints;if(!r||!n||!n.length)return null;var f=l.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var s=f.map(function(p,h){var y=!f[h+1],v=y?a+u-p:f[h+1]-p;if(v<=0)return null;var d=h%n.length;return A.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:v,height:c,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var jL=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return yv(tl(je(je(je({},qr.defaultProps),n),{},{ticks:dt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},ML=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return yv(tl(je(je(je({},qr.defaultProps),n),{},{ticks:dt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},nr={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function CL(e){var t,r,n,i,a,o,u=Zc(),c=Jc(),l=mR(),f=je(je({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:nr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:nr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:nr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:nr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:nr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:nr.verticalFill,x:R(e.x)?e.x:l.left,y:R(e.y)?e.y:l.top,width:R(e.width)?e.width:l.width,height:R(e.height)?e.height:l.height}),s=f.x,p=f.y,h=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,w=f.verticalValues,b=vR(),x=yR();if(!R(h)||h<=0||!R(y)||y<=0||!R(s)||s!==+s||!R(p)||p!==+p)return null;var m=f.verticalCoordinatesGenerator||jL,g=f.horizontalCoordinatesGenerator||ML,O=f.horizontalPoints,S=f.verticalPoints;if((!O||!O.length)&&Z(g)){var _=d&&d.length,T=g({yAxis:x?je(je({},x),{},{ticks:_?d:x.ticks}):void 0,width:u,height:c,offset:l},_?!0:v);yt(Array.isArray(T),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Yt(T),"]")),Array.isArray(T)&&(O=T)}if((!S||!S.length)&&Z(m)){var j=w&&w.length,P=m({xAxis:b?je(je({},b),{},{ticks:j?w:b.ticks}):void 0,width:u,height:c,offset:l},j?!0:v);yt(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Yt(P),"]")),Array.isArray(P)&&(S=P)}return A.createElement("g",{className:"recharts-cartesian-grid"},A.createElement(AL,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),A.createElement(PL,qt({},f,{offset:l,horizontalPoints:O,xAxis:b,yAxis:x})),A.createElement($L,qt({},f,{offset:l,verticalPoints:S,xAxis:b,yAxis:x})),A.createElement(TL,qt({},f,{horizontalPoints:O})),A.createElement(EL,qt({},f,{verticalPoints:S})))}CL.displayName="CartesianGrid";function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function IL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function NL(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vy(n.key),n)}}function kL(e,t,r){return t&&NL(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function DL(e,t,r){return t=ua(t),BL(e,hy()?Reflect.construct(t,r||[],ua(e).constructor):t.apply(e,r))}function BL(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return RL(e)}function RL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hy=function(){return!!e})()}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function LL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wu(e,t)}function Wu(e,t){return Wu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Wu(e,t)}function dy(e,t,r){return t=vy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vy(e){var t=FL(e,"string");return $r(t)=="symbol"?t:t+""}function FL(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function zu(){return zu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zu.apply(this,arguments)}function WL(e){var t=e.xAxisId,r=Zc(),n=Jc(),i=ty(t);return i==null?null:A.createElement(qr,zu({},i,{className:re("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return dt(o,!0)}}))}var nl=function(e){function t(){return IL(this,t),DL(this,t,arguments)}return LL(t,e),kL(t,[{key:"render",value:function(){return A.createElement(WL,this.props)}}])}(A.Component);dy(nl,"displayName","XAxis");dy(nl,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function zL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function UL(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,my(n.key),n)}}function qL(e,t,r){return t&&UL(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function HL(e,t,r){return t=ca(t),GL(e,yy()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function GL(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return KL(e)}function KL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(yy=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function XL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uu(e,t)}function Uu(e,t){return Uu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uu(e,t)}function gy(e,t,r){return t=my(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function my(e){var t=VL(e,"string");return Tr(t)=="symbol"?t:t+""}function VL(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qu(){return qu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qu.apply(this,arguments)}var YL=function(t){var r=t.yAxisId,n=Zc(),i=Jc(),a=ry(r);return a==null?null:A.createElement(qr,qu({},a,{className:re("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return dt(u,!0)}}))},il=function(e){function t(){return zL(this,t),HL(this,t,arguments)}return XL(t,e),qL(t,[{key:"render",value:function(){return A.createElement(YL,this.props)}}])}(A.Component);gy(il,"displayName","YAxis");gy(il,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function zp(e){return eF(e)||QL(e)||JL(e)||ZL()}function ZL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function JL(e,t){if(e){if(typeof e=="string")return Hu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hu(e,t)}}function QL(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function eF(e){if(Array.isArray(e))return Hu(e)}function Hu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Gu=function(t,r,n,i,a){var o=Je(t,el),u=Je(t,Xa),c=[].concat(zp(o),zp(u)),l=Je(t,Ya),f="".concat(i,"Id"),s=i[0],p=r;if(c.length&&(p=c.reduce(function(v,d){if(d.props[f]===n&&rt(d.props,"extendDomain")&&R(d.props[s])){var w=d.props[s];return[Math.min(v[0],w),Math.max(v[1],w)]}return v},p)),l.length){var h="".concat(s,"1"),y="".concat(s,"2");p=l.reduce(function(v,d){if(d.props[f]===n&&rt(d.props,"extendDomain")&&R(d.props[h])&&R(d.props[y])){var w=d.props[h],b=d.props[y];return[Math.min(v[0],w,b),Math.max(v[1],w,b)]}return v},p)}return a&&a.length&&(p=a.reduce(function(v,d){return R(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},p)),p},by={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,l,f){this.fn=c,this.context=l,this.once=f||!1}function a(c,l,f,s,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,s||c,p),y=r?r+l:l;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],h]:c._events[y].push(h):(c._events[y]=h,c._eventsCount++),c}function o(c,l){--c._eventsCount===0?c._events=new n:delete c._events[l]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var l=[],f,s;if(this._eventsCount===0)return l;for(s in f=this._events)t.call(f,s)&&l.push(r?s.slice(1):s);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(f)):l},u.prototype.listeners=function(l){var f=r?r+l:l,s=this._events[f];if(!s)return[];if(s.fn)return[s.fn];for(var p=0,h=s.length,y=new Array(h);p<h;p++)y[p]=s[p].fn;return y},u.prototype.listenerCount=function(l){var f=r?r+l:l,s=this._events[f];return s?s.fn?1:s.length:0},u.prototype.emit=function(l,f,s,p,h,y){var v=r?r+l:l;if(!this._events[v])return!1;var d=this._events[v],w=arguments.length,b,x;if(d.fn){switch(d.once&&this.removeListener(l,d.fn,void 0,!0),w){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,s),!0;case 4:return d.fn.call(d.context,f,s,p),!0;case 5:return d.fn.call(d.context,f,s,p,h),!0;case 6:return d.fn.call(d.context,f,s,p,h,y),!0}for(x=1,b=new Array(w-1);x<w;x++)b[x-1]=arguments[x];d.fn.apply(d.context,b)}else{var m=d.length,g;for(x=0;x<m;x++)switch(d[x].once&&this.removeListener(l,d[x].fn,void 0,!0),w){case 1:d[x].fn.call(d[x].context);break;case 2:d[x].fn.call(d[x].context,f);break;case 3:d[x].fn.call(d[x].context,f,s);break;case 4:d[x].fn.call(d[x].context,f,s,p);break;default:if(!b)for(g=1,b=new Array(w-1);g<w;g++)b[g-1]=arguments[g];d[x].fn.apply(d[x].context,b)}}return!0},u.prototype.on=function(l,f,s){return a(this,l,f,s,!1)},u.prototype.once=function(l,f,s){return a(this,l,f,s,!0)},u.prototype.removeListener=function(l,f,s,p){var h=r?r+l:l;if(!this._events[h])return this;if(!f)return o(this,h),this;var y=this._events[h];if(y.fn)y.fn===f&&(!p||y.once)&&(!s||y.context===s)&&o(this,h);else{for(var v=0,d=[],w=y.length;v<w;v++)(y[v].fn!==f||p&&!y[v].once||s&&y[v].context!==s)&&d.push(y[v]);d.length?this._events[h]=d.length===1?d[0]:d:o(this,h)}return this},u.prototype.removeAllListeners=function(l){var f;return l?(f=r?r+l:l,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(by);var tF=by.exports;const rF=ue(tF);var Ao=new rF,Po="recharts.syncMouseEvents";function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function nF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xy(n.key),n)}}function aF(e,t,r){return t&&iF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function $o(e,t,r){return t=xy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xy(e){var t=oF(e,"string");return Wn(t)=="symbol"?t:t+""}function oF(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var uF=function(){function e(){nF(this,e),$o(this,"activeIndex",0),$o(this,"coordinateList",[]),$o(this,"layout","horizontal")}return aF(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,l=c===void 0?null:c,f=r.offset,s=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=l??this.layout,this.offset=s??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,l=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,s=a+c+l,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:s,pageY:p})}}}])}();function cF(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&R(n)&&R(i))return!0}return!1}function lF(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function wy(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Pe(t,r,n,i),u=Pe(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function sF(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,l=t.innerRadius,f=t.outerRadius,s=t.angle,p=Pe(u,c,l,s),h=Pe(u,c,f,s);n=p.x,i=p.y,a=h.x,o=h.y}else return wy(t);return[{x:n,y:i},{x:a,y:o}]}function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function Up(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Up(Object(r),!0).forEach(function(n){fF(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Up(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fF(e,t,r){return t=pF(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pF(e){var t=hF(e,"string");return zn(t)=="symbol"?t:t+""}function hF(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dF(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,l=e.activeTooltipIndex,f=e.tooltipAxisBandSize,s=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var y,v=Ff;if(p==="ScatterChart")y=o,v=Pk;else if(p==="BarChart")y=lF(s,o,c,f),v=Kc;else if(s==="radial"){var d=wy(o),w=d.cx,b=d.cy,x=d.radius,m=d.startAngle,g=d.endAngle;y={cx:w,cy:b,startAngle:m,endAngle:g,innerRadius:x,outerRadius:x},v=Av}else y={points:sF(s,o,c)},v=Ff;var O=fi(fi(fi(fi({stroke:"#ccc",pointerEvents:"none"},c),y),ee(h,!1)),{},{payload:u,payloadIndex:l,className:re("recharts-tooltip-cursor",h.className)});return B.isValidElement(h)?B.cloneElement(h,O):B.createElement(v,O)}var vF=["item"],yF=["children","className","width","height","style","compact","title","desc"];function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function un(){return un=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},un.apply(this,arguments)}function qp(e,t){return bF(e)||mF(e,t)||Sy(e,t)||gF()}function gF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function bF(e){if(Array.isArray(e))return e}function Hp(e,t){if(e==null)return{};var r=xF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function xF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function wF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function OF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_y(n.key),n)}}function SF(e,t,r){return t&&OF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _F(e,t,r){return t=la(t),AF(e,Oy()?Reflect.construct(t,r||[],la(e).constructor):t.apply(e,r))}function AF(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return PF(e)}function PF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Oy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Oy=function(){return!!e})()}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},la(e)}function $F(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ku(e,t)}function Ku(e,t){return Ku=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ku(e,t)}function jr(e){return jF(e)||EF(e)||Sy(e)||TF()}function TF(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sy(e,t){if(e){if(typeof e=="string")return Xu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xu(e,t)}}function EF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jF(e){if(Array.isArray(e))return Xu(e)}function Xu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Gp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gp(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=_y(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _y(e){var t=MF(e,"string");return Er(t)=="symbol"?t:t+""}function MF(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var CF={xAxis:["bottom","top"],yAxis:["left","right"]},IF={width:"100%",height:"100%"},Ay={x:0,y:0};function pi(e){return e}var NF=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},kF=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return E(E(E({},i),Pe(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,l=i.angle;return E(E(E({},i),Pe(i.cx,i.cy,c,l)),{},{angle:l,radius:c})}return Ay},Za=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var l=c.props.data;return l&&l.length?[].concat(jr(u),jr(l)):u},[]);return o.length>0?o:t&&t.length&&R(i)&&R(a)?t.slice(i,a+1):[]};function Py(e){return e==="number"?[0,"auto"]:void 0}var Vu=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Za(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,l){var f,s=(f=l.props.data)!==null&&f!==void 0?f:r;s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=s===void 0?u:s;p=To(h,o.dataKey,i)}else p=s&&s[n]||u[n];return p?[].concat(jr(c),[wv(l,p)]):c},[])},Kp=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=NF(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,l=t.tooltipTicks,f=yI(o,u,l,c);if(f>=0&&l){var s=l[f]&&l[f].value,p=Vu(t,r,f,s),h=kF(n,u,f,a);return{activeTooltipIndex:f,activeLabel:s,activePayload:p,activeCoordinate:h}}return null},DF=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.layout,s=t.children,p=t.stackOffset,h=vv(f,a);return n.reduce(function(y,v){var d,w=v.type.defaultProps!==void 0?E(E({},v.type.defaultProps),v.props):v.props,b=w.type,x=w.dataKey,m=w.allowDataOverflow,g=w.allowDuplicatedCategory,O=w.scale,S=w.ticks,_=w.includeHidden,T=w[o];if(y[T])return y;var j=Za(t.data,{graphicalItems:i.filter(function(U){var K,ce=o in U.props?U.props[o]:(K=U.type.defaultProps)===null||K===void 0?void 0:K[o];return ce===T}),dataStartIndex:c,dataEndIndex:l}),P=j.length,$,C,M;cF(w.domain,m,b)&&($=vu(w.domain,null,m),h&&(b==="number"||O!=="auto")&&(M=an(j,x,"category")));var N=Py(b);if(!$||$.length===0){var k,D=(k=w.domain)!==null&&k!==void 0?k:N;if(x){if($=an(j,x,b),b==="category"&&h){var L=a0($);g&&L?(C=$,$=Zi(0,P)):g||($=$f(D,$,v).reduce(function(U,K){return U.indexOf(K)>=0?U:[].concat(jr(U),[K])},[]))}else if(b==="category")g?$=$.filter(function(U){return U!==""&&!te(U)}):$=$f(D,$,v).reduce(function(U,K){return U.indexOf(K)>=0||K===""||te(K)?U:[].concat(jr(U),[K])},[]);else if(b==="number"){var z=wI(j,i.filter(function(U){var K,ce,_e=o in U.props?U.props[o]:(K=U.type.defaultProps)===null||K===void 0?void 0:K[o],ot="hide"in U.props?U.props.hide:(ce=U.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return _e===T&&(_||!ot)}),x,a,f);z&&($=z)}h&&(b==="number"||O!=="auto")&&(M=an(j,x,"category"))}else h?$=Zi(0,P):u&&u[T]&&u[T].hasStack&&b==="number"?$=p==="expand"?[0,1]:xv(u[T].stackGroups,c,l):$=dv(j,i.filter(function(U){var K=o in U.props?U.props[o]:U.type.defaultProps[o],ce="hide"in U.props?U.props.hide:U.type.defaultProps.hide;return K===T&&(_||!ce)}),b,f,!0);if(b==="number")$=Gu(s,$,T,a,S),D&&($=vu(D,$,m));else if(b==="category"&&D){var G=D,q=$.every(function(U){return G.indexOf(U)>=0});q&&($=G)}}return E(E({},y),{},H({},T,E(E({},w),{},{axisType:a,domain:$,categoricalDomain:M,duplicateDomain:C,originalDomain:(d=w.domain)!==null&&d!==void 0?d:N,isCategorical:h,layout:f})))},{})},BF=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.layout,s=t.children,p=Za(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:l}),h=p.length,y=vv(f,a),v=-1;return n.reduce(function(d,w){var b=w.type.defaultProps!==void 0?E(E({},w.type.defaultProps),w.props):w.props,x=b[o],m=Py("number");if(!d[x]){v++;var g;return y?g=Zi(0,h):u&&u[x]&&u[x].hasStack?(g=xv(u[x].stackGroups,c,l),g=Gu(s,g,x,a)):(g=vu(m,dv(p,n.filter(function(O){var S,_,T=o in O.props?O.props[o]:(S=O.type.defaultProps)===null||S===void 0?void 0:S[o],j="hide"in O.props?O.props.hide:(_=O.type.defaultProps)===null||_===void 0?void 0:_.hide;return T===x&&!j}),"number",f),i.defaultProps.allowDataOverflow),g=Gu(s,g,x,a)),E(E({},d),{},H({},x,E(E({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ue(CF,"".concat(a,".").concat(v%2),null),domain:g,originalDomain:m,isCategorical:y,layout:f})))}return d},{})},RF=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.children,s="".concat(i,"Id"),p=Je(f,a),h={};return p&&p.length?h=DF(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:s,stackGroups:u,dataStartIndex:c,dataEndIndex:l}):o&&o.length&&(h=BF(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:s,stackGroups:u,dataStartIndex:c,dataEndIndex:l})),h},LF=function(t){var r=At(t),n=dt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:bc(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:zi(r,n)}},Xp=function(t){var r=t.children,n=t.defaultShowTooltip,i=Be(r,wr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},FF=function(t){return!t||!t.length?!1:t.some(function(r){var n=vt(r&&r.type);return n&&n.indexOf("Bar")>=0})},Vp=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},WF=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,l=n.width,f=n.height,s=n.children,p=n.margin||{},h=Be(s,wr),y=Be(s,ur),v=Object.keys(c).reduce(function(g,O){var S=c[O],_=S.orientation;return!S.mirror&&!S.hide?E(E({},g),{},H({},_,g[_]+S.width)):g},{left:p.left||0,right:p.right||0}),d=Object.keys(o).reduce(function(g,O){var S=o[O],_=S.orientation;return!S.mirror&&!S.hide?E(E({},g),{},H({},_,Ue(g,"".concat(_))+S.height)):g},{top:p.top||0,bottom:p.bottom||0}),w=E(E({},d),v),b=w.bottom;h&&(w.bottom+=h.props.height||wr.defaultProps.height),y&&r&&(w=bI(w,i,n,r));var x=l-w.left-w.right,m=f-w.top-w.bottom;return E(E({brushBottom:b},w),{},{width:Math.max(x,0),height:Math.max(m,0)})},zF=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},UF=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,l=t.legendContent,f=t.formatAxisMap,s=t.defaultProps,p=function(d,w){var b=w.graphicalItems,x=w.stackGroups,m=w.offset,g=w.updateId,O=w.dataStartIndex,S=w.dataEndIndex,_=d.barSize,T=d.layout,j=d.barGap,P=d.barCategoryGap,$=d.maxBarSize,C=Vp(T),M=C.numericAxisName,N=C.cateAxisName,k=FF(b),D=[];return b.forEach(function(L,z){var G=Za(d.data,{graphicalItems:[L],dataStartIndex:O,dataEndIndex:S}),q=L.type.defaultProps!==void 0?E(E({},L.type.defaultProps),L.props):L.props,U=q.dataKey,K=q.maxBarSize,ce=q["".concat(M,"Id")],_e=q["".concat(N,"Id")],ot={},$e=c.reduce(function(kt,Dt){var Ja=w["".concat(Dt.axisType,"Map")],al=q["".concat(Dt.axisType,"Id")];Ja&&Ja[al]||Dt.axisType==="zAxis"||Vt();var ol=Ja[al];return E(E({},kt),{},H(H({},Dt.axisType,ol),"".concat(Dt.axisType,"Ticks"),dt(ol)))},ot),ut=$e[N],F=$e["".concat(N,"Ticks")],X=x&&x[ce]&&x[ce].hasStack&&jI(L,x[ce].stackGroups),Y=vt(L.type).indexOf("Bar")>=0,I=zi(ut,F),he=[],J=k&&gI({barSize:_,stackGroups:x,totalSize:zF($e,N)});if(Y){var ge,de,ke=te(K)?$:K,ct=(ge=(de=zi(ut,F,!0))!==null&&de!==void 0?de:ke)!==null&&ge!==void 0?ge:0;he=mI({barGap:j,barCategoryGap:P,bandSize:ct!==I?ct:I,sizeList:J[_e],maxBarSize:ke}),ct!==I&&(he=he.map(function(kt){return E(E({},kt),{},{position:E(E({},kt.position),{},{offset:kt.position.offset-ct/2})})}))}var Zn=L&&L.type&&L.type.getComposedData;Zn&&D.push({props:E(E({},Zn(E(E({},$e),{},{displayedData:G,props:d,dataKey:U,item:L,bandSize:I,barPosition:he,offset:m,stackedData:X,layout:T,dataStartIndex:O,dataEndIndex:S}))),{},H(H(H({key:L.key||"item-".concat(z)},M,$e[M]),N,$e[N]),"animationId",g)),childIndex:y0(L,d.children),item:L})}),D},h=function(d,w){var b=d.props,x=d.dataStartIndex,m=d.dataEndIndex,g=d.updateId;if(!xl({props:b}))return null;var O=b.children,S=b.layout,_=b.stackOffset,T=b.data,j=b.reverseStackOrder,P=Vp(S),$=P.numericAxisName,C=P.cateAxisName,M=Je(O,n),N=TI(T,M,"".concat($,"Id"),"".concat(C,"Id"),_,j),k=c.reduce(function(q,U){var K="".concat(U.axisType,"Map");return E(E({},q),{},H({},K,RF(b,E(E({},U),{},{graphicalItems:M,stackGroups:U.axisType===$&&N,dataStartIndex:x,dataEndIndex:m}))))},{}),D=WF(E(E({},k),{},{props:b,graphicalItems:M}),w==null?void 0:w.legendBBox);Object.keys(k).forEach(function(q){k[q]=f(b,k[q],D,q.replace("Map",""),r)});var L=k["".concat(C,"Map")],z=LF(L),G=p(b,E(E({},k),{},{dataStartIndex:x,dataEndIndex:m,updateId:g,graphicalItems:M,stackGroups:N,offset:D}));return E(E({formattedGraphicalItems:G,graphicalItems:M,offset:D,stackGroups:N},z),k)},y=function(v){function d(w){var b,x,m;return wF(this,d),m=_F(this,d,[w]),H(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(m,"accessibilityManager",new uF),H(m,"handleLegendBBoxUpdate",function(g){if(g){var O=m.state,S=O.dataStartIndex,_=O.dataEndIndex,T=O.updateId;m.setState(E({legendBBox:g},h({props:m.props,dataStartIndex:S,dataEndIndex:_,updateId:T},E(E({},m.state),{},{legendBBox:g}))))}}),H(m,"handleReceiveSyncEvent",function(g,O,S){if(m.props.syncId===g){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(O)}}),H(m,"handleBrushChange",function(g){var O=g.startIndex,S=g.endIndex;if(O!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var _=m.state.updateId;m.setState(function(){return E({dataStartIndex:O,dataEndIndex:S},h({props:m.props,dataStartIndex:O,dataEndIndex:S,updateId:_},m.state))}),m.triggerSyncEvent({dataStartIndex:O,dataEndIndex:S})}}),H(m,"handleMouseEnter",function(g){var O=m.getMouseInfo(g);if(O){var S=E(E({},O),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var _=m.props.onMouseEnter;Z(_)&&_(S,g)}}),H(m,"triggeredAfterMouseMove",function(g){var O=m.getMouseInfo(g),S=O?E(E({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var _=m.props.onMouseMove;Z(_)&&_(S,g)}),H(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),H(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),H(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),H(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};m.setState(O),m.triggerSyncEvent(O);var S=m.props.onMouseLeave;Z(S)&&S(O,g)}),H(m,"handleOuterEvent",function(g){var O=v0(g),S=Ue(m.props,"".concat(O));if(O&&Z(S)){var _,T;/.*touch.*/i.test(O)?T=m.getMouseInfo(g.changedTouches[0]):T=m.getMouseInfo(g),S((_=T)!==null&&_!==void 0?_:{},g)}}),H(m,"handleClick",function(g){var O=m.getMouseInfo(g);if(O){var S=E(E({},O),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var _=m.props.onClick;Z(_)&&_(S,g)}}),H(m,"handleMouseDown",function(g){var O=m.props.onMouseDown;if(Z(O)){var S=m.getMouseInfo(g);O(S,g)}}),H(m,"handleMouseUp",function(g){var O=m.props.onMouseUp;if(Z(O)){var S=m.getMouseInfo(g);O(S,g)}}),H(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),H(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),H(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),H(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&Ao.emit(Po,m.props.syncId,g,m.eventEmitterSymbol)}),H(m,"applySyncEvent",function(g){var O=m.props,S=O.layout,_=O.syncMethod,T=m.state.updateId,j=g.dataStartIndex,P=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(E({dataStartIndex:j,dataEndIndex:P},h({props:m.props,dataStartIndex:j,dataEndIndex:P,updateId:T},m.state)));else if(g.activeTooltipIndex!==void 0){var $=g.chartX,C=g.chartY,M=g.activeTooltipIndex,N=m.state,k=N.offset,D=N.tooltipTicks;if(!k)return;if(typeof _=="function")M=_(D,g);else if(_==="value"){M=-1;for(var L=0;L<D.length;L++)if(D[L].value===g.activeLabel){M=L;break}}var z=E(E({},k),{},{x:k.left,y:k.top}),G=Math.min($,z.x+z.width),q=Math.min(C,z.y+z.height),U=D[M]&&D[M].value,K=Vu(m.state,m.props.data,M),ce=D[M]?{x:S==="horizontal"?D[M].coordinate:G,y:S==="horizontal"?q:D[M].coordinate}:Ay;m.setState(E(E({},g),{},{activeLabel:U,activeCoordinate:ce,activePayload:K,activeTooltipIndex:M}))}else m.setState(g)}),H(m,"renderCursor",function(g){var O,S=m.state,_=S.isTooltipActive,T=S.activeCoordinate,j=S.activePayload,P=S.offset,$=S.activeTooltipIndex,C=S.tooltipAxisBandSize,M=m.getTooltipEventType(),N=(O=g.props.active)!==null&&O!==void 0?O:_,k=m.props.layout,D=g.key||"_recharts-cursor";return A.createElement(dF,{key:D,activeCoordinate:T,activePayload:j,activeTooltipIndex:$,chartName:r,element:g,isActive:N,layout:k,offset:P,tooltipAxisBandSize:C,tooltipEventType:M})}),H(m,"renderPolarAxis",function(g,O,S){var _=Ue(g,"type.axisType"),T=Ue(m.state,"".concat(_,"Map")),j=g.type.defaultProps,P=j!==void 0?E(E({},j),g.props):g.props,$=T&&T[P["".concat(_,"Id")]];return B.cloneElement(g,E(E({},$),{},{className:re(_,$.className),key:g.key||"".concat(O,"-").concat(S),ticks:dt($,!0)}))}),H(m,"renderPolarGrid",function(g){var O=g.props,S=O.radialLines,_=O.polarAngles,T=O.polarRadius,j=m.state,P=j.radiusAxisMap,$=j.angleAxisMap,C=At(P),M=At($),N=M.cx,k=M.cy,D=M.innerRadius,L=M.outerRadius;return B.cloneElement(g,{polarAngles:Array.isArray(_)?_:dt(M,!0).map(function(z){return z.coordinate}),polarRadius:Array.isArray(T)?T:dt(C,!0).map(function(z){return z.coordinate}),cx:N,cy:k,innerRadius:D,outerRadius:L,key:g.key||"polar-grid",radialLines:S})}),H(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,O=m.props,S=O.children,_=O.width,T=O.height,j=m.props.margin||{},P=_-(j.left||0)-(j.right||0),$=pv({children:S,formattedGraphicalItems:g,legendWidth:P,legendContent:l});if(!$)return null;var C=$.item,M=Hp($,vF);return B.cloneElement(C,E(E({},M),{},{chartWidth:_,chartHeight:T,margin:j,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),H(m,"renderTooltip",function(){var g,O=m.props,S=O.children,_=O.accessibilityLayer,T=Be(S,st);if(!T)return null;var j=m.state,P=j.isTooltipActive,$=j.activeCoordinate,C=j.activePayload,M=j.activeLabel,N=j.offset,k=(g=T.props.active)!==null&&g!==void 0?g:P;return B.cloneElement(T,{viewBox:E(E({},N),{},{x:N.left,y:N.top}),active:k,label:M,payload:k?C:[],coordinate:$,accessibilityLayer:_})}),H(m,"renderBrush",function(g){var O=m.props,S=O.margin,_=O.data,T=m.state,j=T.offset,P=T.dataStartIndex,$=T.dataEndIndex,C=T.updateId;return B.cloneElement(g,{key:g.key||"_recharts-brush",onChange:ui(m.handleBrushChange,g.props.onChange),data:_,x:R(g.props.x)?g.props.x:j.left,y:R(g.props.y)?g.props.y:j.top+j.height+j.brushBottom-(S.bottom||0),width:R(g.props.width)?g.props.width:j.width,startIndex:P,endIndex:$,updateId:"brush-".concat(C)})}),H(m,"renderReferenceElement",function(g,O,S){if(!g)return null;var _=m,T=_.clipPathId,j=m.state,P=j.xAxisMap,$=j.yAxisMap,C=j.offset,M=g.type.defaultProps||{},N=g.props,k=N.xAxisId,D=k===void 0?M.xAxisId:k,L=N.yAxisId,z=L===void 0?M.yAxisId:L;return B.cloneElement(g,{key:g.key||"".concat(O,"-").concat(S),xAxis:P[D],yAxis:$[z],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:T})}),H(m,"renderActivePoints",function(g){var O=g.item,S=g.activePoint,_=g.basePoint,T=g.childIndex,j=g.isRange,P=[],$=O.props.key,C=O.item.type.defaultProps!==void 0?E(E({},O.item.type.defaultProps),O.item.props):O.item.props,M=C.activeDot,N=C.dataKey,k=E(E({index:T,dataKey:N,cx:S.x,cy:S.y,r:4,fill:Gc(O.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},ee(M,!1)),hi(M));return P.push(d.renderActiveDot(M,k,"".concat($,"-activePoint-").concat(T))),_?P.push(d.renderActiveDot(M,E(E({},k),{},{cx:_.x,cy:_.y}),"".concat($,"-basePoint-").concat(T))):j&&P.push(null),P}),H(m,"renderGraphicChild",function(g,O,S){var _=m.filterFormatItem(g,O,S);if(!_)return null;var T=m.getTooltipEventType(),j=m.state,P=j.isTooltipActive,$=j.tooltipAxis,C=j.activeTooltipIndex,M=j.activeLabel,N=m.props.children,k=Be(N,st),D=_.props,L=D.points,z=D.isRange,G=D.baseLine,q=_.item.type.defaultProps!==void 0?E(E({},_.item.type.defaultProps),_.item.props):_.item.props,U=q.activeDot,K=q.hide,ce=q.activeBar,_e=q.activeShape,ot=!!(!K&&P&&k&&(U||ce||_e)),$e={};T!=="axis"&&k&&k.props.trigger==="click"?$e={onClick:ui(m.handleItemMouseEnter,g.props.onClick)}:T!=="axis"&&($e={onMouseLeave:ui(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:ui(m.handleItemMouseEnter,g.props.onMouseEnter)});var ut=B.cloneElement(g,E(E({},_.props),$e));function F(Dt){return typeof $.dataKey=="function"?$.dataKey(Dt.payload):null}if(ot)if(C>=0){var X,Y;if($.dataKey&&!$.allowDuplicatedCategory){var I=typeof $.dataKey=="function"?F:"payload.".concat($.dataKey.toString());X=To(L,I,M),Y=z&&G&&To(G,I,M)}else X=L==null?void 0:L[C],Y=z&&G&&G[C];if(_e||ce){var he=g.props.activeIndex!==void 0?g.props.activeIndex:C;return[B.cloneElement(g,E(E(E({},_.props),$e),{},{activeIndex:he})),null,null]}if(!te(X))return[ut].concat(jr(m.renderActivePoints({item:_,activePoint:X,basePoint:Y,childIndex:C,isRange:z})))}else{var J,ge=(J=m.getItemByXY(m.state.activeCoordinate))!==null&&J!==void 0?J:{graphicalItem:ut},de=ge.graphicalItem,ke=de.item,ct=ke===void 0?g:ke,Zn=de.childIndex,kt=E(E(E({},_.props),$e),{},{activeIndex:Zn});return[B.cloneElement(ct,kt),null,null]}return z?[ut,null,null]:[ut,null]}),H(m,"renderCustomized",function(g,O,S){return B.cloneElement(g,E(E({key:"recharts-customized-".concat(S)},m.props),m.state))}),H(m,"renderMap",{CartesianGrid:{handler:pi,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:pi},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:pi},YAxis:{handler:pi},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((b=w.id)!==null&&b!==void 0?b:Aa("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=fd(m.triggeredAfterMouseMove,(x=w.throttleDelay)!==null&&x!==void 0?x:1e3/60),m.state={},m}return $F(d,v),SF(d,[{key:"componentDidMount",value:function(){var b,x;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(b=this.props.margin.left)!==null&&b!==void 0?b:0,top:(x=this.props.margin.top)!==null&&x!==void 0?x:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var b=this.props,x=b.children,m=b.data,g=b.height,O=b.layout,S=Be(x,st);if(S){var _=S.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var T=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,j=Vu(this.state,m,_,T),P=this.state.tooltipTicks[_].coordinate,$=(this.state.offset.top+g)/2,C=O==="horizontal",M=C?{x:P,y:$}:{y:P,x:$},N=this.state.formattedGraphicalItems.find(function(D){var L=D.item;return L.type.name==="Scatter"});N&&(M=E(E({},M),N.props.points[_].tooltipPosition),j=N.props.points[_].tooltipPayload);var k={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:T,activePayload:j,activeCoordinate:M};this.setState(k),this.renderCursor(S),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(b,x){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==x.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==b.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==b.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(b){jo([Be(b.children,st)],[Be(this.props.children,st)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var b=Be(this.props.children,st);if(b&&typeof b.props.shared=="boolean"){var x=b.props.shared?"axis":"item";return u.indexOf(x)>=0?x:a}return a}},{key:"getMouseInfo",value:function(b){if(!this.container)return null;var x=this.container,m=x.getBoundingClientRect(),g=zT(m),O={chartX:Math.round(b.pageX-g.left),chartY:Math.round(b.pageY-g.top)},S=m.width/x.offsetWidth||1,_=this.inRange(O.chartX,O.chartY,S);if(!_)return null;var T=this.state,j=T.xAxisMap,P=T.yAxisMap,$=this.getTooltipEventType();if($!=="axis"&&j&&P){var C=At(j).scale,M=At(P).scale,N=C&&C.invert?C.invert(O.chartX):null,k=M&&M.invert?M.invert(O.chartY):null;return E(E({},O),{},{xValue:N,yValue:k})}var D=Kp(this.state,this.props.data,this.props.layout,_);return D?E(E({},O),D):null}},{key:"inRange",value:function(b,x){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,O=b/m,S=x/m;if(g==="horizontal"||g==="vertical"){var _=this.state.offset,T=O>=_.left&&O<=_.left+_.width&&S>=_.top&&S<=_.top+_.height;return T?{x:O,y:S}:null}var j=this.state,P=j.angleAxisMap,$=j.radiusAxisMap;if(P&&$){var C=At(P);return jf({x:O,y:S},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var b=this.props.children,x=this.getTooltipEventType(),m=Be(b,st),g={};m&&x==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd});var O=hi(this.props,this.handleOuterEvent);return E(E({},O),g)}},{key:"addListener",value:function(){Ao.on(Po,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Ao.removeListener(Po,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(b,x,m){for(var g=this.state.formattedGraphicalItems,O=0,S=g.length;O<S;O++){var _=g[O];if(_.item===b||_.props.key===b.key||x===vt(_.item.type)&&m===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var b=this.clipPathId,x=this.state.offset,m=x.left,g=x.top,O=x.height,S=x.width;return A.createElement("defs",null,A.createElement("clipPath",{id:b},A.createElement("rect",{x:m,y:g,height:O,width:S})))}},{key:"getXScales",value:function(){var b=this.state.xAxisMap;return b?Object.entries(b).reduce(function(x,m){var g=qp(m,2),O=g[0],S=g[1];return E(E({},x),{},H({},O,S.scale))},{}):null}},{key:"getYScales",value:function(){var b=this.state.yAxisMap;return b?Object.entries(b).reduce(function(x,m){var g=qp(m,2),O=g[0],S=g[1];return E(E({},x),{},H({},O,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(b){var x;return(x=this.state.xAxisMap)===null||x===void 0||(x=x[b])===null||x===void 0?void 0:x.scale}},{key:"getYScaleByAxisId",value:function(b){var x;return(x=this.state.yAxisMap)===null||x===void 0||(x=x[b])===null||x===void 0?void 0:x.scale}},{key:"getItemByXY",value:function(b){var x=this.state,m=x.formattedGraphicalItems,g=x.activeItem;if(m&&m.length)for(var O=0,S=m.length;O<S;O++){var _=m[O],T=_.props,j=_.item,P=j.type.defaultProps!==void 0?E(E({},j.type.defaultProps),j.props):j.props,$=vt(j.type);if($==="Bar"){var C=(T.data||[]).find(function(D){return yk(b,D)});if(C)return{graphicalItem:_,payload:C}}else if($==="RadialBar"){var M=(T.data||[]).find(function(D){return jf(b,D)});if(M)return{graphicalItem:_,payload:M}}else if(qa(_,g)||Ha(_,g)||Dn(_,g)){var N=bD({graphicalItem:_,activeTooltipItem:g,itemData:P.data}),k=P.activeIndex===void 0?N:P.activeIndex;return{graphicalItem:E(E({},_),{},{childIndex:k}),payload:Dn(_,g)?P.data[N]:_.props.data[N]}}}return null}},{key:"render",value:function(){var b=this;if(!xl(this))return null;var x=this.props,m=x.children,g=x.className,O=x.width,S=x.height,_=x.style,T=x.compact,j=x.title,P=x.desc,$=Hp(x,yF),C=ee($,!1);if(T)return A.createElement(Ep,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement(Co,un({},C,{width:O,height:S,title:j,desc:P}),this.renderClipPath(),Ol(m,this.renderMap)));if(this.props.accessibilityLayer){var M,N;C.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,C.role=(N=this.props.role)!==null&&N!==void 0?N:"application",C.onKeyDown=function(D){b.accessibilityManager.keyboardEvent(D)},C.onFocus=function(){b.accessibilityManager.focus()}}var k=this.parseEventsOfWrapper();return A.createElement(Ep,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement("div",un({className:re("recharts-wrapper",g),style:E({position:"relative",cursor:"default",width:O,height:S},_)},k,{ref:function(L){b.container=L}}),A.createElement(Co,un({},C,{width:O,height:S,title:j,desc:P,style:IF}),this.renderClipPath(),Ol(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(B.Component);return H(y,"displayName",r),H(y,"defaultProps",E({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},s)),H(y,"getDerivedStateFromProps",function(v,d){var w=v.dataKey,b=v.data,x=v.children,m=v.width,g=v.height,O=v.layout,S=v.stackOffset,_=v.margin,T=d.dataStartIndex,j=d.dataEndIndex;if(d.updateId===void 0){var P=Xp(v);return E(E(E({},P),{},{updateId:0},h(E(E({props:v},P),{},{updateId:0}),d)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:S,prevMargin:_,prevChildren:x})}if(w!==d.prevDataKey||b!==d.prevData||m!==d.prevWidth||g!==d.prevHeight||O!==d.prevLayout||S!==d.prevStackOffset||!or(_,d.prevMargin)){var $=Xp(v),C={chartX:d.chartX,chartY:d.chartY,isTooltipActive:d.isTooltipActive},M=E(E({},Kp(d,b,O)),{},{updateId:d.updateId+1}),N=E(E(E({},$),C),M);return E(E(E({},N),h(E({props:v},N),d)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:S,prevMargin:_,prevChildren:x})}if(!jo(x,d.prevChildren)){var k,D,L,z,G=Be(x,wr),q=G&&(k=(D=G.props)===null||D===void 0?void 0:D.startIndex)!==null&&k!==void 0?k:T,U=G&&(L=(z=G.props)===null||z===void 0?void 0:z.endIndex)!==null&&L!==void 0?L:j,K=q!==T||U!==j,ce=!te(b),_e=ce&&!K?d.updateId:d.updateId+1;return E(E({updateId:_e},h(E(E({props:v},d),{},{updateId:_e,dataStartIndex:q,dataEndIndex:U}),d)),{},{prevChildren:x,dataStartIndex:q,dataEndIndex:U})}return null}),H(y,"renderActiveDot",function(v,d,w){var b;return B.isValidElement(v)?b=B.cloneElement(v,d):Z(v)?b=v(d):b=A.createElement(Bv,d),A.createElement(Oe,{className:"recharts-active-dot",key:w},b)}),function(d){return A.createElement(y,d)}},XF=UF({chartName:"BarChart",GraphicalChild:Ur,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:nl},{axisType:"yAxis",AxisComp:il}],formatAxisMap:qB});export{Et as A,XF as B,CL as C,Bv as D,pd as E,UF as F,$t as G,GF as H,ur as L,HF as R,pD as S,st as T,nl as X,il as Y,Yd as _,Ur as a,Mt as b,UM as c,YM as d,Ti as e,ee as f,Oe as g,KF as h,Z as i,di as j,Ee as k,Ff as l,te as m,it as n,Ue as o,Pe as p,er as q,Uc as r,R as s,Kt as t,Je as u,FI as v,Ze as w,yt as x,Ye as y,Aa as z};
