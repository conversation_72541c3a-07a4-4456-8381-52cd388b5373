import{c as W,j as e,k as j,l as g,B as w,a as T,C as S,i as D,m as Z,b as Q,r as I}from"./index-Cmt5neWh.js";import{C,f as z,U as M,T as ee,b as te,c as se,M as ae}from"./MainLayout-wyzz138D.js";import{D as re}from"./download-BHlV_KY3.js";import{T as ne,a as oe,b,c as y}from"./tabs-BJh52NhZ.js";import{P as A,a as E,b as H,C as B}from"./popover-BPm3A8JC.js";import{X as ie}from"./input-BK13BBqa.js";import{C as le}from"./circle-check-big-DK6RP7UF.js";import{T as ce}from"./target-Cbp8QShB.js";import{R as L,B as V,C as F,X as O,Y as U,T as _,L as R,a as X}from"./BarChart-DJI4ZcuR.js";import"./index-C8JwcrUT.js";import"./subDays-BLJlWEqr.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=W("CalendarRange",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M17 14h-6",key:"bkmgh3"}],["path",{d:"M13 18H7",key:"bb0bb7"}],["path",{d:"M7 14h.01",key:"1qa3f1"}],["path",{d:"M17 18h.01",key:"1bdyru"}]]),$=t=>{switch(t){case"N/A":return"N/A";case"Angetroffen → Termin":return"Termin";case"Angetroffen → Kein Interesse":return"Kein Interesse";case"Angetroffen → Sale":return"Sale";default:return t}},K=(t,s,a)=>{const n=new Date(t);if(n.setHours(0,0,0,0),s){const r=new Date(s);if(r.setHours(0,0,0,0),n<r)return!1}if(a){const r=new Date(a);if(r.setHours(23,59,59,999),n>r)return!1}return!0},de=t=>{const s=new Date,a=new Date,n=new Date;switch(t){case"day":a.setHours(0,0,0,0),n.setHours(23,59,59,999);break;case"week":a.setDate(s.getDate()-7),a.setHours(0,0,0,0),n.setHours(23,59,59,999);break;case"month":a.setDate(s.getDate()-30),a.setHours(0,0,0,0),n.setHours(23,59,59,999);break;default:return{from:s,to:s}}return{from:a,to:n}},me=(t,s,a,n,r,l)=>{let m,c;if(t==="custom"&&l)m=l.from,c=l.to;else if(t!=="custom"){const i=de(t);m=i.from,c=i.to}const o=[];s.forEach(i=>{if(!K(i.timestamp,m,c))return;const x=n.find(h=>h.id===i.houseId);if(!x)return;const d=r.find(h=>h.id===x.addressId);d&&o.push({street:d.street,houseNumber:x.houseNumber,city:d.city,zipCode:d.zipCode,status:$(i.status),visitDate:i.timestamp.split("T")[0],appointmentDate:i.appointmentDate,appointmentTime:i.appointmentTime,type:"EFH"})}),a.forEach(i=>{const x=s.find(p=>p.id===i.visitId);if(!x||!K(x.timestamp,m,c))return;const d=n.find(p=>p.id===x.houseId);if(!d)return;const h=r.find(p=>p.id===d.addressId);h&&o.push({street:h.street,houseNumber:d.houseNumber,city:h.city,zipCode:h.zipCode,status:$(i.status),visitDate:x.timestamp.split("T")[0],appointmentDate:i.appointmentDate,appointmentTime:i.appointmentTime,doorName:i.name,type:"MFH"})}),o.sort((i,x)=>new Date(x.visitDate).getTime()-new Date(i.visitDate).getTime());const f={period:t==="custom"?"Benutzerdefiniert":t,exportDate:new Date().toISOString().split("T")[0],totalVisits:o.length,visits:o};return t==="custom"&&m&&c&&(f.dateRange={from:m.toISOString().split("T")[0],to:c.toISOString().split("T")[0]}),f},xe=t=>{const s=JSON.stringify(t,null,2),a=new Blob([s],{type:"application/json"}),n=URL.createObjectURL(a),r=t.period==="Benutzerdefiniert"&&t.dateRange?`visits_export_${t.dateRange.from}_to_${t.dateRange.to}.json`:`visits_export_${t.exportDate}_${t.period}.json`,l=document.createElement("a");l.href=n,l.download=r,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(n)},he=({onExport:t,isMobile:s})=>e.jsx(j,{className:"glass-card rounded-3xl border-0 shadow-2xl",children:e.jsx(g,{className:"p-6",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"Statistiken"}),e.jsxs(w,{onClick:t,variant:"outline",size:s?"lg":"default",className:`${s?"h-14 px-6":"h-12 px-4"} rounded-xl border-2 border-gray-300 hover:border-red-500 hover:text-red-600 transition-all duration-200 hover-scale touch-feedback`,children:[e.jsx(re,{size:s?20:16}),s&&e.jsx("span",{className:"ml-2",children:"Export"})]})]})})}),ue=({dateRange:t,onDateRangeChange:s,isMobile:a})=>{const n=()=>{s({from:void 0,to:void 0})};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Von Datum"}),e.jsxs(A,{children:[e.jsx(E,{asChild:!0,children:e.jsxs(w,{variant:"outline",className:T("w-full justify-start text-left font-normal",!t.from&&"text-muted-foreground",a?"h-14 text-lg":"h-12"),children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),t.from?z(t.from,"dd.MM.yyyy"):"Datum wählen"]})}),e.jsx(H,{className:"w-auto p-0",align:"start",children:e.jsx(B,{mode:"single",selected:t.from,onSelect:r=>s({...t,from:r}),initialFocus:!0,className:"p-3 pointer-events-auto"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Bis Datum"}),e.jsxs(A,{children:[e.jsx(E,{asChild:!0,children:e.jsxs(w,{variant:"outline",className:T("w-full justify-start text-left font-normal",!t.to&&"text-muted-foreground",a?"h-14 text-lg":"h-12"),children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),t.to?z(t.to,"dd.MM.yyyy"):"Datum wählen"]})}),e.jsx(H,{className:"w-auto p-0",align:"start",children:e.jsx(B,{mode:"single",selected:t.to,onSelect:r=>s({...t,to:r}),disabled:r=>t.from?r<t.from:!1,initialFocus:!0,className:"p-3 pointer-events-auto"})})]})]})]}),(t.from||t.to)&&e.jsxs(w,{variant:"outline",size:"sm",onClick:n,className:"w-full md:w-auto",children:[e.jsx(ie,{className:"mr-2 h-4 w-4"}),"Zurücksetzen"]})]})},fe=({totalDoors:t,salesCount:s})=>e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(j,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl",children:e.jsxs(g,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(M,{className:"h-8 w-8 text-blue-600"})}),e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Gesamttüren"}),e.jsx("p",{className:"text-3xl font-bold text-gray-800",children:t})]})}),e.jsx(j,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl",children:e.jsxs(g,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(le,{className:"h-8 w-8 text-green-600"})}),e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Sales"}),e.jsx("p",{className:"text-3xl font-bold text-green-600",children:s})]})})]}),pe=({data:t,isMobile:s})=>e.jsxs(j,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-slide-in",children:[e.jsx(S,{className:"pb-4",children:e.jsxs(D,{className:"text-xl font-bold text-gray-800 flex items-center gap-3",children:[e.jsx(ce,{className:"h-6 w-6 text-blue-600"}),"Besuchsstatus"]})}),e.jsx(g,{className:"pb-6",children:e.jsx("div",{style:{height:s?"250px":"320px"},className:"w-full",children:e.jsx(L,{width:"100%",height:"100%",children:e.jsxs(V,{data:t,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx(F,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(O,{dataKey:"name",tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(U,{tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(_,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),e.jsx(R,{}),e.jsx(X,{dataKey:"count",name:"Anzahl",fill:"#3b82f6",radius:[4,4,0,0]})]})})})})]}),je=({data:t,isMobile:s})=>e.jsxs(j,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-slide-in",children:[e.jsx(S,{className:"pb-4",children:e.jsxs(D,{className:"text-xl font-bold text-gray-800 flex items-center gap-3",children:[e.jsx(ee,{className:"h-6 w-6 text-green-600"}),"Produktverkäufe"]})}),e.jsx(g,{className:"pb-6",children:e.jsx("div",{style:{height:s?"250px":"320px"},className:"w-full",children:e.jsx(L,{width:"100%",height:"100%",children:e.jsxs(V,{data:t,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx(F,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(O,{dataKey:"name",tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(U,{tick:{fontSize:s?12:14},stroke:"#64748b"}),e.jsx(_,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"none",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)"}}),e.jsx(R,{}),e.jsx(X,{dataKey:"count",name:"Anzahl",fill:"#10b981",radius:[4,4,0,0]})]})})})})]}),v=({totalDoors:t,salesCount:s,statusData:a,productData:n,isMobile:r})=>e.jsxs("div",{className:"space-y-6 animate-fade-in",children:[e.jsx(fe,{totalDoors:t,salesCount:s}),e.jsx(pe,{data:a,isMobile:r}),e.jsx(je,{data:n,isMobile:r})]}),ge=({period:t,onPeriodChange:s,customDateRange:a,onCustomDateRangeChange:n,totalDoors:r,salesCount:l,statusData:m,productData:c,isMobile:o})=>e.jsx(j,{className:"glass-card rounded-3xl border-0 shadow-2xl",children:e.jsx(g,{className:"p-6",children:e.jsxs(ne,{defaultValue:"day",onValueChange:f=>s(f),children:[e.jsxs(oe,{className:`grid w-full grid-cols-4 ${o?"h-16":"h-12"} bg-gray-100 rounded-2xl p-2`,children:[e.jsxs(b,{value:"day",className:`${o?"h-12 text-sm":"h-8 text-sm"} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`,children:[e.jsx(C,{className:"h-4 w-4"}),!o&&e.jsx("span",{children:"Tag"})]}),e.jsxs(b,{value:"week",className:`${o?"h-12 text-sm":"h-8 text-sm"} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`,children:[e.jsx(P,{className:"h-4 w-4"}),!o&&e.jsx("span",{children:"Woche"})]}),e.jsxs(b,{value:"month",className:`${o?"h-12 text-sm":"h-8 text-sm"} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`,children:[e.jsx(C,{className:"h-4 w-4"}),!o&&e.jsx("span",{children:"Monat"})]}),e.jsxs(b,{value:"custom",className:`${o?"h-12 text-xs":"h-8 text-xs"} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-1`,children:[e.jsx(P,{className:"h-4 w-4"}),!o&&e.jsx("span",{children:"Custom"})]})]}),e.jsx(y,{value:"day",className:"mt-6",children:e.jsx(v,{totalDoors:r,salesCount:l,statusData:m,productData:c,isMobile:o})}),e.jsx(y,{value:"week",className:"mt-6",children:e.jsx(v,{totalDoors:r,salesCount:l,statusData:m,productData:c,isMobile:o})}),e.jsx(y,{value:"month",className:"mt-6",children:e.jsx(v,{totalDoors:r,salesCount:l,statusData:m,productData:c,isMobile:o})}),e.jsx(y,{value:"custom",className:"mt-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs(j,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl",children:[e.jsx(S,{className:"pb-4",children:e.jsx(D,{className:"text-xl font-bold text-gray-800",children:"Datumsbereich auswählen"})}),e.jsx(g,{className:"pb-6",children:e.jsx(ue,{dateRange:a,onDateRangeChange:n,isMobile:o})})]}),a.from&&a.to&&e.jsx(v,{totalDoors:r,salesCount:l,statusData:m,productData:c,isMobile:o})]})})]})})}),Ne=()=>{const{houses:t,visits:s,doors:a,products:n,addresses:r}=Z();Q();const[l,m]=I.useState("day"),[c,o]=I.useState({from:void 0,to:void 0}),f=te(),{toast:i}=se(),x=()=>{const u=s.filter(Y=>{const k=t.find(q=>q.id===Y.houseId);return(k==null?void 0:k.type)==="EFH"}).length,N=a.length;return u+N},d=u=>a.filter(N=>N.status===u).length,h=u=>n.filter(N=>N.category===u).length,p=[{name:"N/A",count:d("N/A")},{name:"Termin",count:d("Angetroffen → Termin")},{name:"Kein Interesse",count:d("Angetroffen → Kein Interesse")},{name:"Sale",count:d("Angetroffen → Sale")}],G=[{name:"KIP",count:h("KIP")},{name:"TV",count:h("TV")},{name:"Mobile",count:h("Mobile")}],J=()=>{if(l==="custom"&&(!c.from||!c.to)){i({title:"Fehler",description:"Bitte wählen Sie einen vollständigen Datumsbereich aus.",variant:"destructive"});return}try{const u=me(l,s,a,t,r,l==="custom"?c:void 0);xe(u),i({title:"Export erfolgreich",description:`${u.totalVisits} Besuche wurden exportiert.`})}catch(u){console.error("Export error:",u),i({title:"Export fehlgeschlagen",description:"Beim Exportieren ist ein Fehler aufgetreten.",variant:"destructive"})}};return e.jsxs("div",{className:`${f?"px-4 py-6":"p-6"} space-y-6 w-full animate-fade-in`,children:[e.jsx(he,{onExport:J,isMobile:f}),e.jsx(ge,{period:l,onPeriodChange:m,customDateRange:c,onCustomDateRangeChange:o,totalDoors:x(),salesCount:d("Angetroffen → Sale"),statusData:p,productData:G,isMobile:f})]})},Ae=()=>e.jsx(ae,{title:"Statistiken",children:e.jsx("div",{className:"w-full",children:e.jsx(Ne,{})})});export{Ae as default};
