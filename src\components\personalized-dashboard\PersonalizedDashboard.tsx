import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Target, 
  Flame, 
  Star, 
  Award, 
  TrendingUp,
  Calendar,
  Clock,
  MapPin,
  Zap,
  Crown,
  Medal,
  Gift,
  ChevronRight
} from 'lucide-react';
import { useData } from '@/context/data';
import { triggerHapticFeedback } from '@/hooks/useSwipeGestures';
import { format, startOfWeek, endOfWeek, isToday, isThisWeek, differenceInDays } from 'date-fns';
import { de } from 'date-fns/locale';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  unlockedAt?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
}

interface PersonalStats {
  todayVisits: number;
  todaySuccessful: number;
  weeklyVisits: number;
  weeklySuccessful: number;
  totalVisits: number;
  totalSuccessful: number;
  currentStreak: number;
  longestStreak: number;
  averageVisitsPerDay: number;
  successRate: number;
  level: number;
  experience: number;
  experienceToNext: number;
}

interface PersonalizedDashboardProps {
  className?: string;
}

export const PersonalizedDashboard: React.FC<PersonalizedDashboardProps> = ({ className }) => {
  const { visits, addresses, houses } = useData();
  const [personalStats, setPersonalStats] = useState<PersonalStats>({
    todayVisits: 0,
    todaySuccessful: 0,
    weeklyVisits: 0,
    weeklySuccessful: 0,
    totalVisits: 0,
    totalSuccessful: 0,
    currentStreak: 0,
    longestStreak: 0,
    averageVisitsPerDay: 0,
    successRate: 0,
    level: 1,
    experience: 0,
    experienceToNext: 100
  });
  
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([]);
  const [showAllAchievements, setShowAllAchievements] = useState(false);

  // Calculate personal statistics
  useEffect(() => {
    const today = new Date();
    const weekStart = startOfWeek(today, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });

    // Filter visits
    const todayVisits = visits.filter(v => isToday(new Date(v.timestamp)));
    const weeklyVisits = visits.filter(v => isThisWeek(new Date(v.timestamp), { weekStartsOn: 1 }));
    const successfulVisits = visits.filter(v => v.status !== 'N/A');
    const todaySuccessful = todayVisits.filter(v => v.status !== 'N/A');
    const weeklySuccessful = weeklyVisits.filter(v => v.status !== 'N/A');

    // Calculate streak
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    const checkDate = new Date(today);

    // Group visits by date
    const visitsByDate = new Map<string, boolean>();
    visits.forEach(visit => {
      const dateKey = format(new Date(visit.timestamp), 'yyyy-MM-dd');
      const hasSuccess = visit.status !== 'N/A';
      visitsByDate.set(dateKey, visitsByDate.get(dateKey) || hasSuccess);
    });

    // Calculate streaks
    for (let i = 0; i < 365; i++) {
      const dateKey = format(checkDate, 'yyyy-MM-dd');
      const hadSuccessfulVisit = visitsByDate.get(dateKey) || false;

      if (hadSuccessfulVisit) {
        tempStreak++;
        if (i === 0) currentStreak = tempStreak;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        if (i === 0) currentStreak = 0;
        tempStreak = 0;
      }

      checkDate.setDate(checkDate.getDate() - 1);
    }

    // Calculate experience and level
    const totalSuccessfulCount = successfulVisits.length;
    const experience = totalSuccessfulCount * 10 + currentStreak * 5;
    const level = Math.floor(experience / 100) + 1;
    const experienceToNext = (level * 100) - experience;

    // Calculate average visits per day
    const firstVisitDate = visits.length > 0 
      ? new Date(Math.min(...visits.map(v => new Date(v.timestamp).getTime())))
      : today;
    const daysSinceFirst = Math.max(1, differenceInDays(today, firstVisitDate) + 1);
    const averageVisitsPerDay = visits.length / daysSinceFirst;

    setPersonalStats({
      todayVisits: todayVisits.length,
      todaySuccessful: todaySuccessful.length,
      weeklyVisits: weeklyVisits.length,
      weeklySuccessful: weeklySuccessful.length,
      totalVisits: visits.length,
      totalSuccessful: successfulVisits.length,
      currentStreak,
      longestStreak,
      averageVisitsPerDay,
      successRate: visits.length > 0 ? (successfulVisits.length / visits.length) * 100 : 0,
      level,
      experience: experience % 100,
      experienceToNext
    });
  }, [visits]);

  // Store previous achievements for comparison
  const previousAchievementsRef = useRef<Achievement[]>([]);

  // Memoize achievement definitions to prevent recreation on every render
  const achievementDefinitions = useMemo(() => [
    {
      id: 'first-visit',
      title: 'Erster Besuch',
      description: 'Ersten Besuch erfolgreich abgeschlossen',
      icon: <Star className="h-6 w-6 text-yellow-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.totalSuccessful, 1),
      maxProgress: 1,
      isUnlocked: (stats: PersonalStats) => stats.totalSuccessful >= 1,
      rarity: 'common' as const,
      points: 10
    },
    {
      id: 'daily-goal',
      title: 'Tagesziel',
      description: '5 erfolgreiche Besuche an einem Tag',
      icon: <Target className="h-6 w-6 text-blue-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.todaySuccessful, 5),
      maxProgress: 5,
      isUnlocked: (stats: PersonalStats) => stats.todaySuccessful >= 5,
      rarity: 'common' as const,
      points: 25
    },
    {
      id: 'week-warrior',
      title: 'Wochenkämpfer',
      description: '25 erfolgreiche Besuche in einer Woche',
      icon: <Award className="h-6 w-6 text-purple-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.weeklySuccessful, 25),
      maxProgress: 25,
      isUnlocked: (stats: PersonalStats) => stats.weeklySuccessful >= 25,
      rarity: 'rare' as const,
      points: 100
    },
    {
      id: 'streak-master',
      title: 'Serienkönig',
      description: '7 Tage in Folge erfolgreich',
      icon: <Flame className="h-6 w-6 text-orange-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.currentStreak, 7),
      maxProgress: 7,
      isUnlocked: (stats: PersonalStats) => stats.currentStreak >= 7,
      rarity: 'rare' as const,
      points: 150
    },
    {
      id: 'century-club',
      title: 'Jahrhundert-Club',
      description: '100 erfolgreiche Besuche insgesamt',
      icon: <Trophy className="h-6 w-6 text-gold-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.totalSuccessful, 100),
      maxProgress: 100,
      isUnlocked: (stats: PersonalStats) => stats.totalSuccessful >= 100,
      rarity: 'epic' as const,
      points: 500
    },
    {
      id: 'perfectionist',
      title: 'Perfektionist',
      description: '95% Erfolgsrate bei mindestens 50 Besuchen',
      icon: <Crown className="h-6 w-6 text-yellow-600" />,
      getProgress: (stats: PersonalStats) => stats.totalVisits >= 50 ? Math.min(stats.successRate, 95) : 0,
      maxProgress: 95,
      isUnlocked: (stats: PersonalStats) => stats.totalVisits >= 50 && stats.successRate >= 95,
      rarity: 'legendary' as const,
      points: 1000
    },
    {
      id: 'speed-demon',
      title: 'Geschwindigkeitsdämon',
      description: '10 Besuche an einem Tag',
      icon: <Zap className="h-6 w-6 text-yellow-400" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.todayVisits, 10),
      maxProgress: 10,
      isUnlocked: (stats: PersonalStats) => stats.todayVisits >= 10,
      rarity: 'rare' as const,
      points: 200
    },
    {
      id: 'marathon-runner',
      title: 'Marathonläufer',
      description: '30 Tage Streak',
      icon: <Medal className="h-6 w-6 text-bronze-500" />,
      getProgress: (stats: PersonalStats) => Math.min(stats.currentStreak, 30),
      maxProgress: 30,
      isUnlocked: (stats: PersonalStats) => stats.currentStreak >= 30,
      rarity: 'legendary' as const,
      points: 2000
    }
  ], []);

  // Generate achievements based on current stats
  const currentAchievements = useMemo(() => {
    return achievementDefinitions.map(def => ({
      id: def.id,
      title: def.title,
      description: def.description,
      icon: def.icon,
      progress: def.getProgress(personalStats),
      maxProgress: def.maxProgress,
      unlocked: def.isUnlocked(personalStats),
      rarity: def.rarity,
      points: def.points
    }));
  }, [achievementDefinitions, personalStats]);

  // Handle achievement unlocks
  const handleAchievementUnlocks = useCallback(() => {
    const previousAchievements = previousAchievementsRef.current;
    const newlyUnlocked: Achievement[] = [];

    currentAchievements.forEach(achievement => {
      const previous = previousAchievements.find(a => a.id === achievement.id);
      if (achievement.unlocked && (!previous || !previous.unlocked)) {
        const unlockedAchievement = {
          ...achievement,
          unlockedAt: new Date().toISOString()
        };
        newlyUnlocked.push(unlockedAchievement);
      }
    });

    if (newlyUnlocked.length > 0) {
      setRecentAchievements(prev => [...newlyUnlocked, ...prev.slice(0, 2)]);
      newlyUnlocked.forEach(() => triggerHapticFeedback('success'));
    }

    // Update the ref for next comparison
    previousAchievementsRef.current = currentAchievements;
    setAchievements(currentAchievements);
  }, [currentAchievements]);

  // Update achievements when stats change
  useEffect(() => {
    handleAchievementUnlocks();
  }, [handleAchievementUnlocks]);

  const getRarityColor = useCallback((rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800 border-gray-300';
      case 'rare': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'epic': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'legendary': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  }, []);

  const getSuccessRateColor = useCallback((rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-blue-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  const unlockedAchievements = useMemo(() =>
    achievements.filter(a => a.unlocked),
    [achievements]
  );

  const totalPoints = useMemo(() =>
    unlockedAchievements.reduce((sum, a) => sum + a.points, 0),
    [unlockedAchievements]
  );

  return (
    <div className={className}>
      {/* Level and Experience */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Level {personalStats.level}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">Erfahrung</span>
              <span className="text-sm font-medium">
                {personalStats.experience}/100 XP
              </span>
            </div>
            <Progress value={personalStats.experience} className="h-3" />
            <div className="flex justify-between items-center text-sm text-muted-foreground">
              <span>{totalPoints} Punkte insgesamt</span>
              <span>{personalStats.experienceToNext} XP bis Level {personalStats.level + 1}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{personalStats.todayVisits}</div>
              <div className="text-sm text-muted-foreground">Besuche heute</div>
              <div className="text-xs text-green-600">
                {personalStats.todaySuccessful} erfolgreich
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getSuccessRateColor(personalStats.successRate)}`}>
                {personalStats.successRate.toFixed(0)}%
              </div>
              <div className="text-sm text-muted-foreground">Erfolgsrate</div>
              <div className="text-xs text-muted-foreground">
                {personalStats.totalSuccessful}/{personalStats.totalVisits}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <Flame className="h-5 w-5 text-orange-500" />
                <span className="text-2xl font-bold">{personalStats.currentStreak}</span>
              </div>
              <div className="text-sm text-muted-foreground">Tage Serie</div>
              <div className="text-xs text-muted-foreground">
                Rekord: {personalStats.longestStreak}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {personalStats.averageVisitsPerDay.toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">Ø pro Tag</div>
              <div className="text-xs text-muted-foreground">
                {personalStats.weeklyVisits} diese Woche
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Achievements */}
      {recentAchievements.length > 0 && (
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5 text-green-500" />
              Neue Erfolge!
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recentAchievements.map((achievement) => (
                <div key={achievement.id} className="flex items-center gap-3 p-2 bg-green-50 border border-green-200 rounded">
                  {achievement.icon}
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{achievement.title}</h4>
                    <p className="text-xs text-muted-foreground">{achievement.description}</p>
                  </div>
                  <Badge className={getRarityColor(achievement.rarity)}>
                    +{achievement.points} XP
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Achievements */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Erfolge ({unlockedAchievements.length}/{achievements.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAllAchievements(!showAllAchievements)}
            >
              {showAllAchievements ? 'Weniger' : 'Alle'}
              <ChevronRight className={`h-4 w-4 ml-1 transition-transform ${showAllAchievements ? 'rotate-90' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {(showAllAchievements ? achievements : achievements.slice(0, 4)).map((achievement) => (
              <div 
                key={achievement.id} 
                className={`flex items-center gap-3 p-3 rounded border ${
                  achievement.unlocked 
                    ? 'bg-white border-gray-200' 
                    : 'bg-gray-50 border-gray-100 opacity-60'
                }`}
              >
                <div className={achievement.unlocked ? '' : 'grayscale'}>
                  {achievement.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{achievement.title}</h4>
                  <p className="text-xs text-muted-foreground mb-1">{achievement.description}</p>
                  <div className="flex items-center gap-2">
                    <Progress 
                      value={(achievement.progress / achievement.maxProgress) * 100} 
                      className="h-1 flex-1"
                    />
                    <span className="text-xs text-muted-foreground">
                      {achievement.progress}/{achievement.maxProgress}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className={getRarityColor(achievement.rarity)}>
                    {achievement.rarity}
                  </Badge>
                  {achievement.unlocked && (
                    <div className="text-xs text-green-600 mt-1">
                      +{achievement.points} XP
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PersonalizedDashboard;
