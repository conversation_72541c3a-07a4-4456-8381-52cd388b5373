import{b as K,r as n,J as i,I as Q,j as e,B as c,k as W,C as X,i as Y,l as ee}from"./index-Cmt5neWh.js";import{b as ae,M as S,U as D,B as se}from"./MainLayout-wyzz138D.js";import{D as $,a as re,b as k,c as B,d as F}from"./dialog-BFTaoFLK.js";import{I as E}from"./input-BK13BBqa.js";import{L as l}from"./label-NwAA2N0T.js";import{T as A}from"./textarea-BM1-JMTm.js";import{S as h,a as u,b as j,c as p,d as g}from"./select-IVIdgARa.js";import{T as te,a as ie,b as I,c as m,d as le,e as o}from"./table-Bp6KGmPn.js";import{B as ne}from"./badge-XkNoLG2o.js";import{S as de}from"./switch-DOMi03D3.js";import{a as ce,g as me,d as oe,e as xe,f as he}from"./functions-DcGEt8N_.js";import{P as ue}from"./plus-C74OdLeW.js";import{S as L,T as je}from"./trash-2-BcpLsksG.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./chevron-down-I8tOk39n.js";import"./check-abM7k-xd.js";import"./client-DbI4l5kI.js";const Ae=()=>{const{user:N,users:z}=K(),r=ae(),[v,M]=n.useState([]),[T,O]=n.useState([]),[G,P]=n.useState(!0),[_,w]=n.useState(null),[V,y]=n.useState(!1),[H,b]=n.useState(!1),[s,t]=n.useState({name:"",description:"",area_id:"",team_leader_id:"",is_active:!0});n.useEffect(()=>{x()},[]);const x=async()=>{try{const[a,d]=await Promise.all([ce(),me()]);M(a||[]),O(d||[])}catch(a){console.error("Error loading data:",a),i.error("Fehler beim Laden der Daten")}finally{P(!1)}},f=z.filter(a=>a.role==="teamleiter"),J=async()=>{if(!s.name.trim()){i.error("Name ist erforderlich");return}try{await oe({name:s.name,description:s.description||void 0,area_id:s.area_id||void 0,team_leader_id:s.team_leader_id||void 0}),i.success(`Team "${s.name}" wurde erstellt`),t({name:"",description:"",area_id:"",team_leader_id:"",is_active:!0}),y(!1),x()}catch(a){console.error("Error creating team:",a),i.error("Fehler beim Erstellen des Teams")}},R=async()=>{if(!_||!s.name.trim()){i.error("Name ist erforderlich");return}try{await xe(_.id,{name:s.name,description:s.description||void 0,area_id:s.area_id||void 0,team_leader_id:s.team_leader_id||void 0,is_active:s.is_active}),i.success(`Team "${s.name}" wurde aktualisiert`),t({name:"",description:"",area_id:"",team_leader_id:"",is_active:!0}),w(null),b(!1),x()}catch(a){console.error("Error updating team:",a),i.error("Fehler beim Aktualisieren des Teams")}},U=async a=>{if(confirm(`Möchten Sie das Team "${a.name}" wirklich löschen?`))try{await he(a.id),i.success(`Team "${a.name}" wurde gelöscht`),x()}catch(d){console.error("Error deleting team:",d),i.error("Fehler beim Löschen des Teams")}},q=a=>{w(a),t({name:a.name,description:a.description||"",area_id:a.area_id||"",team_leader_id:a.team_leader_id||"",is_active:a.is_active??!0}),b(!0)};return!N||!Q(N)?e.jsx(S,{title:"Teams verwalten",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-600",children:"Sie haben keine Berechtigung, diese Seite anzuzeigen."})})})}):e.jsx(S,{title:"Teams verwalten",children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${r?"text-2xl":"text-4xl"}`,children:"Teams verwalten"}),e.jsx("p",{className:`text-gray-600 ${r?"text-sm":"text-lg"}`,children:"Erstellen und verwalten Sie Teams und deren Zuordnungen"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs($,{open:V,onOpenChange:y,children:[e.jsx(re,{asChild:!0,children:e.jsxs(c,{className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Neues Team"]})}),e.jsxs(k,{className:"sm:max-w-[500px]",children:[e.jsx(B,{children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-5 w-5 text-blue-600"}),"Neues Team erstellen"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"name",children:"Name*"}),e.jsx(E,{id:"name",value:s.name,onChange:a=>t({...s,name:a.target.value}),placeholder:"z.B. Team Stuttgart"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"description",children:"Beschreibung"}),e.jsx(A,{id:"description",value:s.description,onChange:a=>t({...s,description:a.target.value}),placeholder:"Beschreibung des Teams...",rows:3})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"area",children:"Gebiet"}),e.jsxs(h,{value:s.area_id,onValueChange:a=>t({...s,area_id:a}),children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Gebiet auswählen"})}),e.jsx(p,{children:T.map(a=>e.jsx(g,{value:a.id,children:a.name},a.id))})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"team_leader",children:"Teamleiter"}),e.jsxs(h,{value:s.team_leader_id,onValueChange:a=>t({...s,team_leader_id:a}),children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Teamleiter auswählen"})}),e.jsx(p,{children:f.map(a=>e.jsx(g,{value:a.id,children:a.name},a.id))})]})]}),e.jsx(c,{onClick:J,className:"w-full",children:"Team erstellen"})]})]})]})}),e.jsxs(W,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in",children:[e.jsx(X,{className:`${r?"p-4 pb-2":"p-6 pb-4"}`,children:e.jsxs(Y,{className:`flex items-center gap-3 ${r?"text-lg":"text-xl"} font-bold text-gray-800`,children:[e.jsx(se,{className:`${r?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Alle Teams (",v.length,")"]})}),e.jsx(ee,{className:`${r?"p-4 pt-0":"p-6 pt-0"}`,children:G?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Lade Teams..."})}):v.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(D,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Noch keine Teams erstellt"})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(te,{children:[e.jsx(ie,{children:e.jsxs(I,{children:[e.jsx(m,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Name"}),e.jsx(m,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Gebiet"}),e.jsx(m,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Teamleiter"}),e.jsx(m,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Status"}),e.jsx(m,{className:`${r?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Aktionen"})]})}),e.jsx(le,{children:v.map(a=>{var C;const d=f.find(Z=>Z.id===a.team_leader_id);return e.jsxs(I,{className:"hover:bg-blue-50/50 transition-colors",children:[e.jsx(o,{className:`${r?"text-xs":"text-sm"} font-medium text-gray-800`,children:a.name}),e.jsx(o,{className:`${r?"text-xs":"text-sm"} text-gray-700`,children:((C=a.areas)==null?void 0:C.name)||"-"}),e.jsx(o,{className:`${r?"text-xs":"text-sm"} text-gray-700`,children:(d==null?void 0:d.name)||"-"}),e.jsx(o,{children:e.jsx(ne,{variant:a.is_active?"default":"secondary",children:a.is_active?"Aktiv":"Inaktiv"})}),e.jsx(o,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>q(a),className:"text-blue-600 border-blue-200 hover:bg-blue-50",children:e.jsx(L,{className:"h-3 w-3"})}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>U(a),className:"text-red-600 border-red-200 hover:bg-red-50",children:e.jsx(je,{className:"h-3 w-3"})})]})})]},a.id)})})]})})})]}),e.jsx($,{open:H,onOpenChange:b,children:e.jsxs(k,{className:"sm:max-w-[500px]",children:[e.jsx(B,{children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5 text-blue-600"}),"Team bearbeiten"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"edit-name",children:"Name*"}),e.jsx(E,{id:"edit-name",value:s.name,onChange:a=>t({...s,name:a.target.value}),placeholder:"z.B. Team Stuttgart"})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"edit-description",children:"Beschreibung"}),e.jsx(A,{id:"edit-description",value:s.description,onChange:a=>t({...s,description:a.target.value}),placeholder:"Beschreibung des Teams...",rows:3})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"edit-area",children:"Gebiet"}),e.jsxs(h,{value:s.area_id,onValueChange:a=>t({...s,area_id:a}),children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Gebiet auswählen"})}),e.jsx(p,{children:T.map(a=>e.jsx(g,{value:a.id,children:a.name},a.id))})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"edit-team-leader",children:"Teamleiter"}),e.jsxs(h,{value:s.team_leader_id,onValueChange:a=>t({...s,team_leader_id:a}),children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Teamleiter auswählen"})}),e.jsx(p,{children:f.map(a=>e.jsx(g,{value:a.id,children:a.name},a.id))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(de,{id:"edit-active",checked:s.is_active,onCheckedChange:a=>t({...s,is_active:a})}),e.jsx(l,{htmlFor:"edit-active",children:"Team ist aktiv"})]}),e.jsx(c,{onClick:R,className:"w-full",children:"Änderungen speichern"})]})]})})]})})})};export{Ae as default};
