import{N as i,j as t}from"./index-Cmt5neWh.js";import{M as r}from"./MainLayout-wyzz138D.js";import{E as s}from"./EFHVisitTracker-BC_kU7m6.js";import"./input-BK13BBqa.js";import"./label-NwAA2N0T.js";import"./Button-ETlvKXsU.js";import"./loader-circle-Brkx1kW_.js";import"./Card-CELKqcN7.js";import"./badge-XkNoLG2o.js";import"./SimpleStatusButtons-CtnyXuDf.js";import"./clock-DhYcPjhn.js";import"./dialog-BFTaoFLK.js";import"./popover-BPm3A8JC.js";import"./subDays-BLJlWEqr.js";import"./useSwipeGestures-49dHBwDT.js";const y=()=>{const{visitId:e}=i();return e?t.jsx(r,{title:"Besuchsstatus",children:t.jsx("div",{className:"min-h-full flex items-start justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50",children:t.jsx("div",{className:"w-full max-w-2xl",children:t.jsx(s,{visitId:e})})})}):t.jsx(r,{title:"Besuchsstatus",children:t.jsx("div",{className:"min-h-full flex items-center justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50",children:t.jsxs("div",{className:"text-center",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Keine Besuchs-ID gefunden"}),t.jsx("p",{className:"text-gray-600",children:"Bitte gehen Sie zurück zur Startseite und versuchen Sie es erneut."})]})})})};export{y as default};
