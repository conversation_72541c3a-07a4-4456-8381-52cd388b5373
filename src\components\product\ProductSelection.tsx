
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useData } from '@/context/data';
import { ProductCategory } from '@/types';
import { toast } from 'sonner';
import { ProductSelectionContent } from './ProductSelectionContent';
import { NotFoundError } from '@/design-system/components/ErrorMessage';
import { LoadingOverlay } from '@/design-system/components/Loading';
import ErrorBoundary from '@/components/ErrorBoundary';

interface ProductSelectionProps {
  visitId?: string;
}

const ProductSelection: React.FC<ProductSelectionProps> = ({ visitId: propVisitId }) => {
  const { visitId: paramVisitId } = useParams<{ visitId: string }>();
  const navigate = useNavigate();

  // Use prop visitId if provided, otherwise use URL param
  const visitId = propVisitId || paramVisitId;
  const {
    addProduct,
    visits,
    getDoorsByVisit,
    getHouseById,
    getAddressById
  } = useData();

  const [selectedDoors, setSelectedDoors] = useState<string[]>([]);
  const [products, setProducts] = useState<{category: ProductCategory; type: string; quantity: number}[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get data early to use in hooks
  const visit = visitId ? visits.find(v => v.id === visitId) : null;
  const doors = visitId ? getDoorsByVisit(visitId) : [];
  const salesDoors = doors.filter(door => door.status === 'Angetroffen → Sale');
  const house = visit ? getHouseById(visit.houseId) : null;
  const address = house ? getAddressById(house.addressId) : null;

  // Auto-select single door for EFH or no auto-selection for MFH
  useEffect(() => {
    if (salesDoors.length === 1 && selectedDoors.length === 0) {
      console.log('Auto-selecting single sales door:', salesDoors[0]!.id);
      setSelectedDoors([salesDoors[0]!.id]);
    }
  }, [salesDoors, selectedDoors.length]);

  console.log('Doors für Visit:', doors);
  console.log('Sales Doors:', salesDoors);
  console.log('Selected Doors:', selectedDoors);

  // Validate visitId
  if (!visitId) {
    return (
      <NotFoundError
        title="Besuch nicht gefunden"
        message="Keine Besuchs-ID verfügbar. Bitte starten Sie einen neuen Besuch."
        onGoHome={() => navigate('/')}
        onGoBack={() => navigate(-1)}
      />
    );
  }

  if (!visit) {
    return (
      <NotFoundError
        title="Besuch nicht gefunden"
        message={`Der Besuch mit ID "${visitId}" konnte nicht gefunden werden.`}
        onGoHome={() => navigate('/')}
        onGoBack={() => navigate(-1)}
      />
    );
  }

  if (!house) {
    return (
      <NotFoundError
        title="Haus nicht gefunden"
        message={`Das Haus mit ID "${visit.houseId}" konnte nicht gefunden werden.`}
        onGoHome={() => navigate('/')}
        onGoBack={() => navigate(-1)}
      />
    );
  }

  if (!address) {
    return (
      <NotFoundError
        title="Adresse nicht gefunden"
        message={`Die Adresse mit ID "${house.addressId}" konnte nicht gefunden werden.`}
        onGoHome={() => navigate('/')}
        onGoBack={() => navigate(-1)}
      />
    );
  }

  const addProductItem = (category: ProductCategory) => {
    const defaultOption = {
      category,
      type: category === 'KIP' ? 'KIP Standard' :
            category === 'TV' ? 'TV Connect Standard' : 'Giga Mobil S',
      quantity: 1
    };

    // Wenn KIP ausgewählt wird, automatisch auch TV Connect Standard hinzufügen
    if (category === 'KIP') {
      const tvConnectOption = {
        category: 'TV' as ProductCategory,
        type: 'TV Connect Standard',
        quantity: 1
      };

      // Prüfen ob TV Connect Standard bereits existiert
      const hasExistingTVConnect = products.some(p =>
        p.category === 'TV' && p.type === 'TV Connect Standard'
      );

      if (!hasExistingTVConnect) {
        setProducts([...products, defaultOption, tvConnectOption]);
        console.log('KIP + TV Connect Standard automatisch hinzugefügt:', [defaultOption, tvConnectOption]);
        toast.success('🎯 KIP Standard + TV Connect Standard automatisch hinzugefügt!');
      } else {
        setProducts([...products, defaultOption]);
        console.log('KIP hinzugefügt (TV Connect Standard bereits vorhanden):', defaultOption);
        toast.success('✅ KIP Standard hinzugefügt');
      }
    } else {
      setProducts([...products, defaultOption]);
      console.log('Product hinzugefügt:', defaultOption);
    }
  };

  const removeProductItem = (index: number) => {
    const newProducts = [...products];
    newProducts.splice(index, 1);
    setProducts(newProducts);
    console.log('Product entfernt, neue Liste:', newProducts);
  };

  const updateProductType = (index: number, type: string) => {
    const newProducts = [...products];
    newProducts[index].type = type;
    setProducts(newProducts);
  };

  const updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1) return;
    
    const newProducts = [...products];
    newProducts[index].quantity = quantity;
    setProducts(newProducts);
  };

  const handleDoorToggle = (doorId: string) => {
    setSelectedDoors(prev => {
      if (prev.includes(doorId)) {
        return prev.filter(id => id !== doorId);
      } else {
        return [...prev, doorId];
      }
    });
  };

  const handleSubmit = async () => {
    if (selectedDoors.length === 0) {
      toast.error('Bitte wählen Sie mindestens eine Tür aus');
      return;
    }

    console.log('Submitting mit selectedDoors:', selectedDoors, 'und products:', products);
    setIsSubmitting(true);

    try {
      let totalProductsSaved = 0;
      
      if (products.length > 0) {
        for (const doorId of selectedDoors) {
          for (const product of products) {
            addProduct({
              doorId: doorId,
              category: product.category,
              type: product.type,
              quantity: product.quantity,
            });
            totalProductsSaved++;
          }
        }
        toast.success(`${totalProductsSaved} Produkte für ${selectedDoors.length} Tür(en) erfolgreich gespeichert`);
      } else {
        toast.success(`Besuch für ${selectedDoors.length} Tür(en) ohne Produkte gespeichert`);
      }

      navigate('/daily-view');
    } catch (error) {
      console.error(error);
      toast.error('Fehler beim Speichern der Produkte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <ErrorBoundary>
      <LoadingOverlay isLoading={isSubmitting} text="Speichert Produkte...">
        <ProductSelectionContent
          address={address}
          house={house}
          salesDoors={salesDoors}
          selectedDoors={selectedDoors}
          products={products}
          isSubmitting={isSubmitting}
          onDoorToggle={handleDoorToggle}
          onAddProduct={addProductItem}
          onRemoveProduct={removeProductItem}
          onUpdateProductType={updateProductType}
          onUpdateProductQuantity={updateProductQuantity}
          onSubmit={handleSubmit}
          onBack={handleBack}
        />
      </LoadingOverlay>
    </ErrorBoundary>
  );
};

export default ProductSelection;
