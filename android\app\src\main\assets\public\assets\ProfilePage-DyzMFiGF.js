import{c as ne,b as _,r as g,j as e,k as n,l as c,C as i,i as d,t as $,B as j,J as V,m as ee,T as ce,u as ie,w as de,x as xe,y as W}from"./index-Cmt5neWh.js";import{b as G,S as y,U as S,C as K,f as R,d as z,a as O,B as J,e as L,T as me,M as oe}from"./MainLayout-wyzz138D.js";import{U as E,I as T}from"./input-BK13BBqa.js";import{L as A}from"./label-NwAA2N0T.js";import{M as Z}from"./mail-4oZ8O593.js";import{U as he}from"./user-check-BIUVRotN.js";import{S as ge,a as je,b as ue,c as be,d as pe}from"./select-IVIdgARa.js";import{T as Ne}from"./target-Cbp8QShB.js";import{C as fe}from"./circle-check-big-DK6RP7UF.js";import{M as ye}from"./MentorDashboard-CXkcQl70.js";import{T as we}from"./TeamleiterDashboard-B_jNRP8o.js";import{G as ve}from"./GebietsmanagerDashboard-DqTFTS2M.js";import{T as q,a as Q,b as B,c as N,d as X,e as f}from"./table-Bp6KGmPn.js";import{T as $e,a as ke,b as P,c as D}from"./tabs-BJh52NhZ.js";import{B as U}from"./badge-XkNoLG2o.js";import{g as Se,a as Ce,b as Te,c as Ae}from"./functions-DcGEt8N_.js";import{A as Pe,U as De}from"./upload-CWJxBjPA.js";import{D as Y}from"./database-DPAjLTxQ.js";import{D as Me}from"./download-BHlV_KY3.js";import{R as Ie}from"./rotate-ccw-YT9XFzuk.js";import"./index-C8JwcrUT.js";import"./index-CEhV84jC.js";import"./chevron-down-I8tOk39n.js";import"./check-abM7k-xd.js";import"./eye-DwLHs0eg.js";import"./client-DbI4l5kI.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=ne("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),Re=()=>{const{user:r,updateUser:w}=_(),t=G(),[p,l]=g.useState((r==null?void 0:r.name)||""),[x,s]=g.useState((r==null?void 0:r.email)||""),[v,m]=g.useState(""),[b,o]=g.useState(""),[k,M]=g.useState(""),[I,C]=g.useState(!1);if(!r)return e.jsx("div",{className:`${t?"px-4 py-6":"p-6"} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`,children:e.jsx(n,{className:"glass-card rounded-3xl border-0 shadow-2xl",children:e.jsx(c,{className:"pt-6",children:e.jsx("p",{className:"text-center text-muted-foreground",children:"Bitte melden Sie sich an, um Ihr Profil zu sehen."})})})});const F=()=>{if(b&&b!==k){V.error("Die Passwörter stimmen nicht überein.");return}w({...r,name:p,email:x,...b?{password:b}:{}}),C(!1),m(""),o(""),M(""),V.success("Profil erfolgreich aktualisiert.")};return e.jsxs("div",{className:`space-y-6 ${t?"px-4 py-6":"p-6"} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`,children:[e.jsxs(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:[e.jsxs(i,{className:`${t?"pb-4":"pb-6"}`,children:[e.jsxs(d,{className:`${t?"text-lg":"text-xl"} font-bold text-gray-800 flex items-center gap-3`,children:[e.jsx(E,{className:`${t?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Persönliche Informationen"]}),e.jsx($,{className:`${t?"text-sm":"text-base"}`,children:"Verwalten Sie Ihre persönlichen Daten und Passwort"})]}),e.jsx(c,{className:`${t?"pt-0 pb-6":"pt-0 pb-8"}`,children:e.jsx("div",{className:"space-y-4",children:I?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(A,{htmlFor:"name",className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4 text-blue-600"}),"Name"]}),e.jsx(T,{id:"name",value:p,onChange:u=>l(u.target.value),className:`${t?"h-12":"h-10"} rounded-xl border-2 focus:border-blue-500`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(A,{htmlFor:"email",className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-4 w-4 text-green-600"}),"Email"]}),e.jsx(T,{id:"email",type:"email",value:x,onChange:u=>s(u.target.value),className:`${t?"h-12":"h-10"} rounded-xl border-2 focus:border-green-500`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(A,{htmlFor:"currentPassword",className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4 text-orange-600"}),"Aktuelles Passwort"]}),e.jsx(T,{id:"currentPassword",type:"password",value:v,onChange:u=>m(u.target.value),className:`${t?"h-12":"h-10"} rounded-xl border-2 focus:border-orange-500`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(A,{htmlFor:"newPassword",className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4 text-red-600"}),"Neues Passwort"]}),e.jsx(T,{id:"newPassword",type:"password",value:b,onChange:u=>o(u.target.value),className:`${t?"h-12":"h-10"} rounded-xl border-2 focus:border-red-500`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(A,{htmlFor:"confirmPassword",className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4 text-red-600"}),"Passwort bestätigen"]}),e.jsx(T,{id:"confirmPassword",type:"password",value:k,onChange:u=>M(u.target.value),className:`${t?"h-12":"h-10"} rounded-xl border-2 focus:border-red-500`})]}),e.jsxs("div",{className:`flex ${t?"flex-col gap-3":"justify-end"} space-x-0 ${t?"":"space-x-2"} pt-4`,children:[e.jsx(j,{variant:"outline",onClick:()=>C(!1),className:`${t?"h-12 text-base":"h-10"} rounded-xl border-2 border-gray-300 hover:border-gray-500 transition-all duration-200`,children:"Abbrechen"}),e.jsx(j,{onClick:F,className:`${t?"h-12 text-base":"h-10"} rounded-xl bg-blue-600 hover:bg-blue-700 transition-all duration-200`,children:"Speichern"})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:`flex ${t?"flex-col gap-2":"justify-between items-center"} p-4 bg-gray-50 rounded-xl`,children:[e.jsxs("span",{className:"font-medium flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4 text-blue-600"}),"Name:"]}),e.jsx("span",{className:`${t?"text-lg":""} font-semibold text-gray-800`,children:r.name})]}),e.jsxs("div",{className:`flex ${t?"flex-col gap-2":"justify-between items-center"} p-4 bg-gray-50 rounded-xl`,children:[e.jsxs("span",{className:"font-medium flex items-center gap-2",children:[e.jsx(Z,{className:"h-4 w-4 text-green-600"}),"Email:"]}),e.jsx("span",{className:`${t?"text-lg":""} font-semibold text-gray-800`,children:r.email})]})]}),e.jsx("div",{className:`flex ${t?"w-full":"justify-end"} pt-4`,children:e.jsxs(j,{onClick:()=>C(!0),className:`${t?"h-12 text-base w-full":"h-10"} rounded-xl bg-blue-600 hover:bg-blue-700 transition-all duration-200 flex items-center gap-2`,children:[e.jsx(Be,{className:"h-4 w-4"}),"Bearbeiten"]})})]})})})]}),e.jsxs(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:[e.jsxs(i,{className:`${t?"pb-4":"pb-6"}`,children:[e.jsxs(d,{className:`${t?"text-lg":"text-xl"} font-bold text-gray-800 flex items-center gap-3`,children:[e.jsx(y,{className:`${t?"h-5 w-5":"h-6 w-6"} text-orange-600`}),"Rolle und Team"]}),e.jsx($,{className:`${t?"text-sm":"text-base"}`,children:"Informationen zu Ihrer Rolle und Team"})]}),e.jsx(c,{className:`${t?"pt-0 pb-6":"pt-0 pb-8"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:`flex ${t?"flex-col gap-2":"justify-between items-center"} p-4 bg-gray-50 rounded-xl`,children:[e.jsxs("span",{className:"font-medium flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4 text-orange-600"}),"Rolle:"]}),e.jsx("span",{className:`${t?"text-lg":""} font-semibold text-orange-600 bg-orange-100 px-3 py-1 rounded-lg`,children:r.role})]}),r.teamId&&e.jsxs("div",{className:`flex ${t?"flex-col gap-2":"justify-between items-center"} p-4 bg-gray-50 rounded-xl`,children:[e.jsxs("span",{className:"font-medium flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4 text-green-600"}),"Team ID:"]}),e.jsx("span",{className:`${t?"text-lg":""} font-semibold text-green-600 bg-green-100 px-3 py-1 rounded-lg`,children:r.teamId})]}),r.mentorId&&e.jsxs("div",{className:`flex ${t?"flex-col gap-2":"justify-between items-center"} p-4 bg-gray-50 rounded-xl`,children:[e.jsxs("span",{className:"font-medium flex items-center gap-2",children:[e.jsx(he,{className:"h-4 w-4 text-blue-600"}),"Mentor ID:"]}),e.jsx("span",{className:`${t?"text-lg":""} font-semibold text-blue-600 bg-blue-100 px-3 py-1 rounded-lg`,children:r.mentorId})]})]})})]})]})},ze=({userId:r})=>{const{visits:w,doors:t,products:p}=ee(),l=G(),x=w.filter(o=>o.userId===r),s=t.filter(o=>o.status==="Angetroffen → Termin"||o.status==="Angetroffen → Kein Interesse"||o.status==="Angetroffen → Sale"),v=p.filter(o=>o.userId===r),m=new Date().toISOString().split("T")[0],b=x.filter(o=>o.timestamp.startsWith(m));return e.jsxs("div",{className:`space-y-6 ${l?"px-4 py-6":"p-6"} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`,children:[e.jsx(n,{className:"glass-card rounded-3xl border-0 shadow-2xl",children:e.jsx(c,{className:`${l?"p-6":"p-8"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:`${l?"text-2xl":"text-3xl"} font-bold text-gray-800 mb-2`,children:"Berater Dashboard"}),e.jsx("p",{className:`text-muted-foreground ${l?"text-sm":"text-base"}`,children:"Willkommen zurück! Hier ist Ihre aktuelle Übersicht."})]})})}),e.jsxs("div",{className:`grid ${l?"grid-cols-2 gap-4":"grid-cols-4 gap-6"}`,children:[e.jsx(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:e.jsxs(c,{className:`${l?"p-4":"p-6"} text-center`,children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(S,{className:`${l?"h-6 w-6":"h-8 w-8"} text-blue-600`})}),e.jsx("p",{className:`text-xs text-muted-foreground mb-2 ${l?"text-xs":"text-sm"}`,children:"Gesamtbesuche"}),e.jsx("p",{className:`${l?"text-2xl":"text-3xl"} font-bold text-gray-800`,children:x.length})]})}),e.jsx(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:e.jsxs(c,{className:`${l?"p-4":"p-6"} text-center`,children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(Ne,{className:`${l?"h-6 w-6":"h-8 w-8"} text-orange-600`})}),e.jsx("p",{className:`text-xs text-muted-foreground mb-2 ${l?"text-xs":"text-sm"}`,children:"Angetroffen"}),e.jsx("p",{className:`${l?"text-2xl":"text-3xl"} font-bold text-gray-800`,children:s.length})]})}),e.jsx(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:e.jsxs(c,{className:`${l?"p-4":"p-6"} text-center`,children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(fe,{className:`${l?"h-6 w-6":"h-8 w-8"} text-green-600`})}),e.jsx("p",{className:`text-xs text-muted-foreground mb-2 ${l?"text-xs":"text-sm"}`,children:"Verkäufe"}),e.jsx("p",{className:`${l?"text-2xl":"text-3xl"} font-bold text-green-600`,children:v.length})]})}),e.jsx(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in",children:e.jsxs(c,{className:`${l?"p-4":"p-6"} text-center`,children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(K,{className:`${l?"h-6 w-6":"h-8 w-8"} text-red-600`})}),e.jsx("p",{className:`text-xs text-muted-foreground mb-2 ${l?"text-xs":"text-sm"}`,children:"Heute"}),e.jsx("p",{className:`${l?"text-2xl":"text-3xl"} font-bold text-red-600`,children:b.length}),e.jsx("p",{className:`${l?"text-xs":"text-sm"} text-muted-foreground mt-1`,children:"Besuche"})]})})]}),e.jsxs(n,{className:"glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-slide-in",children:[e.jsxs(i,{className:`${l?"pb-4":"pb-6"}`,children:[e.jsxs(d,{className:`${l?"text-lg":"text-xl"} font-bold text-gray-800 flex items-center gap-3`,children:[e.jsx(K,{className:`${l?"h-5 w-5":"h-6 w-6"} text-blue-600`}),"Aktuelle Periode"]}),e.jsx($,{className:`${l?"text-sm":"text-base"}`,children:R(new Date,"'Woche vom' d. MMMM yyyy",{locale:z})})]}),e.jsx(c,{className:`${l?"pt-0 pb-6":"pt-0 pb-8"}`,children:e.jsx("p",{className:`${l?"text-sm":"text-base"} text-gray-700`,children:"Tragen Sie weiterhin Ihre Besuche ein und pflegen Sie die Ergebnisse."})})]})]})},Fe=()=>{const{users:r}=_(),{addresses:w,houses:t,visits:p,doors:l,products:x}=ee(),s=G(),[v,m]=g.useState([]),[b,o]=g.useState([]),[k,M]=g.useState([]),[I,C]=g.useState([]),[F,u]=g.useState(!0);g.useEffect(()=>{se()},[]);const se=async()=>{try{const[a,h,H,re]=await Promise.all([Se(),Ce(),Te(20),Ae()]);m(a||[]),o(h||[]),M(H||[]),C(re||[])}catch(a){console.error("Error loading admin data:",a),V.error("Fehler beim Laden der Admin-Daten")}finally{u(!1)}},ae={admin:r.filter(a=>a.role==="admin").length,gebietsmanager:r.filter(a=>a.role==="gebietsmanager").length,teamleiter:r.filter(a=>a.role==="teamleiter").length,mentor:r.filter(a=>a.role==="mentor").length,berater:r.filter(a=>a.role==="berater").length},te={kip:x.filter(a=>a.category==="KIP").length,tv:x.filter(a=>a.category==="TV").length,mobile:x.filter(a=>a.category==="Mobile").length},le=[{title:"Gebiete",value:v.length,icon:O,color:"from-blue-500 to-blue-600",textColor:"text-blue-600"},{title:"Teams",value:b.length,icon:J,color:"from-purple-500 to-purple-600",textColor:"text-purple-600"},{title:"Aktive Benutzer",value:I.filter(a=>a.is_active!==!1).length,icon:S,color:"from-green-500 to-green-600",textColor:"text-green-600"},{title:"Admin Aktionen",value:k.length,icon:y,color:"from-orange-500 to-orange-600",textColor:"text-orange-600"}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6 md:space-y-8",children:[e.jsxs("div",{className:"text-center md:text-left space-y-2 md:space-y-4 animate-fade-in",children:[e.jsx("h1",{className:`font-bold text-gray-800 ${s?"text-2xl":"text-4xl"}`,children:"Admin Dashboard"}),e.jsx("p",{className:`text-gray-600 ${s?"text-sm":"text-lg"}`,children:"Systemverwaltung und Übersichten"}),e.jsx("p",{className:`text-gray-500 ${s?"text-xs":"text-sm"}`,children:R(new Date,"'Stand:' d. MMMM yyyy",{locale:z})})]}),e.jsx("div",{className:`grid gap-4 md:gap-6 ${s?"grid-cols-2":"grid-cols-2 md:grid-cols-4"}`,children:le.map((a,h)=>e.jsx(n,{className:`glass-card hover-lift ${s?"p-3":"p-4"} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`,style:{animationDelay:`${h*.1}s`},children:e.jsx(c,{className:"p-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:`font-medium text-gray-600 ${s?"text-xs":"text-sm"}`,children:a.title}),e.jsx("p",{className:`font-bold ${a.textColor} ${s?"text-xl":"text-3xl"}`,children:a.value})]}),e.jsx("div",{className:`rounded-2xl bg-gradient-to-br ${a.color} p-2 shadow-lg`,children:e.jsx(a.icon,{className:`${s?"h-5 w-5":"h-6 w-6"} text-white`})})]})})},a.title))}),e.jsxs($e,{defaultValue:"users",className:"w-full",children:[e.jsxs(ke,{className:`grid w-full ${s?"grid-cols-2":"grid-cols-5"} glass-card rounded-2xl bg-white/90 backdrop-blur-sm`,children:[e.jsxs(P,{value:"users",className:"flex items-center space-x-2 rounded-xl",children:[e.jsx(S,{className:"h-4 w-4"}),e.jsx("span",{className:s?"hidden":"inline",children:"Benutzer"})]}),e.jsxs(P,{value:"activity",className:"flex items-center space-x-2 rounded-xl",children:[e.jsx(Pe,{className:"h-4 w-4"}),e.jsx("span",{className:s?"hidden":"inline",children:"Aktivitäten"})]}),e.jsxs(P,{value:"data",className:"flex items-center space-x-2 rounded-xl",children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsx("span",{className:s?"hidden":"inline",children:"Daten"})]}),e.jsxs(P,{value:"audit",className:"flex items-center space-x-2 rounded-xl",children:[e.jsx(y,{className:"h-4 w-4"}),e.jsx("span",{className:s?"hidden":"inline",children:"Audit"})]}),e.jsxs(P,{value:"system",className:"flex items-center space-x-2 rounded-xl",children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{className:s?"hidden":"inline",children:"System"})]})]}),e.jsxs(D,{value:"users",className:"space-y-4 mt-4",children:[e.jsx("div",{className:`grid gap-4 ${s?"grid-cols-2":"grid-cols-5"}`,children:Object.entries(ae).map(([a,h])=>e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:`${s?"text-xs":"text-sm"} font-medium text-gray-600 capitalize`,children:a})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-blue-600`,children:h})})]},a))}),e.jsxs(n,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-5 w-5 text-blue-600"}),"Benutzerübersicht"]}),e.jsx($,{children:"Verwaltung aller Benutzer im System"})]}),e.jsx(c,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(q,{children:[e.jsx(Q,{children:e.jsxs(B,{children:[e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Name"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"E-Mail"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Rolle"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Team"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Status"})]})}),e.jsx(X,{children:r.slice(0,10).map(a=>{const h=I.find(H=>H.id===a.id);return e.jsxs(B,{className:"hover:bg-blue-50/50 transition-colors",children:[e.jsx(f,{className:`${s?"text-xs":"text-sm"} font-medium`,children:a.name}),e.jsx(f,{className:`${s?"text-xs":"text-sm"}`,children:a.email}),e.jsx(f,{children:e.jsx(U,{variant:"outline",className:"capitalize text-xs",children:a.role})}),e.jsx(f,{className:`${s?"text-xs":"text-sm"}`,children:a.teamId||"-"}),e.jsx(f,{children:e.jsx(U,{variant:(h==null?void 0:h.is_active)!==!1?"default":"secondary",className:"text-xs",children:(h==null?void 0:h.is_active)!==!1?"Aktiv":"Inaktiv"})})]},a.id)})})]})})})]})]}),e.jsxs(D,{value:"activity",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:`grid gap-4 ${s?"grid-cols-1":"grid-cols-3"}`,children:[e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:"text-sm font-medium text-gray-600",children:"Besuche"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-blue-600`,children:p.length})})]}),e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:"text-sm font-medium text-gray-600",children:"Türen"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-purple-600`,children:l.length})})]}),e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:"text-sm font-medium text-gray-600",children:"Verkäufe"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-green-600`,children:x.length})})]})]}),e.jsxs(n,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(me,{className:"h-5 w-5 text-green-600"}),"Produktverteilung"]}),e.jsx($,{children:"Übersicht aller verkauften Produkte nach Kategorie"})]}),e.jsx(c,{children:e.jsx("div",{className:`grid gap-4 ${s?"grid-cols-1":"grid-cols-3"}`,children:Object.entries(te).map(([a,h])=>e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:"text-sm font-medium text-gray-600 uppercase",children:a})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-green-600`,children:h})})]},a))})})]})]}),e.jsxs(D,{value:"data",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:`grid gap-4 ${s?"grid-cols-2":"grid-cols-4"}`,children:[e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:`${s?"text-xs":"text-sm"} font-medium text-gray-600`,children:"Adressen"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-blue-600`,children:w.length})})]}),e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:`${s?"text-xs":"text-sm"} font-medium text-gray-600`,children:"Häuser"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-purple-600`,children:t.length})})]}),e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:`${s?"text-xs":"text-sm"} font-medium text-gray-600`,children:"EFH"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-green-600`,children:t.filter(a=>a.type==="EFH").length})})]}),e.jsxs(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[e.jsx(i,{className:"pb-2",children:e.jsx(d,{className:`${s?"text-xs":"text-sm"} font-medium text-gray-600`,children:"MFH"})}),e.jsx(c,{children:e.jsx("p",{className:`${s?"text-2xl":"text-3xl"} font-bold text-orange-600`,children:t.filter(a=>a.type==="MFH").length})})]})]}),e.jsxs(n,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5 text-blue-600"}),"Datenübersicht"]}),e.jsx($,{children:R(new Date,"'Stand:' d. MMMM yyyy",{locale:z})})]}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:`${s?"text-sm":"text-base"}`,children:"In der Datenbank befinden sich:"}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 ${s?"text-sm":"text-base"}`,children:[e.jsxs("li",{children:[w.length," Adressen"]}),e.jsxs("li",{children:[t.length," Häuser (",t.filter(a=>a.type==="EFH").length," EFH, ",t.filter(a=>a.type==="MFH").length," MFH)"]}),e.jsxs("li",{children:[p.length," Besuche"]}),e.jsxs("li",{children:[l.length," erfasste Türen"]}),e.jsxs("li",{children:[x.length," verkaufte Produkte"]}),e.jsxs("li",{children:[v.length," Gebiete"]}),e.jsxs("li",{children:[b.length," Teams"]})]})]})})]})]}),e.jsx(D,{value:"audit",className:"space-y-4 mt-4",children:e.jsxs(n,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5 text-orange-600"}),"Admin-Aktionen Protokoll"]}),e.jsxs($,{children:["Letzte ",k.length," Admin-Aktionen im System"]})]}),e.jsx(c,{children:F?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Lade Audit-Logs..."})}):k.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(y,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Noch keine Admin-Aktionen protokolliert"})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(q,{children:[e.jsx(Q,{children:e.jsxs(B,{children:[e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Aktion"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Typ"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Beschreibung"}),e.jsx(N,{className:`${s?"text-xs":"text-sm"} font-semibold text-gray-700`,children:"Datum"})]})}),e.jsx(X,{children:k.map(a=>e.jsxs(B,{className:"hover:bg-blue-50/50 transition-colors",children:[e.jsx(f,{children:e.jsx(U,{variant:"outline",className:"text-xs",children:a.action_type})}),e.jsx(f,{className:`${s?"text-xs":"text-sm"}`,children:a.target_type}),e.jsx(f,{className:`${s?"text-xs":"text-sm"}`,children:a.description||"-"}),e.jsx(f,{className:`${s?"text-xs":"text-sm"}`,children:R(new Date(a.created_at),"dd.MM.yyyy HH:mm",{locale:z})})]},a.id))})]})})})]})}),e.jsx(D,{value:"system",className:"space-y-4 mt-4",children:e.jsxs(n,{className:"glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5 text-blue-600"}),"Systemkonfiguration"]}),e.jsx($,{children:"Verwaltung der Systemeinstellungen und Wartungsoptionen"})]}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-2 md:grid-cols-2",children:[e.jsxs(j,{variant:"outline",className:"flex items-center gap-2",children:[e.jsx(Me,{className:"h-4 w-4"}),"Daten exportieren"]}),e.jsxs(j,{variant:"outline",className:"flex items-center gap-2",children:[e.jsx(De,{className:"h-4 w-4"}),"Daten importieren"]}),e.jsxs(j,{variant:"outline",className:"flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50",children:[e.jsx(Ie,{className:"h-4 w-4"}),"Cache leeren"]}),e.jsxs(j,{variant:"outline",className:"flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50",children:[e.jsx(ce,{className:"h-4 w-4"}),"System zurücksetzen"]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Schnellzugriff"}),e.jsxs("div",{className:"grid gap-2 md:grid-cols-2",children:[e.jsxs(j,{variant:"ghost",className:"justify-start",onClick:()=>window.open("/area-management","_blank"),children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Gebiete verwalten"]}),e.jsxs(j,{variant:"ghost",className:"justify-start",onClick:()=>window.open("/team-management","_blank"),children:[e.jsx(J,{className:"h-4 w-4 mr-2"}),"Teams verwalten"]}),e.jsxs(j,{variant:"ghost",className:"justify-start",onClick:()=>window.open("/user-management","_blank"),children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Benutzer verwalten"]}),e.jsxs(j,{variant:"ghost",className:"justify-start",onClick:()=>window.open("/settings","_blank"),children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Einstellungen"]})]})]})]})})]})})]})]})})},He=()=>{const{user:r}=_(),w=ie(),[t,p]=g.useState(null);if(!r)return e.jsx(n,{className:"h-full flex items-center justify-center",children:e.jsxs(c,{className:"pt-6 text-center",children:[e.jsx("p",{className:"text-sm md:text-base",children:"Bitte melden Sie sich an, um Ihre Übersicht zu sehen."}),e.jsx(j,{onClick:()=>w("/login"),className:"mt-4 text-sm md:text-base",children:"Zum Login"})]})});const l=de(r),x=t||r.role,s=xe(r),v=m=>{switch(m){case"berater":return e.jsx(ze,{userId:r.id});case"mentor":return e.jsx(ye,{mentorId:r.id});case"teamleiter":return e.jsx(we,{teamId:r.teamId||""});case"gebietsmanager":return e.jsx(ve,{});case"admin":return e.jsx(Fe,{});default:return e.jsxs(n,{className:"h-full flex items-center justify-center",children:[e.jsx(i,{children:e.jsx(d,{className:"text-base md:text-lg",children:"Willkommen"})}),e.jsx(c,{children:e.jsx("p",{className:"text-sm md:text-base",children:"Für Ihre Rolle ist keine spezifische Übersicht verfügbar."})})]})}};return e.jsxs("div",{className:"space-y-4",children:[s&&e.jsx(n,{className:"glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:e.jsx(c,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Aktive Rolle wählen:"}),e.jsxs(ge,{value:x,onValueChange:m=>p(m),children:[e.jsx(je,{className:"w-full",children:e.jsx(ue,{placeholder:"Rolle auswählen"})}),e.jsx(be,{children:l.map(m=>e.jsx(pe,{value:m,children:W(m)},m))})]})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[e.jsx("p",{children:"Verfügbare Rollen:"}),e.jsx("p",{className:"font-medium",children:l.map(W).join(", ")})]})]})})}),v(x)]})},ms=()=>e.jsx(oe,{title:"Profil",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(He,{}),e.jsx(Re,{})]})});export{ms as default};
