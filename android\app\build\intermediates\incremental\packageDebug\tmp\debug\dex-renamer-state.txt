#Mon Jun 16 11:27:56 CEST 2025
base.0=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\2\\classes.dex
base.2=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\5\\classes.dex
base.3=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.4=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.5=C\:\\Users\\marti\\Desktop\\visit-flow-compass\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
path.0=classes.dex
path.1=2/classes.dex
path.2=5/classes.dex
path.3=8/classes.dex
path.4=0/classes.dex
path.5=15/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
