import{r as i,j as t,a as o}from"./index-Cmt5neWh.js";import{O as l,p as n,r as f,q as d,D as r,R as p,o as g,P as x}from"./MainLayout-wyzz138D.js";import{X as u}from"./input-BK13BBqa.js";const k=p,z=g,y=x,c=i.forwardRef(({className:a,...e},s)=>t.jsx(l,{ref:s,className:o("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...e}));c.displayName=l.displayName;const N=i.forwardRef(({className:a,children:e,...s},m)=>t.jsxs(y,{children:[t.jsx(c,{}),t.jsxs(n,{ref:m,className:o("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...s,children:[e,t.jsxs(f,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground min-h-[44px] min-w-[44px] flex items-center justify-center",children:[t.jsx(u,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));N.displayName=n.displayName;const j=({className:a,...e})=>t.jsx("div",{className:o("flex flex-col space-y-1.5 text-center sm:text-left",a),...e});j.displayName="DialogHeader";const D=({className:a,...e})=>t.jsx("div",{className:o("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...e});D.displayName="DialogFooter";const b=i.forwardRef(({className:a,...e},s)=>t.jsx(d,{ref:s,className:o("text-lg font-semibold leading-none tracking-tight",a),...e}));b.displayName=d.displayName;const h=i.forwardRef(({className:a,...e},s)=>t.jsx(r,{ref:s,className:o("text-sm text-muted-foreground",a),...e}));h.displayName=r.displayName;export{k as D,z as a,N as b,j as c,b as d,D as e};
