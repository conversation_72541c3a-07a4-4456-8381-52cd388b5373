import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  MapPin, 
  Clock, 
  TrendingUp, 
  Zap,
  Navigation,
  Target,
  ChevronLeft,
  ChevronRight,
  Star,
  Award,
  Flame
} from 'lucide-react';
import { useData } from '@/context/data';
import { useSwipeGestures, triggerHapticFeedback, validateTouchTarget } from '@/hooks/useSwipeGestures';
import { predictOptimalVisitWindows, analyzeDetailedTimePatterns } from '@/utils/advancedPatternAnalysis';
import { format, addHours, isToday } from 'date-fns';
import { de } from 'date-fns/locale';

interface SmartVisitAssistantProps {
  className?: string;
}

interface PersonalMetrics {
  todayVisits: number;
  successRate: number;
  streak: number;
  weeklyGoal: number;
  achievements: string[];
}

export const SmartVisitAssistant: React.FC<SmartVisitAssistantProps> = ({ className }) => {
  const { 
    getAddressesRequiringReturnVisits, 
    getHighPriorityRecommendations,
    visits,
    houses,
    addresses 
  } = useData();
  
  const [currentRecommendationIndex, setCurrentRecommendationIndex] = useState(0);
  const [personalMetrics, setPersonalMetrics] = useState<PersonalMetrics>({
    todayVisits: 0,
    successRate: 0,
    streak: 0,
    weeklyGoal: 25,
    achievements: []
  });
  const [isLoading, setIsLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const addressesRequiringVisits = getAddressesRequiringReturnVisits();
  const highPriorityRecommendations = getHighPriorityRecommendations();

  // Swipe gestures for navigation
  const { ref: swipeRef, isSwipeActive } = useSwipeGestures({
    onSwipeLeft: () => {
      if (currentRecommendationIndex < addressesRequiringVisits.length - 1) {
        setCurrentRecommendationIndex(prev => prev + 1);
        triggerHapticFeedback('light');
      }
    },
    onSwipeRight: () => {
      if (currentRecommendationIndex > 0) {
        setCurrentRecommendationIndex(prev => prev - 1);
        triggerHapticFeedback('light');
      }
    },
    threshold: 50
  });

  // Calculate personal metrics
  useEffect(() => {
    const today = new Date();
    const todayVisits = visits.filter(v => isToday(new Date(v.timestamp)));
    const successfulVisits = todayVisits.filter(v => v.status !== 'N/A');
    
    // Calculate streak (consecutive days with successful visits)
    let streak = 0;
    const checkDate = new Date(today);
    
    for (let i = 0; i < 30; i++) { // Check last 30 days
      const dayVisits = visits.filter(v => {
        const visitDate = new Date(v.timestamp);
        return visitDate.toDateString() === checkDate.toDateString();
      });
      
      const daySuccessful = dayVisits.some(v => v.status !== 'N/A');
      
      if (daySuccessful) {
        streak++;
        checkDate.setDate(checkDate.getDate() - 1);
      } else {
        break;
      }
    }

    // Generate achievements
    const achievements: string[] = [];
    if (streak >= 7) achievements.push('🔥 Wochenserie');
    if (successfulVisits.length >= 5) achievements.push('⭐ Tagesstar');
    if (todayVisits.length >= 10) achievements.push('💪 Fleißbiene');

    setPersonalMetrics({
      todayVisits: todayVisits.length,
      successRate: todayVisits.length > 0 ? (successfulVisits.length / todayVisits.length) * 100 : 0,
      streak,
      weeklyGoal: 25,
      achievements
    });
  }, [visits]);

  // Validate touch targets on mount
  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll('button');
      buttons.forEach(button => {
        if (!validateTouchTarget(button as HTMLElement)) {
          console.warn('Touch target too small:', button);
        }
      });
    }
  }, []);

  const currentRecommendation = addressesRequiringVisits[currentRecommendationIndex];
  
  // Generate predictive windows for current recommendation
  const predictiveWindows = currentRecommendation 
    ? predictOptimalVisitWindows(currentRecommendation.address.id, visits, houses)
    : [];

  const handleNavigateToAddress = async () => {
    if (!currentRecommendation) return;
    
    setIsLoading(true);
    triggerHapticFeedback('medium');
    
    try {
      const address = currentRecommendation.address;
      const query = `${address.street} ${address.zipCode} ${address.city}`;
      
      // Open in default maps app
      if ('geolocation' in navigator) {
        const url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(query)}`;
        window.open(url, '_blank');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      triggerHapticFeedback('error');
    } finally {
      setIsLoading(false);
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStreakIcon = (streak: number) => {
    if (streak >= 14) return <Award className="h-4 w-4 text-yellow-500" />;
    if (streak >= 7) return <Flame className="h-4 w-4 text-orange-500" />;
    if (streak >= 3) return <Star className="h-4 w-4 text-blue-500" />;
    return <Target className="h-4 w-4 text-gray-500" />;
  };

  if (addressesRequiringVisits.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Brain className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Perfekte Arbeit!</h3>
            <p className="text-muted-foreground mb-4">
              Alle Adressen wurden erfolgreich besucht. Keine Wiederholungsbesuche erforderlich.
            </p>
            <div className="flex justify-center gap-2">
              {personalMetrics.achievements.map((achievement, index) => (
                <Badge key={index} className="bg-green-100 text-green-800">
                  {achievement}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className} ref={containerRef}>
      {/* Personal Metrics Dashboard */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-blue-500" />
            Ihr persönlicher Assistent
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{personalMetrics.todayVisits}</div>
              <div className="text-sm text-muted-foreground">Besuche heute</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getSuccessRateColor(personalMetrics.successRate)}`}>
                {personalMetrics.successRate.toFixed(0)}%
              </div>
              <div className="text-sm text-muted-foreground">Erfolgsrate</div>
            </div>
          </div>
          
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {getStreakIcon(personalMetrics.streak)}
              <span className="text-sm font-medium">
                {personalMetrics.streak} Tage Serie
              </span>
            </div>
            <div className="text-sm text-muted-foreground">
              Wochenziel: {personalMetrics.todayVisits}/{personalMetrics.weeklyGoal}
            </div>
          </div>
          
          <Progress 
            value={(personalMetrics.todayVisits / personalMetrics.weeklyGoal) * 100} 
            className="h-2 mb-3"
          />
          
          {personalMetrics.achievements.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {personalMetrics.achievements.map((achievement, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {achievement}
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Smart Recommendations */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Zap className="h-5 w-5 text-yellow-500" />
              Intelligente Empfehlungen
            </CardTitle>
            <Badge variant="outline" className="bg-blue-50">
              {currentRecommendationIndex + 1} / {addressesRequiringVisits.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {currentRecommendation && (
            <div
              ref={swipeRef}
              className={`space-y-4 ${isSwipeActive ? 'transition-transform' : ''}`}
            >
              {/* Address Info */}
              <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                <MapPin className="h-5 w-5 text-blue-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-semibold">
                    {currentRecommendation.address.street}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {currentRecommendation.address.zipCode} {currentRecommendation.address.city}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="destructive" className="text-xs">
                      {currentRecommendation.failedVisitCount} fehlgeschlagen
                    </Badge>
                    <Badge className="bg-blue-500 text-xs">
                      {currentRecommendation.recommendations.length} Empfehlungen
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Top Recommendation */}
              {currentRecommendation.recommendations.length > 0 && (
                <div className="p-3 border border-green-200 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">Beste Empfehlung</span>
                    <Badge className="bg-green-600 text-xs">
                      {Math.round(currentRecommendation.recommendations[0].confidence * 100)}% Vertrauen
                    </Badge>
                  </div>
                  <p className="text-sm text-green-700">
                    {currentRecommendation.recommendations[0].recommendation}
                  </p>
                </div>
              )}

              {/* Predictive Windows */}
              {predictiveWindows.length > 0 && (
                <div className="space-y-2">
                  <h5 className="font-medium flex items-center gap-2">
                    <Clock className="h-4 w-4 text-purple-500" />
                    Optimale Zeitfenster heute
                  </h5>
                  {predictiveWindows.slice(0, 2).map((window, index) => (
                    <div key={index} className="p-2 bg-purple-50 rounded border border-purple-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {format(new Date(window.startTime), 'HH:mm', { locale: de })} - 
                          {format(new Date(window.endTime), 'HH:mm', { locale: de })}
                        </span>
                        <Badge className="bg-purple-600 text-xs">
                          {Math.round(window.probability * 100)}% Chance
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Navigation Controls */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentRecommendationIndex(prev => Math.max(0, prev - 1))}
                  disabled={currentRecommendationIndex === 0}
                  className="flex-1 h-12"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Zurück
                </Button>
                
                <Button
                  onClick={handleNavigateToAddress}
                  disabled={isLoading}
                  className="flex-1 h-12 bg-blue-600 hover:bg-blue-700"
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  {isLoading ? 'Öffne...' : 'Navigation'}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentRecommendationIndex(prev => 
                    Math.min(addressesRequiringVisits.length - 1, prev + 1)
                  )}
                  disabled={currentRecommendationIndex === addressesRequiringVisits.length - 1}
                  className="flex-1 h-12"
                >
                  Weiter
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>

              {/* Swipe Indicator */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground">
                  ← Wischen für weitere Empfehlungen →
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SmartVisitAssistant;
